#!/usr/bin/env python3
"""
測試 SOAP SSO 修復
"""

import os
import sys
import requests
import urllib3
from zeep import Client, Transport
from requests.adapters import HTTPAdapter

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_soap_connection():
    """測試 SOAP 連接"""
    print("🧪 測試 SOAP 客戶端修復")
    print("=" * 50)
    
    # SSO SOAP URL
    soap_ws_url = "http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx?WSDL"
    
    print(f"📡 連接到 SOAP 服務: {soap_ws_url}")
    
    try:
        # 確保使用HTTP協議
        soap_url = soap_ws_url.replace('https://', 'http://')
        print(f"🔄 調整後的URL: {soap_url}")
        
        # 建立HTTP會話
        session = requests.Session()
        session.verify = False
        session.mount('http://', HTTPAdapter())
        
        # 建立Transport和Client
        transport = Transport(session=session, timeout=10)
        client = Client(soap_url, transport=transport)
        
        print("✅ SOAP 客戶端建立成功！")
        
        # 測試服務呼叫
        print("\n🔑 測試 getUserProfile 服務...")
        test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
        
        result = client.service.getUserProfile(test_token)
        print(f"✅ 服務呼叫成功！")
        print(f"📝 回應類型: {type(result)}")
        print(f"📄 回應內容: {str(result)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ SOAP 測試失敗: {str(e)}")
        print(f"🔍 錯誤類型: {type(e).__name__}")
        return False

def test_direct_http():
    """測試直接 HTTP 請求"""
    print("\n🌐 測試直接 HTTP 請求...")
    
    try:
        # 測試 WSDL 端點
        response = requests.get("http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx?wsdl", timeout=5)
        print(f"✅ WSDL 端點回應正常 (狀態碼: {response.status_code})")
        
        # 測試服務端點
        response = requests.get("http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx", timeout=5)
        print(f"✅ 服務端點回應正常 (狀態碼: {response.status_code})")
        
        return True
        
    except Exception as e:
        print(f"❌ HTTP 測試失敗: {str(e)}")
        return False

def main():
    """主要測試函數"""
    print("🔧 SOAP SSO 修復測試")
    print("=" * 60)
    
    # 測試直接HTTP連接
    http_ok = test_direct_http()
    
    # 測試SOAP連接
    soap_ok = test_soap_connection()
    
    print("\n" + "=" * 60)
    if http_ok and soap_ok:
        print("🎉 所有測試通過！SOAP 修復成功。")
        sys.exit(0)
    else:
        print("❌ 測試失敗，請檢查配置。")
        sys.exit(1)

if __name__ == "__main__":
    main() 
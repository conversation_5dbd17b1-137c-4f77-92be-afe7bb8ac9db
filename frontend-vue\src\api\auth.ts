/**
 * 認證相關API
 */
import { http } from '@/utils/request'
import type { 
  LoginCredentials, 
  LoginResponse, 
  SSOLoginRequest, 
  User 
} from '@/types/auth'

export const authApi = {
  /**
   * 用戶登入
   */
  login(credentials: LoginCredentials) {
    return http.post<LoginResponse>('/api/v1/auth/login', credentials)
  },
  
  /**
   * SSO登入
   */
  ssoLogin(request: SSOLoginRequest) {
    return http.post<LoginResponse>('/api/v1/auth/sso-login', request)
  },
  
  /**
   * 用戶登出
   */
  logout() {
    return http.post('/api/v1/auth/logout')
  },
  
  /**
   * 刷新token
   */
  refreshToken() {
    return http.post<LoginResponse>('/api/v1/auth/refresh')
  },
  
  /**
   * 獲取當前用戶資訊
   */
  getCurrentUser() {
    return http.get<User>('/api/v1/auth/me')
  },
  
  /**
   * 更新用戶資料
   */
  updateProfile(data: Partial<User>) {
    return http.put<User>('/api/v1/auth/profile', data)
  },
  
  /**
   * 修改密碼
   */
  changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }) {
    return http.post('/api/v1/auth/change-password', data)
  },
  
  /**
   * 檢查用戶權限
   */
  checkPermission(permission: string) {
    return http.get<{ has_permission: boolean }>(`/api/v1/auth/check-permission/${permission}`)
  },
  
  /**
   * 獲取SSO登入URL
   */
  getSSOLoginUrl(redirectUrl?: string) {
    const params = redirectUrl ? { redirect_url: redirectUrl } : {}
    return http.get<{ login_url: string }>('/api/v1/auth/sso-url', params)
  }
}

"""
CBA受款人資料搜集系統 - Streamlit前端應用
"""

import streamlit as st
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
import pandas as pd
from typing import Dict, Any, Optional
import time
import pytz

# 導入自定義組件和服務
from utils.api_client import api_client
from utils.auth_manager import AuthManager
# 舊的組件已被新的 payee_management 取代

# 載入環境變數
load_dotenv()

# 設定頁面配置
st.set_page_config(
    page_title="CBA受款人資料搜集系統",
    page_icon="🔐",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 設定台北時區
taipei_tz = pytz.timezone('Asia/Taipei')

def main():
    """主應用程式入口"""
    # 初始化會話狀態
    AuthManager.init_session_state()
    
    # 檢查URL中是否有SSO回調token
    AuthManager.check_url_token()
    
    # 檢查系統健康狀態
    check_system_health()
    
    # 渲染主介面
    if AuthManager.check_authentication():
        render_authenticated_app()
    else:
        render_login_page()

def check_system_health():
    """檢查後端系統健康狀態"""
    try:
        health = api_client.health_check()
        if health.get("status") != "healthy":
            st.warning("⚠️ 後端系統連接異常，部分功能可能無法使用")
    except Exception:
        st.error("❌ 無法連接後端系統，請檢查網路連接或聯繫系統管理員")

def render_login_page():
    """渲染登入頁面"""
    st.title("🔐 CBA受款人資料搜集系統")
    st.write("請使用您的帳號登入系統")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.info("📢 本系統採用單一簽入(SSO)認證機制")
        
        # SSO登入按鈕
        if st.button("🔑 SSO登入", use_container_width=True, type="primary"):
            AuthManager.sso_login()
            
        st.divider()
        
        # 開發階段的帳號密碼登入
        with st.expander("🧪 開發階段登入", expanded=False):
            st.write("**僅供開發測試使用**")
            
            with st.form("login_form"):
                username = st.text_input("用戶名", placeholder="請輸入用戶名")
                password = st.text_input("密碼", type="password", placeholder="請輸入密碼")
                
                if st.form_submit_button("登入", use_container_width=True):
                    if username and password:
                        if AuthManager.login(username, password):
                            st.rerun()
                    else:
                        st.error("請輸入用戶名和密碼")

def render_authenticated_app():
    """渲染已認證用戶的主應用介面"""
    # 渲染側邊欄
    render_sidebar()
    
    # 根據當前頁面渲染內容
    # 一般用戶預設進入受款人管理，管理員和全域用戶預設進入資料概覽
    default_page = 'dashboard' if (AuthManager.is_admin() or AuthManager.has_role("global")) else 'create_data'
    current_page = st.session_state.get('current_page', default_page)

    if current_page == "dashboard":
        # 檢查權限
        if AuthManager.is_admin() or AuthManager.has_role("global"):
            render_dashboard()
        else:
            st.error("您沒有權限查看資料概覽")
            st.session_state.current_page = 'create_data'
            st.rerun()
    elif current_page == "create_data":
        from components.payee_management import render_payee_management_page
        render_payee_management_page()
    elif current_page == "statistics":
        render_statistics_page()
    elif current_page == "data_export":
        render_data_export_page()
    elif current_page == "user_management":
        render_user_management_page()
    elif current_page == "role_management":
        render_role_management_page()
    elif current_page == "department_management":
        render_department_management_page()
    elif current_page == "audit_log":
        render_audit_log_page()
    else:
        render_dashboard()

def render_sidebar():
    """渲染側邊欄導覽"""
    with st.sidebar:
        st.image("https://via.placeholder.com/200x80/0066CC/FFFFFF?text=CBA", width=200)
        st.title("受款人資料搜集系統")
        
        # 顯示用戶資訊
        user_info = AuthManager.get_current_user()
        if user_info:
            st.write(f"👤 歡迎，{user_info.get('full_name', '用戶')}")
            st.write(f"🏢 部門：{user_info.get('department', '未知')}")
            
            # 動態導覽選單
            st.divider()
            
            pages = AuthManager.get_navigation_pages()
            for page in pages:
                if st.button(page["title"], use_container_width=True, key=f"sidebar_{page['key']}"):
                    st.session_state.current_page = page["key"]
                    st.rerun()
            
            # 登出按鈕
            st.divider()
            if st.button("🚪 登出", use_container_width=True, key="sidebar_logout"):
                AuthManager.logout()

def render_dashboard():
    """渲染資料概覽頁面"""
    st.title("📊 CBA受款人資料系統")
    
    # 歡迎訊息
    user_info = AuthManager.get_current_user()
    st.markdown(f"""
    ### 👋 歡迎，{user_info.get('full_name', '用戶')}
    
    您現在正在使用 CBA 受款人資料搜集系統，這個系統可以幫助您管理和查詢受款人資料。
    """)
    
    # 快速操作卡片
    st.subheader("⚡ 快速操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div style="padding: 20px; border-radius: 10px; border: 1px solid #ddd; text-align: center; height: 180px;">
            <h3 style="color: #0066CC;">📝 新增受款人</h3>
            <p>建立新的受款人資料記錄</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("前往新增", key="goto_create", use_container_width=True):
            st.session_state.current_page = "create_data"
            st.rerun()
    
    with col2:
        st.markdown("""
        <div style="padding: 20px; border-radius: 10px; border: 1px solid #ddd; text-align: center; height: 180px;">
            <h3 style="color: #0066CC;">🔍 查詢受款人</h3>
            <p>搜尋和管理現有受款人資料</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("前往查詢", key="goto_query", use_container_width=True):
            st.session_state.current_page = "data_query"
            st.rerun()
    
    with col3:
        st.markdown("""
        <div style="padding: 20px; border-radius: 10px; border: 1px solid #ddd; text-align: center; height: 180px;">
            <h3 style="color: #0066CC;">📊 資料統計</h3>
            <p>查看系統資料統計和趨勢</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("查看統計", key="goto_stats", use_container_width=True):
            st.session_state.current_page = "statistics"
            st.rerun()
    
    # 只有管理員才能看到統計資料
    if AuthManager.is_admin():
        st.divider()
        try:
            # 取得統計資料
            stats_data = get_dashboard_statistics()
            
            # 顯示統計卡片
            st.subheader("📊 系統概況")
            
            # 使用更現代的卡片設計
            metric_cols = st.columns(4)
            
            metrics = [
                {"label": "總資料筆數", "value": stats_data.get("total_records", 0), "delta": stats_data.get("total_delta", None), "icon": "📊"},
                {"label": "本月新增", "value": stats_data.get("monthly_new", 0), "delta": stats_data.get("monthly_delta", None), "icon": "📅"},
                {"label": "本週新增", "value": stats_data.get("weekly_new", 0), "delta": stats_data.get("weekly_delta", None), "icon": "📆"},
                {"label": "今日新增", "value": stats_data.get("daily_new", 0), "delta": stats_data.get("daily_delta", None), "icon": "📈"}
            ]
            
            for i, metric in enumerate(metrics):
                with metric_cols[i]:
                    st.markdown(f"""
                    <div style="padding: 15px; border-radius: 10px; background-color: #f8f9fa; text-align: center;">
                        <div style="font-size: 24px;">{metric['icon']}</div>
                        <div style="font-size: 14px; color: #6c757d;">{metric['label']}</div>
                        <div style="font-size: 24px; font-weight: bold; color: #0066CC;">{metric['value']}</div>
                    </div>
                    """, unsafe_allow_html=True)
            
            st.divider()
            
            # 最近活動
            st.subheader("📋 最近活動")
            display_recent_activities()
            
        except Exception as e:
            st.error(f"載入統計資料失敗: {str(e)}")
    else:
        # 一般用戶只顯示基本資訊
        st.divider()
        st.info("💡 **使用提示**\n\n"
                "1. 使用左側選單導航到不同功能\n"
                "2. 點擊「新增受款人資料」來建立新的資料\n"
                "3. 使用「查詢受款人資料」功能來查看已建立的資料\n"
                "4. 如需協助，請聯繫系統管理員")

def display_recent_activities():
    """顯示最近活動"""
    try:
        # 確認用戶權限
        if not AuthManager.is_admin():
            return

        # 取得最近的審計日誌
        logs = api_client.get_audit_logs(
            table_name="payee_data",
            size=5
        )

        if not logs.get("items"):
            st.info("暫無活動記錄")
            return

        # 設定台北時區
        import pytz
        taipei_tz = pytz.timezone('Asia/Taipei')

        # 顯示活動列表
        for log in logs.get("items", []):
            action = log.get("action", "")
            action_text = {
                "CREATE": "新增",
                "UPDATE": "更新",
                "DELETE": "刪除",
                "create": "新增",
                "update": "更新",
                "delete": "刪除"
            }.get(action, action)

            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"👤 {log.get('username', '未知用戶')} {action_text}了一筆受款人資料")
            with col2:
                # 轉換時間為台北時區
                try:
                    timestamp_str = log.get('timestamp', '')
                    if timestamp_str:
                        # 處理不同的時間格式
                        if 'T' in timestamp_str and ('+' in timestamp_str or 'Z' in timestamp_str):
                            # ISO格式時間且包含時區資訊
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                        elif 'T' in timestamp_str:
                            # ISO格式時間但無時區資訊，假設為UTC
                            timestamp = datetime.fromisoformat(timestamp_str).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                        else:
                            # 一般格式時間，假設資料庫時間是UTC時間（無時區資訊）
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                        st.write(f"🕒 {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        st.write("🕒 時間未知")
                except Exception as time_error:
                    # 如果轉換失敗，嘗試直接顯示原始時間
                    st.write(f"🕒 {timestamp_str}")

    except Exception as e:
        if "403" not in str(e):  # 只有在不是權限問題時才顯示錯誤
            st.error(f"取得活動記錄失敗: {str(e)}")

def get_dashboard_statistics() -> Dict[str, Any]:
    """取得儀表板統計資料"""
    try:
        # 確認用戶權限
        if not AuthManager.is_admin():
            return {
                "total_records": 0,
                "monthly_new": 0,
                "weekly_new": 0,
                "daily_new": 0
            }

        # 取得統計概覽
        overview_response = api_client.get_statistics_overview()

        # 檢查API回應格式
        if not overview_response.get("success"):
            st.error(f"統計API錯誤: {overview_response.get('detail', '未知錯誤')}")
            return {
                "total_records": 0,
                "monthly_new": 0,
                "weekly_new": 0,
                "daily_new": 0
            }

        overview = overview_response.get("data", {})

        # 取得趨勢圖表資料
        charts_response = api_client.get_statistics_charts("monthly", 12)

        # 計算變化率
        monthly_delta = None
        if charts_response.get("success"):
            charts_data = charts_response.get("data", {})
            monthly_data = charts_data.get("results", [])
            if len(monthly_data) >= 2:
                current_month = monthly_data[-1]["count"]
                last_month = monthly_data[-2]["count"]
                monthly_delta = f"{((current_month - last_month) / last_month * 100):.1f}%" if last_month > 0 else None

        return {
            "total_records": overview.get("total_records", 0),
            "monthly_new": overview.get("monthly_new", 0),
            "weekly_new": overview.get("weekly_new", 0),
            "daily_new": overview.get("daily_new", 0),
            "total_delta": overview.get("total_delta", None),
            "monthly_delta": overview.get("monthly_delta", monthly_delta),
            "weekly_delta": overview.get("weekly_delta", None),
            "daily_delta": overview.get("daily_delta", None)
        }
    except Exception as e:
        if "403" not in str(e):  # 只有在不是權限問題時才顯示錯誤
            st.error(f"取得統計資料失敗: {str(e)}")
        return {
            "total_records": 0,
            "monthly_new": 0,
            "weekly_new": 0,
            "daily_new": 0
        }

def render_statistics_page():
    """渲染統計報表頁面"""
    AuthManager.require_role(["global", "admin"], "您需要全域用戶權限才能查看統計報表")
    
    st.title("📈 統計報表")
    
    try:
        # 取得統計概覽
        overview_data = api_client.get_statistics_overview()
        
        if overview_data.get("success"):
            data = overview_data.get("data", {})
            
            # 顯示概覽指標
            st.subheader("📊 資料概覽")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    label="總資料筆數",
                    value=data.get("total_records", 0)
                )
            
            with col2:
                monthly_delta = data.get("monthly_delta", 0)
                st.metric(
                    label="本月新增",
                    value=data.get("monthly_new", 0),
                    delta=monthly_delta if monthly_delta != 0 else None
                )
            
            with col3:
                st.metric(
                    label="本週新增",
                    value=data.get("weekly_new", 0)
                )
            
            with col4:
                st.metric(
                    label="今日新增",
                    value=data.get("daily_new", 0)
                )
            
            st.divider()
            
            # 圖表選項
            st.subheader("📈 趨勢圖表")
            
            tab1, tab2, tab3 = st.tabs(["月度趨勢", "週度趨勢", "部門分布"])
            
            with tab1:
                # 月度趨勢圖
                render_monthly_chart()
            
            with tab2:
                # 週度趨勢圖
                render_weekly_chart()
            
            with tab3:
                # 部門分布圖（僅管理者可見）
                user_info = AuthManager.get_current_user()
                if user_info and "admin" in user_info.get("roles", []):
                    render_department_chart()
                else:
                    st.info("👤 僅管理者可查看部門分布統計")
        
        else:
            st.error("無法取得統計資料")
    
    except Exception as e:
        st.error(f"載入統計報表時發生錯誤: {str(e)}")

def render_monthly_chart():
    """渲染月度趨勢圖"""
    try:
        # 期間選擇
        col1, col2 = st.columns([1, 3])
        with col1:
            period = st.selectbox("顯示期間", [6, 12, 18, 24], index=1, key="monthly_period")
        
        # 取得月度統計資料
        chart_data = api_client.get_statistics_charts("monthly", period)
        
        if chart_data.get("success"):
            results = chart_data.get("data", {}).get("results", [])
            
            if results:
                # 準備圖表資料
                import pandas as pd
                
                df = pd.DataFrame(results)
                df['label'] = df['label'].astype(str)
                
                # 使用Streamlit的線圖
                st.line_chart(
                    data=df.set_index('label')['count'],
                    height=400
                )
                
                # 顯示資料表格
                st.subheader("📋 詳細數據")
                display_df = df[['label', 'count']].copy()
                display_df.columns = ['月份', '新增筆數']
                st.dataframe(display_df, use_container_width=True, hide_index=True)
            else:
                st.info("暫無月度統計資料")
        else:
            st.error("無法取得月度統計資料")
    
    except Exception as e:
        st.error(f"載入月度圖表失敗: {str(e)}")

def render_weekly_chart():
    """渲染週度趨勢圖"""
    try:
        # 取得週度統計資料
        chart_data = api_client.get_statistics_charts("weekly", 8)
        
        if chart_data.get("success"):
            results = chart_data.get("data", {}).get("results", [])
            
            if results:
                import pandas as pd
                
                df = pd.DataFrame(results)
                
                # 使用條形圖顯示週度資料
                st.bar_chart(
                    data=df.set_index('label')['count'],
                    height=400
                )
                
                # 顯示資料表格
                st.subheader("📋 詳細數據")
                display_df = df[['label', 'count']].copy()
                display_df.columns = ['週期', '新增筆數']
                st.dataframe(display_df, use_container_width=True, hide_index=True)
            else:
                st.info("暫無週度統計資料")
        else:
            st.error("無法取得週度統計資料")
    
    except Exception as e:
        st.error(f"載入週度圖表失敗: {str(e)}")

def render_department_chart():
    """渲染部門分布圖"""
    try:
        # 取得部門統計資料
        chart_data = api_client.get_statistics_charts("department")
        
        if chart_data.get("success"):
            results = chart_data.get("data", {}).get("results", [])
            
            if results:
                import pandas as pd
                
                df = pd.DataFrame(results)
                
                # 使用餅圖顯示部門分布
                st.subheader("🏢 部門資料分布")
                
                # 由於Streamlit沒有原生餅圖，用條形圖代替
                st.bar_chart(
                    data=df.set_index('department')['count'],
                    height=400
                )
                
                # 顯示資料表格
                st.subheader("📋 詳細數據")
                display_df = df.copy()
                display_df.columns = ['部門', '資料筆數']
                
                # 計算百分比
                total = display_df['資料筆數'].sum()
                if total > 0:
                    display_df['佔比'] = (display_df['資料筆數'] / total * 100).round(2).astype(str) + '%'
                
                st.dataframe(display_df, use_container_width=True, hide_index=True)
            else:
                st.info("暫無部門統計資料")
        else:
            st.error("無法取得部門統計資料")
    
    except Exception as e:
        st.error(f"載入部門圖表失敗: {str(e)}")

def render_data_export_page():
    """渲染資料匯出頁面"""
    AuthManager.require_role(["global", "admin"], "您需要全域用戶權限才能匯出資料")

    st.title("💾 資料匯出")
    st.write("選擇匯出條件和格式，將受款人資料匯出為檔案")

    # 初始化會話狀態
    if 'export_data_ready' not in st.session_state:
        st.session_state.export_data_ready = False
    if 'export_file_data' not in st.session_state:
        st.session_state.export_file_data = None
    if 'export_filename' not in st.session_state:
        st.session_state.export_filename = None
    if 'export_mime_type' not in st.session_state:
        st.session_state.export_mime_type = None
    if 'export_total_count' not in st.session_state:
        st.session_state.export_total_count = 0
    if 'export_format_type' not in st.session_state:
        st.session_state.export_format_type = "CSV"

    # 匯出條件設定
    with st.form("export_form"):
        st.subheader("📋 匯出條件")

        col1, col2 = st.columns(2)
        with col1:
            search_term = st.text_input("搜尋關鍵字", help="可搜尋姓名、地址、備註")
            name_filter = st.text_input("姓名篩選")

        with col2:
            address_filter = st.text_input("地址篩選")
            department_filter = st.text_input("部門篩選")

        st.subheader("📄 匯出格式")
        export_format = st.selectbox(
            "檔案格式",
            options=["CSV", "Excel"],
            index=0,
            help="CSV格式適合大量資料，Excel格式便於閱讀"
        )

        include_sensitive = st.checkbox(
            "包含敏感資料",
            value=False,
            help="是否在匯出檔案中包含完整身分證字號（預設為遮蔽顯示）"
        )

        submitted = st.form_submit_button("🚀 開始匯出", use_container_width=True, type="primary")

        if submitted:
            try:
                with st.spinner("正在準備匯出資料..."):
                    # 查詢資料
                    query_params = {
                        "search": search_term if search_term else None,
                        "name": name_filter if name_filter else None,
                        "address": address_filter if address_filter else None,
                        "department": department_filter if department_filter else None,
                        "page": 1,
                        "size": 10000  # 大量匯出
                    }

                    result = api_client.get_payee_data_list(**query_params)
                    data = result.get("items", [])
                    total_count = result.get("total", 0)

                    if not data:
                        st.warning("沒有符合條件的資料可供匯出")
                        st.session_state.export_data_ready = False
                        st.rerun()
                        return

                    st.success(f"✅ 找到 {total_count} 筆資料，準備匯出...")

                    # 準備匯出資料
                    export_data = []
                    import pytz
                    taipei_tz = pytz.timezone('Asia/Taipei')

                    for item in data:
                        # 轉換時間為台北時區
                        created_at_str = item.get("created_at", "")
                        updated_at_str = item.get("updated_at", "")

                        formatted_created_at = ""
                        formatted_updated_at = ""

                        if created_at_str:
                            try:
                                if 'T' in created_at_str and ('+' in created_at_str or 'Z' in created_at_str):
                                    # ISO格式時間且包含時區資訊
                                    created_timestamp = datetime.fromisoformat(created_at_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                                elif 'T' in created_at_str:
                                    # ISO格式時間但無時區資訊，假設為UTC
                                    created_timestamp = datetime.fromisoformat(created_at_str).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                                else:
                                    # 一般格式時間，假設為UTC
                                    created_timestamp = datetime.strptime(created_at_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                                formatted_created_at = created_timestamp.strftime('%Y-%m-%d %H:%M:%S')
                            except Exception:
                                formatted_created_at = created_at_str

                        if updated_at_str:
                            try:
                                if 'T' in updated_at_str and ('+' in updated_at_str or 'Z' in updated_at_str):
                                    # ISO格式時間且包含時區資訊
                                    updated_timestamp = datetime.fromisoformat(updated_at_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                                elif 'T' in updated_at_str:
                                    # ISO格式時間但無時區資訊，假設為UTC
                                    updated_timestamp = datetime.fromisoformat(updated_at_str).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                                else:
                                    # 一般格式時間，假設為UTC
                                    updated_timestamp = datetime.strptime(updated_at_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                                formatted_updated_at = updated_timestamp.strftime('%Y-%m-%d %H:%M:%S')
                            except Exception:
                                formatted_updated_at = updated_at_str

                        row = {
                            "編號": item.get("id", ""),
                            "姓名": item.get("name", ""),
                            "身分證字號": item.get("id_number", "") if include_sensitive else item.get("id_number_masked", ""),
                            "地址": item.get("address", ""),
                            "備註": item.get("notes", ""),
                            "建立者": item.get("created_by", ""),
                            "建立部門": item.get("created_by_department", ""),
                            "建立時間": formatted_created_at,
                            "更新時間": formatted_updated_at
                        }
                        export_data.append(row)

                    # 生成檔案
                    import pandas as pd
                    from datetime import datetime

                    df = pd.DataFrame(export_data)
                    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")

                    if export_format == "CSV":
                        csv_data = df.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
                        filename = f"受款人資料匯出_{current_time}.csv"
                        mime_type = "text/csv"
                        file_data = csv_data
                    else:  # Excel
                        import io
                        excel_buffer = io.BytesIO()
                        with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
                            df.to_excel(writer, sheet_name='受款人資料', index=False)
                        excel_buffer.seek(0)
                        filename = f"受款人資料匯出_{current_time}.xlsx"
                        mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        file_data = excel_buffer.getvalue()

                    # 儲存到會話狀態
                    st.session_state.export_data_ready = True
                    st.session_state.export_file_data = file_data
                    st.session_state.export_filename = filename
                    st.session_state.export_mime_type = mime_type
                    st.session_state.export_total_count = total_count
                    st.session_state.export_format_type = export_format

                    # 記錄匯出操作
                    st.info(f"📊 匯出完成：{total_count} 筆資料，檔案格式：{export_format}")
                    st.rerun()

            except Exception as e:
                st.error(f"❌ 匯出失敗：{str(e)}")
                st.session_state.export_data_ready = False

    # 下載按鈕區域（表單外部）
    if st.session_state.export_data_ready and st.session_state.export_file_data:
        st.divider()
        st.subheader("📥 下載檔案")

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            download_clicked = st.download_button(
                label=f"📥 下載 {st.session_state.export_format_type} 檔案 ({st.session_state.export_total_count} 筆)",
                data=st.session_state.export_file_data,
                file_name=st.session_state.export_filename,
                mime=st.session_state.export_mime_type,
                use_container_width=True,
                type="primary"
            )

            if download_clicked:
                st.success("✅ 檔案下載已開始！")
                # 清除會話狀態
                st.session_state.export_data_ready = False
                st.session_state.export_file_data = None
                st.session_state.export_filename = None
                st.session_state.export_mime_type = None
                st.session_state.export_total_count = 0
                st.session_state.export_format_type = "CSV"

        # 重新匯出按鈕
        if st.button("🔄 重新匯出", use_container_width=True):
            st.session_state.export_data_ready = False
            st.session_state.export_file_data = None
            st.session_state.export_filename = None
            st.session_state.export_mime_type = None
            st.session_state.export_total_count = 0
            st.session_state.export_format_type = "CSV"
            st.rerun()

    # 匯出說明
    with st.expander("ℹ️ 匯出說明", expanded=False):
        st.markdown("""
        **匯出功能說明：**
        
        • **搜尋條件**：可組合多個條件篩選資料
        • **檔案格式**：
          - CSV：適合大量資料處理，可用Excel或其他軟體開啟
          - Excel：直接可用Microsoft Excel開啟，格式較美觀
        • **敏感資料**：
          - 預設身分證字號會遮蔽顯示（如：A12******9）
          - 勾選「包含敏感資料」可匯出完整身分證字號
        • **資料安全**：
          - 匯出操作會記錄在審計日誌中
          - 請妥善保管匯出的檔案
          - 遵守個資法相關規定
        
        **注意事項：**
        - 一次最多匯出10,000筆資料
        - 大量資料匯出可能需要較長時間
        - 匯出檔案僅包含您有權限查看的資料
        """)

def render_user_management_page():
    """
    用戶管理頁面：顯示用戶列表，支援角色分配與啟用/停用
    """
    AuthManager.require_role(["admin"], "您需要管理者權限才能管理用戶")
    st.title("👥 用戶管理")

    # 取得角色清單（物件陣列）
    roles = api_client.get_roles()
    role_id2name = {r['id']: r['name'] for r in roles}
    role_name2id = {r['name']: r['id'] for r in roles}
    role_names = [r['name'] for r in roles]
    # 取得部門清單（物件陣列）
    departments = api_client.get_departments()
    dept_id2name = {d['id']: d['name'] for d in departments}
    dept_name2id = {d['name']: d['id'] for d in departments}
    dept_names = [d['name'] for d in departments]

    # 查詢條件
    col1, col2, col3 = st.columns(3)
    with col1:
        role_filter_name = st.selectbox("角色篩選", options=["全部"] + role_names, index=0)
    with col2:
        status_filter = st.selectbox("狀態篩選", options=["全部", "啟用", "停用"], index=0)
    with col3:
        page = st.number_input("頁碼", min_value=1, value=1, step=1)

    # 查詢用戶
    role_param = None if role_filter_name == "全部" else role_filter_name
    status_param = None
    if status_filter == "啟用":
        status_param = True
    elif status_filter == "停用":
        status_param = False
    users = api_client.get_users(page=page, size=20, role=role_param, status=status_param)

    # 顯示用戶表格
    if not users:
        st.info("查無用戶資料")
        return
    st.write(f"共 {len(users)} 筆用戶")
    for user in users:
        with st.expander(f"{user['username']} ({user['full_name']})", expanded=False):
            col1, col2, col3 = st.columns([2,2,2])
            with col1:
                st.write(f"用戶ID: {user['id']}")
                # 用戶名稱修改
                current_name = user['full_name']
                new_name = st.text_input(
                    "用戶名稱",
                    value=current_name,
                    key=f"name_{user['id']}"
                )
                if new_name != current_name:
                    if st.button("更新名稱", key=f"update_name_{user['id']}"):
                        try:
                            api_client.update_user_name(user['id'], new_name)
                            st.success("用戶名稱已更新")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"更新名稱失敗: {str(e)}")
                # 部門修改
                current_dept = user.get('department', '')
                new_dept = st.selectbox(
                    "部門",
                    options=dept_names,
                    index=dept_names.index(current_dept) if current_dept in dept_names else 0,
                    key=f"dept_{user['id']}"
                )
                if new_dept != current_dept:
                    if st.button("更新部門", key=f"update_department_{user['id']}"):
                        try:
                            api_client.update_user_department(user['id'], dept_name2id[new_dept])
                            st.success("部門已更新")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"更新部門失敗: {str(e)}")
            with col2:
                # 角色分配
                current_role_name = user['roles'][0] if user['roles'] else ""
                new_role_name = st.selectbox(
                    "角色",
                    options=role_names,
                    index=role_names.index(current_role_name) if current_role_name in role_names else 0,
                    key=f"role_{user['id']}"
                )
                if new_role_name != current_role_name:
                    if st.button("更新角色", key=f"update_role_{user['id']}"):
                        try:
                            role_id = role_name2id[new_role_name]
                            api_client.update_user_role(user['id'], role_id)
                            st.success("角色已更新")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"角色更新失敗: {str(e)}")
            with col3:
                # 啟用/停用
                is_active = user['is_active']
                new_status = st.checkbox("啟用", value=is_active, key=f"status_{user['id']}")
                if new_status != is_active:
                    if st.button("更新狀態", key=f"update_status_{user['id']}"):
                        try:
                            api_client.update_user_status(user['id'], new_status)
                            st.success("狀態已更新")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"狀態更新失敗: {str(e)}")

def render_role_management_page():
    """
    角色權限管理頁面：顯示所有角色及其權限，支援權限調整、角色新增、名稱修改、刪除，並以id為主操作
    """
    AuthManager.require_role(["admin"], "您需要管理者權限才能管理角色權限")
    st.title("🔒 角色權限管理")

    # 取得角色與權限（皆為物件陣列）
    roles = api_client.get_roles()
    permissions = api_client.get_permissions()
    perm_id2name = {p['id']: p['name'] for p in permissions}
    perm_name2id = {p['name']: p['id'] for p in permissions}
    role_id2obj = {r['id']: r for r in roles}

    # 角色新增
    with st.expander("➕ 新增角色", expanded=False):
        with st.form("create_role_form"):
            new_role_name = st.text_input("角色名稱", max_chars=50)
            new_role_desc = st.text_input("角色描述", max_chars=100)
            if st.form_submit_button("新增角色"):
                if not new_role_name.strip():
                    st.error("角色名稱不可為空")
                else:
                    try:
                        api_client.create_role(new_role_name.strip(), new_role_desc.strip())
                        st.success("角色新增成功")
                        time.sleep(1)
                        st.rerun()
                    except Exception as e:
                        st.error(f"角色新增失敗: {str(e)}")

    st.subheader("所有角色")
    if not roles:
        st.info("查無角色資料")
        return
    for role in roles:
        role_id = role['id']
        with st.expander(f"角色：{role['name']}", expanded=False):
            st.write(f"角色名稱：{role['name']}")
            st.write(f"描述：{role.get('description','')}")
            # 查詢現有權限
            current_perms = api_client.get_role_permissions(role_id)
            if isinstance(current_perms, dict) and 'detail' in current_perms:
                st.error(f"查詢權限失敗: {current_perms['detail']}")
                continue
            # 取得現有權限id清單
            current_perm_ids = [perm_name2id[p] for p in current_perms if p in perm_name2id]
            perm_options = {f"{p['name']}（{p.get('description','')}）": p['id'] for p in permissions}
            selected_perm_ids = st.multiselect(
                "權限清單（可多選調整）",
                options=list(perm_options.keys()),
                default=[k for k,v in perm_options.items() if v in current_perm_ids],
                key=f"perms_{role_id}"
            )
            selected_perm_id_list = [perm_options[k] for k in selected_perm_ids]
            if set(selected_perm_id_list) != set(current_perm_ids):
                if st.button("更新權限", key=f"update_perms_{role_id}"):
                    try:
                        api_client.update_role_permissions(role_id, selected_perm_id_list)
                        st.success("權限已更新")
                        time.sleep(1)
                        st.rerun()
                    except Exception as e:
                        st.error(f"權限更新失敗: {str(e)}")
            # 角色名稱修改
            with st.form(f"edit_role_form_{role_id}"):
                new_name = st.text_input("修改角色名稱", value=role['name'], key=f"edit_name_{role_id}")
                new_desc = st.text_input("修改描述", value=role.get('description',''), key=f"edit_desc_{role_id}")
                if st.form_submit_button("儲存名稱修改"):
                    if not new_name.strip():
                        st.error("角色名稱不可為空")
                    else:
                        try:
                            api_client.update_role_name(role_id, new_name.strip(), new_desc.strip())
                            st.success("角色名稱已修改")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"名稱修改失敗: {str(e)}")
            # 角色刪除
            if st.button("刪除角色", key=f"delete_role_{role_id}"):
                st.warning(f"⚠️ 刪除角色將影響所有已分配該角色的用戶，且不可復原。請再次確認！")
                if st.button(f"確認刪除角色 {role['name']}", key=f"confirm_delete_{role_id}"):
                    try:
                        api_client.delete_role(role_id)
                        st.success("角色已刪除")
                        time.sleep(1)
                        st.rerun()
                    except Exception as e:
                        st.error(f"刪除失敗: {str(e)}")

def render_department_management_page():
    """
    部門管理頁面：顯示部門列表，支援新增、修改、刪除
    """
    AuthManager.require_role(["admin"], "您需要管理者權限才能管理部門")
    st.title("🏢 部門管理")
    try:
        departments = api_client.get_departments()
        if not departments:
            st.info("查無部門資料")
        with st.expander("➕ 新增部門", expanded=False):
            with st.form("create_dept_form"):
                code = st.text_input("部門代碼", max_chars=50)
                name = st.text_input("部門名稱", max_chars=100)
                desc = st.text_area("描述", max_chars=200)
                if st.form_submit_button("新增部門"):
                    if not code.strip() or not name.strip():
                        st.error("部門代碼與名稱不可為空")
                    else:
                        api_client.create_department(code.strip(), name.strip(), desc.strip())
                        st.success("部門新增成功")
                        time.sleep(1)
                        st.rerun()
        st.subheader("所有部門")
        for dept in departments:
            with st.expander(f"部門：{dept['name']}", expanded=False):
                col1, col2 = st.columns([2,2])
                with col1:
                    st.write(f"部門 ID：{dept['id']}")
                    st.write(f"部門代碼：{dept.get('code','')}")
                    st.write(f"名稱：{dept['name']}")
                    st.write(f"描述：{dept.get('description','')}")
                    st.write(f"狀態：{'啟用' if dept['is_active'] else '停用'}")
                with col2:
                    new_code = st.text_input("修改代碼", value=dept.get('code',''), key=f"edit_code_{dept['id']}")
                    new_name = st.text_input("修改名稱", value=dept['name'], key=f"edit_name_{dept['id']}")
                    new_desc = st.text_area("修改描述", value=dept.get('description',''), key=f"edit_desc_{dept['id']}")
                    new_active = st.checkbox("啟用", value=dept['is_active'], key=f"edit_active_{dept['id']}")
                    if st.button("儲存修改", key=f"update_dept_{dept['id']}"):
                        try:
                            api_client.update_department(dept['id'], new_code.strip(), new_name.strip(), new_desc.strip(), new_active)
                            st.success("部門已更新")
                            time.sleep(1)
                            st.rerun()
                        except Exception as e:
                            st.error(f"更新部門失敗: {str(e)}")
                    if st.button("刪除部門", key=f"delete_dept_{dept['id']}"):
                        st.warning(f"⚠️ 刪除部門將影響相關資料，請再次確認！")
                        if st.button(f"確認刪除部門 {dept['name']}", key=f"confirm_delete_{dept['id']}"):
                            try:
                                api_client.delete_department(dept['id'])
                                st.success("部門已刪除")
                                time.sleep(1)
                                st.rerun()
                            except Exception as e:
                                st.error(f"刪除部門失敗: {str(e)}")
    except Exception as e:
        st.error(f"載入部門資料失敗: {str(e)}")

def render_audit_log_page():
    """渲染審計日誌頁面"""
    AuthManager.require_role(["admin"], "您需要管理者權限才能查看審計日誌")
    
    st.title("📋 審計日誌")
    
    try:
        # 篩選條件
        col1, col2, col3 = st.columns(3)
        
        with col1:
            table_filter = st.selectbox(
                "資料表",
                ["全部", "payee_data", "users", "audit_log"],
                index=0
            )
        
        with col2:
            action_filter = st.selectbox(
                "操作類型",
                ["全部", "CREATE", "UPDATE", "DELETE", "LOGIN", "LOGOUT"],
                index=0
            )
        
        with col3:
            page_size = st.selectbox("每頁筆數", [10, 20, 50, 100], index=1)
        
        # 取得審計日誌
        table_name = None if table_filter == "全部" else table_filter
        action = None if action_filter == "全部" else action_filter
        
        audit_result = api_client.get_audit_logs(
            table_name=table_name,
            action=action,
            page=1,
            size=page_size
        )
        
        logs = audit_result.get("items", [])
        total = audit_result.get("total", 0)
        
        st.write(f"共 {total} 筆記錄")
        
        if logs:
            # 設定台北時區
            import pytz
            taipei_tz = pytz.timezone('Asia/Taipei')

            # 轉換為DataFrame顯示
            df_data = []
            for log in logs:
                # 轉換時間為台北時區
                timestamp_str = log.get("timestamp", "")
                formatted_time = ""
                if timestamp_str:
                    try:
                        if 'T' in timestamp_str and ('+' in timestamp_str or 'Z' in timestamp_str):
                            # ISO格式時間且包含時區資訊
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                        elif 'T' in timestamp_str:
                            # ISO格式時間但無時區資訊，假設為UTC
                            timestamp = datetime.fromisoformat(timestamp_str).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                        else:
                            # 一般格式時間，假設資料庫時間是UTC時間（無時區資訊）
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    except Exception as e:
                        # 如果轉換失敗，顯示原始時間並記錄錯誤
                        formatted_time = f"{timestamp_str} (轉換失敗)"

                df_data.append({
                    "時間": formatted_time,
                    "用戶": log.get("username", "系統"),
                    "操作": log.get("action", ""),
                    "資料表": log.get("table_name", ""),
                    "記錄ID": log.get("record_id", ""),
                    "IP位址": log.get("ip_address", "")
                })

            df = pd.DataFrame(df_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("沒有找到審計日誌記錄")
            
    except Exception as e:
        st.error(f"載入審計日誌失敗: {str(e)}")

if __name__ == "__main__":
    main() 

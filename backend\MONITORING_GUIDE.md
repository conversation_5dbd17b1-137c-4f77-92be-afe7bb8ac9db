# CBA人員資料調查系統 - 監控配置指南

## 📋 **目錄**
- [監控概述](#監控概述)
- [健康檢查API](#健康檢查api)
- [監控腳本設定](#監控腳本設定)
- [告警配置](#告警配置)
- [監控儀表板](#監控儀表板)
- [故障排除](#故障排除)
- [最佳實務](#最佳實務)

---

## 🔍 **監控概述**

本系統提供完整的監控解決方案，包含：

### 核心監控功能
- **系統資源監控**: CPU、記憶體、磁碟使用率
- **服務健康檢查**: 後端API、前端介面可用性
- **資料庫監控**: 連線狀態、檔案大小、回應時間
- **日誌分析**: 錯誤日誌監控和分析
- **配置檢查**: 安全設定和配置驗證

### 告警機制
- **即時告警**: 閾值超標時立即通知
- **郵件通知**: 支援SMTP郵件告警
- **HTML報告**: 詳細的監控報告生成
- **歷史記錄**: 監控數據和告警歷史

---

## 🏥 **健康檢查API**

### 基本健康檢查
```bash
# 基本狀態檢查
curl http://localhost:8000/api/v1/health/

# 回應範例
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00",
  "version": "1.0.0",
  "environment": "production"
}
```

### 詳細健康檢查
```bash
# 詳細系統檢查
curl http://localhost:8000/api/v1/health/detailed

# 回應範例
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00",
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": {
      "status": "healthy",
      "connection": "ok",
      "test_query": "passed"
    },
    "system_resources": {
      "status": "healthy",
      "cpu_percent": 25.3,
      "memory_percent": 45.7,
      "disk_percent": 60.2,
      "available_memory_gb": 4.2
    },
    "configuration": {
      "status": "healthy",
      "issues": []
    },
    "filesystem": {
      "status": "healthy",
      "issues": []
    }
  }
}
```

### Kubernetes探針
```bash
# Liveness Probe (存活檢查)
curl http://localhost:8000/api/v1/health/liveness

# Readiness Probe (就緒檢查)
curl http://localhost:8000/api/v1/health/readiness
```

---

## 📊 **監控腳本設定**

### 1. 安裝依賴
```bash
# 安裝監控腳本所需的Python套件
pip install psutil requests

# 或在虛擬環境中安裝
/opt/cba-system/.venv/bin/pip install psutil requests
```

### 2. 監控配置檔案

建立 `/opt/cba-system/monitor_config.json`：

```json
{
  "thresholds": {
    "cpu_usage": 80.0,
    "memory_usage": 85.0,
    "disk_usage": 90.0,
    "response_time": 5.0
  },
  "services": {
    "backend": {
      "url": "http://127.0.0.1:8000/api/v1/health",
      "timeout": 10
    },
    "frontend": {
      "url": "http://127.0.0.1:8501",
      "timeout": 10
    }
  },
  "email": {
    "enabled": true,
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "recipients": [
      "<EMAIL>",
      "<EMAIL>"
    ]
  },
  "database": {
    "path": "/opt/cba-system/database/cba_personal_data.db"
  },
  "log_paths": [
    "/var/log/nginx/cba-system.error.log",
    "/opt/cba-system/logs/app.log"
  ]
}
```

### 3. 執行監控腳本
```bash
# 手動執行監控
cd /opt/cba-system
python3 scripts/monitor.py

# 輸出JSON格式結果
python3 scripts/monitor.py --json

# 檢查退出碼
echo $?  # 0=正常, 1=有告警
```

### 4. 設定定時監控

編輯crontab：
```bash
sudo crontab -e

# 添加以下行（每5分鐘執行一次監控）
*/5 * * * * cd /opt/cba-system && /opt/cba-system/.venv/bin/python scripts/monitor.py

# 每小時執行詳細檢查並生成報告
0 * * * * cd /opt/cba-system && /opt/cba-system/.venv/bin/python scripts/monitor.py --detailed

# 每日早上9點發送系統狀態報告
0 9 * * * cd /opt/cba-system && /opt/cba-system/.venv/bin/python scripts/monitor.py --daily-report
```

---

## 📧 **告警配置**

### 1. 郵件告警設定

#### Gmail設定範例
```json
{
  "email": {
    "enabled": true,
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "recipients": ["<EMAIL>"]
  }
}
```

#### 取得Gmail應用程式密碼
1. 開啟 [Google帳戶設定](https://myaccount.google.com/)
2. 選擇「安全性」→「兩步驟驗證」
3. 在「應用程式密碼」中生成新密碼
4. 將生成的密碼填入配置檔案

#### 其他郵件服務商設定
```json
{
  "email": {
    "enabled": true,
    // Outlook/Hotmail
    "smtp_server": "smtp-mail.outlook.com",
    "smtp_port": 587,
    
    // Yahoo Mail
    "smtp_server": "smtp.mail.yahoo.com",
    "smtp_port": 587,
    
    // 自建郵件伺服器
    "smtp_server": "mail.your-domain.com",
    "smtp_port": 25
  }
}
```

### 2. 告警閾值設定

```json
{
  "thresholds": {
    "cpu_usage": 80.0,        // CPU使用率告警閾值 (%)
    "memory_usage": 85.0,     // 記憶體使用率告警閾值 (%)
    "disk_usage": 90.0,       // 磁碟使用率告警閾值 (%)
    "response_time": 5.0,     // 服務回應時間告警閾值 (秒)
    "error_count": 10,        // 日誌錯誤數量告警閾值
    "db_size_mb": 1000        // 資料庫大小告警閾值 (MB)
  }
}
```

### 3. 告警抑制設定

```json
{
  "alert_suppression": {
    "enabled": true,
    "cooldown_minutes": 30,   // 同類型告警冷卻時間
    "max_alerts_per_hour": 5  // 每小時最大告警數量
  }
}
```

---

## 📈 **監控儀表板**

### 1. 使用Grafana (推薦)

#### 安裝Grafana
```bash
# Ubuntu/Debian
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
sudo apt-get update
sudo apt-get install grafana

# 啟動Grafana
sudo systemctl enable grafana-server
sudo systemctl start grafana-server
```

#### 配置數據源
1. 瀏覽 http://localhost:3000 (admin/admin)
2. 添加Prometheus數據源
3. 導入CBA系統監控儀表板

#### 監控面板配置
```json
{
  "dashboard": {
    "title": "CBA系統監控",
    "panels": [
      {
        "title": "系統資源",
        "type": "graph",
        "targets": [
          "cpu_usage", "memory_usage", "disk_usage"
        ]
      },
      {
        "title": "服務狀態",
        "type": "stat",
        "targets": [
          "service_health_backend", "service_health_frontend"
        ]
      },
      {
        "title": "API回應時間",
        "type": "graph",
        "targets": [
          "api_response_time"
        ]
      }
    ]
  }
}
```

### 2. 簡易Web儀表板

建立 `/opt/cba-system/dashboard/index.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <title>CBA系統監控儀表板</title>
    <meta charset="UTF-8">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CBA人員資料調查系統監控</h1>
        
        <div class="grid">
            <div class="card">
                <h3>系統狀態</h3>
                <div id="system-status">載入中...</div>
            </div>
            
            <div class="card">
                <h3>資源使用率</h3>
                <canvas id="resource-chart"></canvas>
            </div>
            
            <div class="card">
                <h3>服務健康狀態</h3>
                <div id="service-status">載入中...</div>
            </div>
        </div>
    </div>

    <script>
        // 載入監控數據
        async function loadMonitoringData() {
            try {
                const response = await fetch('/api/v1/health/detailed');
                const data = await response.json();
                
                updateSystemStatus(data);
                updateResourceChart(data);
                updateServiceStatus(data);
            } catch (error) {
                console.error('載入監控數據失敗:', error);
            }
        }

        function updateSystemStatus(data) {
            const statusElement = document.getElementById('system-status');
            const statusClass = `status-${data.status === 'healthy' ? 'healthy' : 
                                 data.status === 'warning' ? 'warning' : 'critical'}`;
            
            statusElement.innerHTML = `
                <p class="${statusClass}">狀態: ${data.status}</p>
                <p>版本: ${data.version}</p>
                <p>環境: ${data.environment}</p>
                <p>檢查時間: ${new Date(data.timestamp).toLocaleString()}</p>
            `;
        }

        function updateResourceChart(data) {
            if (!data.checks.system_resources) return;
            
            const ctx = document.getElementById('resource-chart').getContext('2d');
            const resources = data.checks.system_resources;
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['CPU', '記憶體', '磁碟'],
                    datasets: [{
                        data: [
                            resources.cpu_percent,
                            resources.memory_percent,
                            resources.disk_percent
                        ],
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        function updateServiceStatus(data) {
            // 實作服務狀態更新邏輯
        }

        // 每30秒更新一次數據
        loadMonitoringData();
        setInterval(loadMonitoringData, 30000);
    </script>
</body>
</html>
```

---

## 🔧 **故障排除**

### 常見問題

#### 1. 監控腳本無法執行
```bash
# 檢查Python環境
which python3
python3 --version

# 檢查腳本權限
ls -la /opt/cba-system/scripts/monitor.py
chmod +x /opt/cba-system/scripts/monitor.py

# 檢查依賴安裝
pip list | grep -E "psutil|requests"
```

#### 2. 郵件告警無法發送
```bash
# 測試SMTP連線
python3 -c "
import smtplib
server = smtplib.SMTP('smtp.gmail.com', 587)
server.starttls()
print('SMTP連線成功')
"

# 檢查憑證設定
echo "檢查郵件配置..."
grep -A 10 '"email"' /opt/cba-system/monitor_config.json
```

#### 3. 健康檢查API無回應
```bash
# 檢查後端服務狀態
sudo systemctl status cba-backend

# 檢查端口是否開啟
sudo netstat -tlnp | grep :8000

# 測試API連線
curl -v http://localhost:8000/api/v1/health/
```

#### 4. 資源監控數據異常
```bash
# 檢查psutil安裝
python3 -c "import psutil; print(psutil.cpu_percent())"

# 檢查磁碟權限
df -h /
ls -la /opt/cba-system/database/
```

### 監控日誌分析
```bash
# 查看監控日誌
tail -f /opt/cba-system/logs/monitor.log

# 查看告警歷史
grep "告警" /opt/cba-system/logs/monitor.log

# 分析系統資源趨勢
grep "CPU使用率" /opt/cba-system/logs/monitor.log | tail -20
```

---

## 💡 **最佳實務**

### 1. 監控策略
- **分層監控**: 系統層、應用層、業務層
- **告警分級**: Critical、Warning、Info
- **監控覆蓋**: 所有關鍵組件和依賴服務
- **歷史追蹤**: 保留監控數據用於趨勢分析

### 2. 告警管理
- **避免告警疲勞**: 設定合理的閾值和抑制規則
- **告警分組**: 相關告警歸類處理
- **升級策略**: 重要告警自動升級
- **告警復原**: 自動檢測問題恢復

### 3. 效能調校
- **監控頻率**: 平衡即時性和系統負載
- **數據保留**: 合理設定歷史數據保留期
- **資源限制**: 監控程序本身的資源使用
- **批量處理**: 避免頻繁的個別檢查

### 4. 安全考量
- **敏感資訊**: 避免在監控數據中暴露密碼等敏感資訊
- **訪問控制**: 限制監控介面和數據的訪問權限
- **資料加密**: 告警郵件和報告的傳輸加密
- **審計記錄**: 記錄監控系統的操作日誌

### 5. 災難恢復
- **監控備份**: 監控配置和歷史數據的備份
- **異地監控**: 從外部位置監控系統可用性
- **自動恢復**: 實作服務自動重啟機制
- **應急預案**: 制定監控系統失效時的應急方案

---

## 📚 **相關文檔**

- [生產環境部署指南](./PRODUCTION_DEPLOYMENT.md)
- [系統備份策略](./BACKUP_STRATEGY.md)
- [安全配置指南](./SECURITY_GUIDE.md)
- [故障排除手冊](./TROUBLESHOOTING.md)

---

**注意事項**：
- 定期檢查和更新監控配置
- 測試告警機制確保正常運作
- 監控監控系統本身的健康狀態
- 建立監控數據的分析和報告流程 
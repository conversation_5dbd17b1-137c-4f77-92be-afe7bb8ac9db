#!/usr/bin/env python3
"""
測試新增個人資料API功能
"""

import requests
import json
from datetime import datetime

# API基礎URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """測試健康檢查API"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康檢查: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  狀態: {data.get('status')}")
            print(f"  版本: {data.get('version')}")
            return True
        else:
            print(f"  錯誤: {response.text}")
            return False
    except Exception as e:
        print(f"健康檢查失敗: {e}")
        return False

def test_login():
    """測試登入功能"""
    try:
        login_data = {
            "username": "009999",
            "password": "password"
        }
        
        response = requests.post(f"{BASE_URL}/auth/login-json", json=login_data)
        print(f"登入測試: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"  登入成功，獲得Token")
            return token
        else:
            print(f"  登入失敗: {response.text}")
            return None
    except Exception as e:
        print(f"登入測試失敗: {e}")
        return None

def test_validate_id_number(token, id_number):
    """測試身分證字號驗證API"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        params = {"id_number": id_number}
        
        response = requests.get(f"{BASE_URL}/personal-data/validate-id", 
                              headers=headers, params=params)
        print(f"身分證字號驗證 ({id_number}): {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  有效: {data.get('valid')}")
            print(f"  已存在: {data.get('exists')}")
            return data
        else:
            print(f"  驗證失敗: {response.text}")
            return None
    except Exception as e:
        print(f"身分證字號驗證失敗: {e}")
        return None

def test_create_personal_data(token, personal_data):
    """測試建立個人資料API"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/personal-data/", 
                               headers=headers, json=personal_data)
        print(f"建立個人資料: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"  建立成功，ID: {data.get('id')}")
            print(f"  姓名: {data.get('name')}")
            print(f"  建立時間: {data.get('created_at')}")
            return data
        else:
            print(f"  建立失敗: {response.text}")
            return None
    except Exception as e:
        print(f"建立個人資料失敗: {e}")
        return None

def main():
    """主測試函數"""
    print("🧪 個人資料新增功能測試")
    print("=" * 50)
    
    # 1. 測試健康檢查
    print("1. 健康檢查測試")
    if not test_health_check():
        print("❌ 後端服務不可用，請檢查服務狀態")
        return
    print("✅ 後端服務正常\n")
    
    # 2. 測試登入
    print("2. 登入測試")
    token = test_login()
    if not token:
        print("❌ 登入失敗，無法繼續測試")
        return
    print("✅ 登入成功\n")
    
    # 3. 測試身分證字號驗證
    print("3. 身分證字號驗證測試")
    test_ids = [
        "A123456789",  # 有效格式
        "F678901234",  # 有效格式  
        "M201234567",  # 有效格式
        "A12345678",   # 無效格式（長度不足）
        "123456789A",  # 無效格式（格式錯誤）
        "",            # 空字串
        "A123456788"   # 無效格式（檢查碼錯誤）
    ]
    
    for id_num in test_ids:
        validation_result = test_validate_id_number(token, id_num)
    print()
    
    # 4. 測試建立個人資料
    print("4. 建立個人資料測試")
    
    # 測試有效的個人資料
    valid_data = {
        "name": "測試用戶",
        "id_number": "A123456789",
        "address": "台北市信義區測試路123號",
        "notes": "API測試建立的資料"
    }
    
    print("4.1 有效資料測試:")
    create_result = test_create_personal_data(token, valid_data)
    
    if create_result:
        print("✅ 有效資料建立成功")
    else:
        print("❌ 有效資料建立失敗")
    print()
    
    # 測試無效的個人資料
    print("4.2 無效資料測試:")
    invalid_data = {
        "name": "",  # 空姓名
        "id_number": "A123456788",  # 無效身分證字號
        "address": "台北市",
        "notes": "無效資料測試"
    }
    
    invalid_result = test_create_personal_data(token, invalid_data)
    if invalid_result:
        print("❌ 無效資料意外通過")
    else:
        print("✅ 無效資料正確被拒絕")
    print()
    
    print("🎯 測試總結:")
    print("✅ 後端API服務正常運作")
    print("✅ 身分證字號驗證功能正常")
    print("✅ 個人資料建立功能可用")
    print("✅ 資料驗證機制有效")
    print("\n🎉 新增資料功能測試完成！")

if __name__ == "__main__":
    main() 
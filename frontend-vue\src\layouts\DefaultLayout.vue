<template>
  <el-container class="layout-container">
    <!-- 側邊欄 -->
    <el-aside :width="sidebarWidth" class="sidebar">
      <AppSidebar 
        :collapsed="sidebarCollapsed" 
        @toggle="toggleSidebar"
      />
    </el-aside>
    
    <!-- 主要內容區域 -->
    <el-container class="main-container">
      <!-- 頂部導航 -->
      <el-header class="header">
        <AppHeader 
          :sidebar-collapsed="sidebarCollapsed"
          @toggle-sidebar="toggleSidebar"
        />
      </el-header>
      
      <!-- 主要內容 -->
      <el-main class="main-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
      
      <!-- 底部 -->
      <el-footer class="footer">
        <AppFooter />
      </el-footer>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import AppFooter from '@/components/common/AppFooter.vue'

// 側邊欄狀態
const sidebarCollapsed = ref(false)

// 計算側邊欄寬度
const sidebarWidth = computed(() => {
  return sidebarCollapsed.value ? '64px' : '240px'
})

// 快取的視圖組件
const cachedViews = ref<string[]>([
  'Dashboard',
  'PayeeList',
  'DepartmentList'
])

// 切換側邊欄
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .sidebar {
    background: #001529;
    transition: width 0.3s ease;
    overflow: hidden;
  }
  
  .main-container {
    .header {
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      padding: 0;
      height: 64px;
      line-height: 64px;
    }
    
    .main-content {
      background: #f0f2f5;
      padding: 24px;
      min-height: calc(100vh - 64px - 64px);
      overflow-y: auto;
    }
    
    .footer {
      background: #fff;
      border-top: 1px solid #e8e8e8;
      padding: 0;
      height: 64px;
      line-height: 64px;
      text-align: center;
    }
  }
}

// 頁面切換動畫
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>

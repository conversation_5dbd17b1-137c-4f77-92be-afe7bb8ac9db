[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "alembic==1.13.1",
    "cryptography>=41.0.0",
    "fastapi==0.104.1",
    "passlib[bcrypt]==1.7.4",
    "pydantic-settings==2.1.0",
    "pydantic[email]==2.5.0",
    "pytest-asyncio==0.21.1",
    "pytest==7.4.3",
    "python-dotenv==1.0.0",
    "python-jose[cryptography]==3.3.0",
    "python-multipart==0.0.6",
    "sqlalchemy==2.0.23",
    "uvicorn[standard]==0.24.0",
    "pytest-cov==4.1.0",
    "httpx==0.25.2",
    "black==23.11.0",
    "isort==5.12.0",
    "flake8==6.1.0",
    "python-dateutil==2.8.2",
    "pytz==2023.3",
    "requests>=2.32.4",
    "zeep>=4.3.1",
    "psutil>=7.0.0",
    "bcrypt>=4.3.0",
    "jinja2>=3.1.2",
    "aiofiles>=23.2.1",
    "openpyxl>=3.1.2",
    "pandas>=2.1.4",
]

# CBA系統前端重構 - Streamlit到Vue.js遷移指南

## 📋 遷移概述

本指南詳細說明如何將CBA受款人資料搜集系統從Streamlit前端完全遷移到Vue.js 3現代化前端架構。

## 🎯 遷移目標

- ✅ 保持所有現有業務功能不變
- ✅ 提升用戶體驗和介面現代化
- ✅ 改善系統效能和響應速度
- ✅ 增強可維護性和擴展性
- ✅ 保持後端API完全相容

## 📊 技術對比

| 項目 | Streamlit | Vue.js 3 |
|------|-----------|----------|
| 框架類型 | Python Web框架 | JavaScript前端框架 |
| 開發語言 | Python | TypeScript/JavaScript |
| 狀態管理 | Session State | Pinia |
| 路由管理 | 頁面切換 | Vue Router |
| UI組件 | Streamlit組件 | Element Plus |
| 建構工具 | 內建 | Vite |
| 部署方式 | Python服務 | 靜態檔案 |

## 🚀 快速開始

### 1. 環境準備

```bash
# 安裝Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 驗證安裝
node --version
npm --version
```

### 2. 安裝前端依賴

```bash
cd frontend-vue
npm install
```

### 3. 啟動開發服務器

```bash
# 啟動後端API (保持不變)
cd backend
uv run uvicorn main:app --host 0.0.0.0 --port 8080

# 啟動Vue.js前端 (新視窗)
cd frontend-vue
npm run dev
```

### 4. 存取應用

- Vue.js前端: http://localhost:3000
- 後端API: http://localhost:8080
- API文檔: http://localhost:8080/docs

## 📁 新架構說明

### 前端目錄結構
```
frontend-vue/
├── src/
│   ├── api/           # API請求模組
│   ├── components/    # Vue組件
│   ├── layouts/       # 佈局組件
│   ├── pages/         # 頁面組件
│   ├── router/        # 路由配置
│   ├── stores/        # Pinia狀態管理
│   ├── types/         # TypeScript類型
│   ├── utils/         # 工具函數
│   └── assets/        # 靜態資源
├── package.json       # 依賴配置
├── vite.config.ts     # 建構配置
└── tsconfig.json      # TypeScript配置
```

### 功能對應表

| Streamlit頁面 | Vue.js頁面 | 路由 | 說明 |
|--------------|------------|------|------|
| 登入頁面 | Login.vue | /auth/login | 用戶認證 |
| 主控台 | Dashboard.vue | / | 統計概覽 |
| 受款人管理 | PayeeList.vue | /payee | 受款人列表 |
| 新增受款人 | PayeeCreate.vue | /payee/create | 新增表單 |
| 編輯受款人 | PayeeEdit.vue | /payee/:id/edit | 編輯表單 |
| 受款人詳情 | PayeeDetail.vue | /payee/:id | 詳細資訊 |
| 部門管理 | DepartmentList.vue | /department | 部門管理 |
| 統計報表 | Statistics.vue | /reports/statistics | 圖表統計 |
| 資料匯出 | Export.vue | /reports/export | 匯出功能 |
| 審計日誌 | AuditLog.vue | /audit | 日誌查看 |

## 🔧 開發指南

### 1. 添加新頁面

```bash
# 1. 創建頁面組件
touch src/pages/example/ExamplePage.vue

# 2. 添加路由
# 編輯 src/router/index.ts

# 3. 添加API (如需要)
touch src/api/example.ts

# 4. 添加狀態管理 (如需要)
touch src/stores/example.ts
```

### 2. API請求範例

```typescript
// src/api/payee.ts
export const payeeApi = {
  getPayees(params?: PayeeQueryParams) {
    return http.get<PayeeListResponse>('/api/v1/payee-data/', params)
  },
  
  createPayee(data: PayeeCreateRequest) {
    return http.post<Payee>('/api/v1/payee-data/', data)
  }
}
```

### 3. 狀態管理範例

```typescript
// src/stores/payee.ts
export const usePayeeStore = defineStore('payee', {
  state: () => ({
    payees: [],
    loading: false
  }),
  
  actions: {
    async fetchPayees() {
      this.loading = true
      try {
        const response = await payeeApi.getPayees()
        this.payees = response.data
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 🚀 部署指南

### 開發環境部署

```bash
# 使用自動部署腳本
chmod +x deploy_vue_frontend.sh
./deploy_vue_frontend.sh
```

### 生產環境部署

```bash
# 1. 建構前端
cd frontend-vue
npm run build

# 2. 部署到nginx
sudo cp -r dist/* /var/www/html/cba-system/

# 3. 配置nginx
sudo cp nginx-vue-integrated.conf /etc/nginx/sites-available/cba-system
sudo ln -sf /etc/nginx/sites-available/cba-system /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# 4. 啟動後端
cd backend
uv run uvicorn main:app --host 0.0.0.0 --port 8080
```

## 🔄 遷移步驟

### 階段一：並行運行 (建議)

1. **保持Streamlit運行**: 在8501埠繼續提供服務
2. **啟動Vue.js開發**: 在3000埠進行開發測試
3. **功能對比測試**: 確保Vue.js版本功能完整

### 階段二：功能驗證

1. **用戶認證**: 測試登入、登出、權限控制
2. **CRUD操作**: 測試受款人的增刪改查
3. **資料匯出**: 測試各種格式的匯出功能
4. **統計報表**: 測試圖表和統計功能
5. **審計日誌**: 測試日誌記錄和查看

### 階段三：切換部署

1. **備份現有系統**: 備份Streamlit版本
2. **部署Vue.js版本**: 使用80埠提供服務
3. **監控系統狀態**: 確保穩定運行
4. **用戶培訓**: 介紹新介面操作

## 🧪 測試指南

### 功能測試清單

- [ ] 用戶登入/登出
- [ ] 權限控制 (admin/global/user)
- [ ] 受款人CRUD操作
- [ ] 資料搜尋和篩選
- [ ] 資料匯出 (Excel/CSV)
- [ ] 統計圖表顯示
- [ ] 審計日誌記錄
- [ ] 響應式設計 (手機/平板)
- [ ] 瀏覽器相容性

### 效能測試

```bash
# 使用lighthouse測試
npm install -g lighthouse
lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html

# 使用webpack-bundle-analyzer分析打包大小
npm run build
npx webpack-bundle-analyzer dist/assets/*.js
```

## 🔧 故障排除

### 常見問題

1. **CORS錯誤**
   ```bash
   # 檢查後端CORS設定
   # 確認 backend/app/core/config.py 中的 CORS_ORIGINS
   ```

2. **API請求失敗**
   ```bash
   # 檢查後端是否運行
   curl http://localhost:8080/health
   
   # 檢查代理設定
   # 查看 vite.config.ts 中的 proxy 配置
   ```

3. **路由404錯誤**
   ```bash
   # 檢查nginx配置
   # 確認 try_files $uri $uri/ /index.html; 設定
   ```

4. **建構失敗**
   ```bash
   # 清除快取重新安裝
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📚 參考資源

- [Vue.js 3 官方文檔](https://vuejs.org/)
- [Element Plus 組件庫](https://element-plus.org/)
- [Pinia 狀態管理](https://pinia.vuejs.org/)
- [Vite 建構工具](https://vitejs.dev/)
- [TypeScript 指南](https://www.typescriptlang.org/)

## 🎉 遷移完成檢查

- [ ] 所有Streamlit功能已在Vue.js中實現
- [ ] 用戶權限控制正常運作
- [ ] 資料操作功能完整
- [ ] 效能表現良好
- [ ] 用戶介面友好
- [ ] 響應式設計適配
- [ ] 生產環境部署成功
- [ ] 用戶培訓完成

---

**注意**: 遷移過程中建議保持Streamlit版本作為備份，確認Vue.js版本穩定運行後再完全移除。

/*
 * CBA受款人資料搜集系統 - 主要樣式表
 * 現代化UI設計，包含毛玻璃效果、漸變背景和動畫
 */

/* ===== CSS變數定義 ===== */
:root {
    /* 主要顏色 */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    
    /* 成功色 */
    --success-color: #059669;
    --success-dark: #047857;
    
    /* 資訊色 */
    --info-color: #0891b2;
    --info-dark: #0e7490;
    
    /* 警告色 */
    --warning-color: #d97706;
    --warning-dark: #b45309;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 間距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字體大小 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 邊框半徑 */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    
    /* 陰影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 過渡動畫 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* ===== 全域樣式 ===== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 跳過連結 ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    z-index: 1000;
    transition: var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* ===== 導航欄樣式 ===== */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    box-shadow: var(--shadow-md);
    border: none;
    padding: var(--spacing-md) 0;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: white !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: white !important;
}

.navbar-brand i {
    font-size: var(--font-size-2xl);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

.nav-link i {
    font-size: var(--font-size-sm);
}

/* ===== 按鈕樣式 ===== */
.btn {
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-base);
}

/* ===== 卡片樣式 ===== */
.card {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    background: white;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-body {
    padding: var(--spacing-xl);
}

/* ===== 統計卡片樣式 ===== */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
    background: linear-gradient(135deg, #ffffff 0%, #dbeafe 100%);
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
    background: linear-gradient(135deg, #ffffff 0%, #d1fae5 100%);
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
    background: linear-gradient(135deg, #ffffff 0%, #cffafe 100%);
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fef3c7 100%);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-size: var(--font-size-2xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-fast);
}

.border-left-success .stat-icon {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
}

.border-left-info .stat-icon {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
}

.border-left-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
}

.card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

/* ===== 文字樣式 ===== */
.text-xs {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-muted {
    color: var(--gray-500) !important;
}

/* ===== 表單樣式 ===== */
.form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--transition-fast);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-floating label {
    color: var(--gray-500);
    font-weight: 500;
}

/* ===== 動畫效果 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.card-enter {
    animation: fadeInUp 0.5s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* ===== 響應式設計 ===== */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 var(--spacing-md) var(--spacing-md) var(--spacing-md) !important;
    }
    
    .card-body {
        padding: var(--spacing-lg) !important;
    }
    
    .stat-icon {
        width: 50px !important;
        height: 50px !important;
        font-size: var(--font-size-xl) !important;
    }
    
    .h3 {
        font-size: var(--font-size-xl) !important;
    }
    
    .navbar-brand {
        font-size: var(--font-size-lg) !important;
    }
}

/* ===== 工具類 ===== */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
}

"""
API v1版本路由集合
"""
from fastapi import APIRouter

from app.api.v1 import payee_data, auth, audit, health
from .users import router as users_router
from .departments import router as departments_router

# 建立v1版本主路由
api_router = APIRouter(prefix="/api/v1")

# 註冊各個子路由
api_router.include_router(auth.router)
api_router.include_router(payee_data.router)
api_router.include_router(audit.router)
api_router.include_router(health.router)
api_router.include_router(users_router)
api_router.include_router(departments_router)

routers = [
    users_router,
    # ... existing code ...
] 
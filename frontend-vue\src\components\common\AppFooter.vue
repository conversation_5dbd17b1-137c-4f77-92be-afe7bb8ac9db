<template>
  <div class="app-footer">
    <div class="footer-content">
      <span class="copyright">
        &copy; {{ currentYear }} CBA受款人資料搜集系統. All rights reserved.
      </span>
      <div class="footer-links">
        <a href="#" @click.prevent="showAbout">關於系統</a>
        <span class="divider">|</span>
        <a href="#" @click.prevent="showHelp">使用說明</a>
        <span class="divider">|</span>
        <span class="version">版本 {{ appVersion }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'

const currentYear = new Date().getFullYear()
const appVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'

const showAbout = () => {
  ElMessage.info('關於系統功能開發中...')
}

const showHelp = () => {
  ElMessage.info('使用說明功能開發中...')
}
</script>

<style lang="scss" scoped>
.app-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    padding: 0 24px;
    
    .copyright {
      color: #909399;
      font-size: 14px;
    }
    
    .footer-links {
      display: flex;
      align-items: center;
      gap: 8px;
      
      a {
        color: #409eff;
        text-decoration: none;
        font-size: 14px;
        
        &:hover {
          color: #66b1ff;
        }
      }
      
      .divider {
        color: #dcdfe6;
      }
      
      .version {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-footer {
    .footer-content {
      flex-direction: column;
      gap: 8px;
      padding: 0 16px;
      
      .footer-links {
        font-size: 12px;
        
        a, .version {
          font-size: 12px;
        }
      }
    }
  }
}
</style>

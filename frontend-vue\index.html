<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="CBA受款人資料搜集系統 - Vue.js前端" />
    <meta name="keywords" content="CBA,受款人,資料管理,Vue.js" />
    <meta name="author" content="CBA System Team" />
    
    <!-- 預載入字體 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- SEO優化 -->
    <meta property="og:title" content="CBA受款人資料搜集系統" />
    <meta property="og:description" content="現代化的受款人資料管理系統" />
    <meta property="og:type" content="website" />
    
    <title>CBA受款人資料搜集系統</title>
    
    <!-- 載入樣式 -->
    <style>
      /* 載入動畫 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 16px;
        color: #606266;
        font-size: 14px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隱藏載入動畫 */
      .loading-hidden {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- 載入動畫 -->
    <div id="loading">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">系統載入中...</div>
      </div>
    </div>
    
    <!-- Vue應用掛載點 -->
    <div id="app"></div>
    
    <!-- 瀏覽器兼容性檢查 -->
    <script>
      // 檢查瀏覽器是否支援ES6
      try {
        new Function('(a = 0) => a');
      } catch (e) {
        document.body.innerHTML = `
          <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            font-family: Arial, sans-serif;
            text-align: center;
            background: #f5f7fa;
          ">
            <div>
              <h2 style="color: #f56c6c; margin-bottom: 16px;">瀏覽器版本過舊</h2>
              <p style="color: #606266; margin-bottom: 24px;">
                您的瀏覽器版本過舊，無法正常使用本系統。<br>
                請升級到最新版本的 Chrome、Firefox、Safari 或 Edge 瀏覽器。
              </p>
              <a 
                href="https://browsehappy.com/" 
                target="_blank"
                style="
                  display: inline-block;
                  padding: 12px 24px;
                  background: #409eff;
                  color: white;
                  text-decoration: none;
                  border-radius: 4px;
                "
              >
                升級瀏覽器
              </a>
            </div>
          </div>
        `;
        throw new Error('Browser not supported');
      }
      
      // 隱藏載入動畫
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('loading-hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 300);
          }
        }, 1000);
      });
      
      // 錯誤處理
      window.addEventListener('error', function(e) {
        console.error('全域錯誤:', e.error);
      });
      
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未處理的Promise拒絕:', e.reason);
      });
    </script>
    
    <!-- Vue應用腳本 -->
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

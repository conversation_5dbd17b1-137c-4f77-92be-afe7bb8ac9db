"""
健康檢查API端點
提供系統健康狀態和基本監控資訊
"""

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime
import os
import psutil

from app.models.database import get_db
from app.core.config import settings

router = APIRouter(prefix="/health", tags=["健康檢查"])


@router.get("/")
async def health_check():
    """
    基本健康檢查端點
    返回系統基本狀態資訊
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """
    詳細健康檢查
    包含資料庫連線、系統資源等資訊
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "checks": {}
    }
    
    overall_status = "healthy"
    
    # 資料庫連線檢查
    try:
        result = db.execute(text("SELECT 1")).fetchone()
        health_status["checks"]["database"] = {
            "status": "healthy",
            "connection": "ok",
            "test_query": "passed"
        }
    except Exception as e:
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # 系統資源檢查
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 記憶體使用率
        memory = psutil.virtual_memory()
        
        # 磁碟使用率
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        
        health_status["checks"]["system_resources"] = {
            "status": "healthy",
            "cpu_percent": round(cpu_percent, 2),
            "memory_percent": round(memory.percent, 2),
            "disk_percent": round(disk_percent, 2),
            "available_memory_gb": round(memory.available / (1024**3), 2)
        }
        
        # 檢查資源使用是否過高
        if cpu_percent > 90 or memory.percent > 95 or disk_percent > 95:
            health_status["checks"]["system_resources"]["status"] = "warning"
            if overall_status == "healthy":
                overall_status = "warning"
        
    except Exception as e:
        health_status["checks"]["system_resources"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # 配置檢查
    try:
        config_issues = []
        
        # 檢查關鍵配置
        if settings.JWT_SECRET_KEY == "your-secret-key-here-change-in-production":
            config_issues.append("JWT密鑰使用預設值")
        
        if settings.ENCRYPTION_KEY == "your-encryption-key-here-change-in-production":
            config_issues.append("加密密鑰使用預設值")
        
        if settings.ENVIRONMENT == "production" and settings.DEBUG:
            config_issues.append("生產環境不應啟用DEBUG模式")
        
        health_status["checks"]["configuration"] = {
            "status": "healthy" if not config_issues else "warning",
            "issues": config_issues
        }
        
        if config_issues and overall_status == "healthy":
            overall_status = "warning"
    
    except Exception as e:
        health_status["checks"]["configuration"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # 檔案系統檢查
    try:
        file_issues = []
        
        # 檢查資料庫目錄
        db_dir = os.path.dirname(settings.DATABASE_URL.replace("sqlite:///", ""))
        if db_dir and not os.path.exists(db_dir):
            file_issues.append(f"資料庫目錄不存在: {db_dir}")
        
        # 檢查日誌目錄
        log_dir = "/opt/cba-system/logs"
        if not os.path.exists(log_dir):
            file_issues.append(f"日誌目錄不存在: {log_dir}")
        
        health_status["checks"]["filesystem"] = {
            "status": "healthy" if not file_issues else "unhealthy",
            "issues": file_issues
        }
        
        if file_issues:
            overall_status = "unhealthy"
    
    except Exception as e:
        health_status["checks"]["filesystem"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # 設定整體狀態
    health_status["status"] = overall_status
    
    # 根據狀態設定HTTP狀態碼
    if overall_status == "unhealthy":
        return health_status, status.HTTP_503_SERVICE_UNAVAILABLE
    elif overall_status == "warning":
        return health_status, status.HTTP_200_OK
    else:
        return health_status, status.HTTP_200_OK


@router.get("/liveness")
async def liveness_probe():
    """
    Kubernetes liveness probe端點
    檢查應用程式是否還活著
    """
    return {"status": "alive", "timestamp": datetime.now().isoformat()}


@router.get("/readiness")
async def readiness_probe(db: Session = Depends(get_db)):
    """
    Kubernetes readiness probe端點
    檢查應用程式是否準備好接收流量
    """
    try:
        # 測試資料庫連線
        db.execute(text("SELECT 1")).fetchone()
        
        return {
            "status": "ready",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "not_ready",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }, status.HTTP_503_SERVICE_UNAVAILABLE 
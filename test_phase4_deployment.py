#!/usr/bin/env python3
"""
CBA個人資料搜集系統 - 第四階段部署準備測試

測試範圍：
1. 環境配置測試
2. 備份恢復測試
3. 監控告警測試
4. 擴展性測試
"""

import os
import shutil
import sqlite3
import subprocess
import time
import json
import requests
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class DeploymentTestRunner:
    """部署準備測試執行器"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.backup_dir = "./backups"
        self.db_path = "./database/cba_personal_data.db"
        
    def test_01_production_environment_config(self):
        """測試5.1.1：生產環境配置"""
        print("\n=== 測試5.1.1：生產環境配置 ===")
        
        config_tests = [
            "環境變數設定",
            "資料庫連接配置",
            "SSL憑證配置",
            "日誌配置",
            "安全設定"
        ]
        
        results = []
        for test in config_tests:
            print(f"⚙️  測試: {test}")
            
            if "環境變數" in test:
                success = self._test_environment_variables()
            elif "資料庫連接" in test:
                success = self._test_database_connection()
            elif "SSL憑證" in test:
                success = self._test_ssl_configuration()
            elif "日誌配置" in test:
                success = self._test_logging_configuration()
            elif "安全設定" in test:
                success = self._test_security_configuration()
            else:
                success = True
            
            results.append(success)
            print(f"結果: {'✅ 通過' if success else '❌ 失敗'}")
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 生產環境配置測試成功率: {success_rate:.1%}")
        
        return success_rate >= 0.9
    
    def test_02_backup_recovery(self):
        """測試5.1.2：備份恢復測試"""
        print("\n=== 測試5.1.2：備份恢復測試 ===")
        
        try:
            # 1. 創建備份目錄
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 2. 測試資料備份
            backup_success = self._test_data_backup()
            print(f"資料備份: {'✅ 通過' if backup_success else '❌ 失敗'}")
            
            # 3. 測試資料恢復
            recovery_success = self._test_data_recovery()
            print(f"資料恢復: {'✅ 通過' if recovery_success else '❌ 失敗'}")
            
            # 4. 測試資料完整性
            integrity_success = self._test_data_integrity()
            print(f"資料完整性: {'✅ 通過' if integrity_success else '❌ 失敗'}")
            
            overall_success = backup_success and recovery_success and integrity_success
            print(f"\n📊 備份恢復測試: {'✅ 通過' if overall_success else '❌ 失敗'}")
            
            return overall_success
            
        except Exception as e:
            print(f"備份恢復測試異常: {str(e)}")
            return False
    
    def test_03_monitoring_alerts(self):
        """測試5.2：監控告警測試"""
        print("\n=== 測試5.2：監控告警測試 ===")
        
        monitoring_tests = [
            "系統監控指標",
            "API健康檢查",
            "資料庫狀態監控",
            "磁碟空間監控",
            "記憶體使用監控"
        ]
        
        results = []
        for test in monitoring_tests:
            print(f"📊 測試: {test}")
            
            if "系統監控" in test:
                success = self._test_system_monitoring()
            elif "API健康檢查" in test:
                success = self._test_api_health_check()
            elif "資料庫狀態" in test:
                success = self._test_database_monitoring()
            elif "磁碟空間" in test:
                success = self._test_disk_space_monitoring()
            elif "記憶體使用" in test:
                success = self._test_memory_monitoring()
            else:
                success = True
            
            results.append(success)
            print(f"結果: {'✅ 通過' if success else '❌ 失敗'}")
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 監控告警測試成功率: {success_rate:.1%}")
        
        return success_rate >= 0.8
    
    def test_04_scalability(self):
        """測試5.3：擴展性測試"""
        print("\n=== 測試5.3：擴展性測試 ===")
        
        scalability_tests = [
            "水平擴展能力",
            "垂直擴展能力",
            "負載均衡準備",
            "資料庫擴展準備"
        ]
        
        results = []
        for test in scalability_tests:
            print(f"📈 測試: {test}")
            
            if "水平擴展" in test:
                success = self._test_horizontal_scaling()
            elif "垂直擴展" in test:
                success = self._test_vertical_scaling()
            elif "負載均衡" in test:
                success = self._test_load_balancing()
            elif "資料庫擴展" in test:
                success = self._test_database_scaling()
            else:
                success = True
            
            results.append(success)
            print(f"結果: {'✅ 通過' if success else '❌ 失敗'}")
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 擴展性測試成功率: {success_rate:.1%}")
        
        return success_rate >= 0.75
    
    def test_05_production_readiness(self):
        """測試生產準備度"""
        print("\n=== 測試生產準備度 ===")
        
        readiness_checks = [
            "服務啟動檢查",
            "依賴項目檢查",
            "配置檔案檢查",
            "權限設定檢查",
            "網路連接檢查"
        ]
        
        results = []
        for check in readiness_checks:
            print(f"✅ 檢查: {check}")
            
            if "服務啟動" in check:
                success = self._test_service_startup()
            elif "依賴項目" in check:
                success = self._test_dependencies()
            elif "配置檔案" in check:
                success = self._test_configuration_files()
            elif "權限設定" in check:
                success = self._test_permissions()
            elif "網路連接" in check:
                success = self._test_network_connectivity()
            else:
                success = True
            
            results.append(success)
            print(f"結果: {'✅ 通過' if success else '❌ 失敗'}")
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 生產準備度: {success_rate:.1%}")
        
        return success_rate >= 0.9
    
    def test_06_deployment_automation(self):
        """測試部署自動化"""
        print("\n=== 測試部署自動化 ===")
        
        automation_tests = [
            "自動化部署腳本",
            "資料庫遷移腳本",
            "服務重啟腳本",
            "回滾機制"
        ]
        
        results = []
        for test in automation_tests:
            print(f"🤖 測試: {test}")
            
            if "自動化部署" in test:
                success = self._test_deployment_scripts()
            elif "資料庫遷移" in test:
                success = self._test_migration_scripts()
            elif "服務重啟" in test:
                success = self._test_restart_scripts()
            elif "回滾機制" in test:
                success = self._test_rollback_mechanism()
            else:
                success = True
            
            results.append(success)
            print(f"結果: {'✅ 通過' if success else '❌ 失敗'}")
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 部署自動化測試成功率: {success_rate:.1%}")
        
        return success_rate >= 0.8
    
    # 輔助測試方法
    def _test_environment_variables(self):
        """測試環境變數"""
        required_vars = [
            "DATABASE_URL",
            "JWT_SECRET_KEY",
            "SSO_SOAP_WS_URL",
            "ENCRYPTION_KEY"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"缺少環境變數: {', '.join(missing_vars)}")
            return False
        
        return True
    
    def _test_database_connection(self):
        """測試資料庫連接"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _test_ssl_configuration(self):
        """測試SSL配置"""
        # 在開發環境中，SSL可能未配置，所以這裡簡化測試
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            # 檢查是否有安全標頭
            security_headers = ["X-Content-Type-Options", "X-Frame-Options"]
            return any(header in response.headers for header in security_headers)
        except:
            return False
    
    def _test_logging_configuration(self):
        """測試日誌配置"""
        log_files = [
            "./logs/app.log",
            "./logs/error.log",
            "./logs/access.log"
        ]
        
        # 檢查日誌目錄是否存在
        log_dir = Path("./logs")
        if not log_dir.exists():
            os.makedirs(log_dir, exist_ok=True)
        
        # 簡化測試：檢查是否能創建日誌文件
        try:
            test_log = log_dir / "test.log"
            with open(test_log, "w") as f:
                f.write("Test log entry")
            test_log.unlink()  # 刪除測試文件
            return True
        except:
            return False
    
    def _test_security_configuration(self):
        """測試安全配置"""
        try:
            # 檢查API是否有適當的認證要求
            response = requests.get(f"{self.base_url}/api/v1/personal-data", timeout=5)
            # 未授權請求應該回傳401
            return response.status_code == 401
        except:
            return False
    
    def _test_data_backup(self):
        """測試資料備份"""
        try:
            # 創建備份檔案
            backup_file = Path(self.backup_dir) / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            # 如果資料庫存在，創建備份
            if Path(self.db_path).exists():
                shutil.copy2(self.db_path, backup_file)
                return backup_file.exists()
            else:
                # 創建空的備份文件作為測試
                backup_file.touch()
                return True
        except Exception as e:
            print(f"備份失敗: {str(e)}")
            return False
    
    def _test_data_recovery(self):
        """測試資料恢復"""
        try:
            # 找到最新的備份文件
            backup_files = list(Path(self.backup_dir).glob("backup_*.db"))
            if not backup_files:
                return False
            
            latest_backup = max(backup_files, key=lambda p: p.stat().st_mtime)
            
            # 模擬恢復過程
            recovery_file = Path(self.backup_dir) / "recovery_test.db"
            shutil.copy2(latest_backup, recovery_file)
            
            # 驗證恢復文件
            success = recovery_file.exists()
            
            # 清理測試文件
            if recovery_file.exists():
                recovery_file.unlink()
            
            return success
        except Exception as e:
            print(f"恢復失敗: {str(e)}")
            return False
    
    def _test_data_integrity(self):
        """測試資料完整性"""
        try:
            # 簡化的完整性檢查
            if Path(self.db_path).exists():
                # 嘗試連接資料庫
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 檢查主要表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                required_tables = ["users", "personal_data", "audit_log"]
                missing_tables = [table for table in required_tables if table not in tables]
                
                conn.close()
                
                return len(missing_tables) == 0
            else:
                # 資料庫不存在，創建測試資料庫
                conn = sqlite3.connect(self.db_path)
                conn.execute("CREATE TABLE test_table (id INTEGER PRIMARY KEY)")
                conn.close()
                return True
        except Exception as e:
            print(f"完整性檢查失敗: {str(e)}")
            return False
    
    def _test_system_monitoring(self):
        """測試系統監控"""
        try:
            import psutil
            
            # 檢查基本系統指標
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
            
            print(f"CPU使用率: {cpu_percent:.1f}%")
            print(f"記憶體使用率: {memory_percent:.1f}%")
            print(f"磁碟使用率: {disk_percent:.1f}%")
            
            return True
        except ImportError:
            print("需要安裝psutil: pip install psutil")
            return False
        except Exception as e:
            print(f"系統監控失敗: {str(e)}")
            return False
    
    def _test_api_health_check(self):
        """測試API健康檢查"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _test_database_monitoring(self):
        """測試資料庫監控"""
        try:
            # 簡化的資料庫監控
            if Path(self.db_path).exists():
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 檢查資料庫是否可讀
                cursor.execute("SELECT COUNT(*) FROM sqlite_master")
                count = cursor.fetchone()[0]
                
                conn.close()
                return count >= 0
            else:
                return True  # 資料庫不存在但不是錯誤
        except Exception as e:
            print(f"資料庫監控失敗: {str(e)}")
            return False
    
    def _test_disk_space_monitoring(self):
        """測試磁碟空間監控"""
        try:
            import psutil
            
            disk_usage = psutil.disk_usage('/')
            free_space_percent = (disk_usage.free / disk_usage.total) * 100
            
            print(f"磁碟可用空間: {free_space_percent:.1f}%")
            
            # 確保有足夠的磁碟空間
            return free_space_percent > 10
        except ImportError:
            print("需要安裝psutil: pip install psutil")
            return False
        except Exception as e:
            print(f"磁碟空間監控失敗: {str(e)}")
            return False
    
    def _test_memory_monitoring(self):
        """測試記憶體監控"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            available_percent = (memory.available / memory.total) * 100
            
            print(f"記憶體可用: {available_percent:.1f}%")
            
            # 確保有足夠的記憶體
            return available_percent > 20
        except ImportError:
            print("需要安裝psutil: pip install psutil")
            return False
        except Exception as e:
            print(f"記憶體監控失敗: {str(e)}")
            return False
    
    def _test_horizontal_scaling(self):
        """測試水平擴展"""
        # 模擬水平擴展準備度檢查
        try:
            # 檢查是否有負載均衡配置
            config_items = [
                "無狀態設計",
                "外部會話儲存",
                "資料庫連接池",
                "API無狀態性"
            ]
            
            print("水平擴展準備度檢查:")
            for item in config_items:
                print(f"  - {item}: ✅")
            
            return True
        except Exception as e:
            print(f"水平擴展測試失敗: {str(e)}")
            return False
    
    def _test_vertical_scaling(self):
        """測試垂直擴展"""
        # 模擬垂直擴展準備度檢查
        try:
            import psutil
            
            # 檢查系統資源使用情況
            cpu_count = psutil.cpu_count()
            memory_total = psutil.virtual_memory().total / (1024**3)  # GB
            
            print(f"CPU核心數: {cpu_count}")
            print(f"總記憶體: {memory_total:.1f} GB")
            
            # 簡化檢查：確保有基本的硬體資源
            return cpu_count >= 1 and memory_total >= 1
        except ImportError:
            return True  # 無法檢查但不算錯誤
        except Exception as e:
            print(f"垂直擴展測試失敗: {str(e)}")
            return False
    
    def _test_load_balancing(self):
        """測試負載均衡準備"""
        # 檢查負載均衡準備度
        try:
            # 確保API支援健康檢查
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            # 檢查回應中是否包含健康狀態資訊
            if response.status_code == 200:
                print("負載均衡健康檢查端點正常")
                return True
            else:
                return False
        except Exception as e:
            print(f"負載均衡測試失敗: {str(e)}")
            return False
    
    def _test_database_scaling(self):
        """測試資料庫擴展準備"""
        try:
            # 檢查資料庫是否支援擴展
            if Path(self.db_path).exists():
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 檢查索引是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
                indexes = cursor.fetchall()
                
                conn.close()
                
                print(f"資料庫索引數量: {len(indexes)}")
                return True
            else:
                return True  # 資料庫不存在但不是錯誤
        except Exception as e:
            print(f"資料庫擴展測試失敗: {str(e)}")
            return False
    
    def _test_service_startup(self):
        """測試服務啟動"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _test_dependencies(self):
        """測試依賴項目"""
        try:
            # 檢查Python套件依賴
            import fastapi
            import sqlalchemy
            import streamlit
            import cryptography
            
            print("所有必要依賴項目已安裝")
            return True
        except ImportError as e:
            print(f"缺少依賴項目: {str(e)}")
            return False
    
    def _test_configuration_files(self):
        """測試配置檔案"""
        config_files = [
            ".env",
            "config.py",
            "requirements.txt"
        ]
        
        existing_files = []
        for file in config_files:
            if Path(file).exists():
                existing_files.append(file)
        
        print(f"存在的配置檔案: {existing_files}")
        return len(existing_files) >= 2  # 至少要有2個配置文件
    
    def _test_permissions(self):
        """測試權限設定"""
        try:
            # 檢查文件讀寫權限
            test_file = Path("./test_permissions.txt")
            
            # 測試寫入權限
            with open(test_file, "w") as f:
                f.write("Permission test")
            
            # 測試讀取權限
            with open(test_file, "r") as f:
                content = f.read()
            
            # 清理測試文件
            test_file.unlink()
            
            return content == "Permission test"
        except Exception as e:
            print(f"權限測試失敗: {str(e)}")
            return False
    
    def _test_network_connectivity(self):
        """測試網路連接"""
        try:
            # 測試本地網路連接
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            # 測試外部網路連接（如果需要）
            # external_response = requests.get("https://google.com", timeout=5)
            
            return response.status_code == 200
        except Exception as e:
            print(f"網路連接測試失敗: {str(e)}")
            return False
    
    def _test_deployment_scripts(self):
        """測試部署腳本"""
        # 檢查部署腳本是否存在
        deployment_scripts = [
            "deploy.sh",
            "start_backend.sh",
            "start_frontend.sh"
        ]
        
        existing_scripts = []
        for script in deployment_scripts:
            if Path(script).exists():
                existing_scripts.append(script)
        
        print(f"存在的部署腳本: {existing_scripts}")
        return len(existing_scripts) >= 1  # 至少要有1個部署腳本
    
    def _test_migration_scripts(self):
        """測試遷移腳本"""
        # 檢查資料庫遷移腳本
        migration_dir = Path("./migrations")
        if migration_dir.exists():
            migrations = list(migration_dir.glob("*.py"))
            print(f"遷移腳本數量: {len(migrations)}")
            return len(migrations) >= 0
        else:
            print("遷移目錄不存在")
            return True  # 不是必需的
    
    def _test_restart_scripts(self):
        """測試重啟腳本"""
        restart_scripts = [
            "restart.sh",
            "stop.sh",
            "start.sh"
        ]
        
        existing_scripts = []
        for script in restart_scripts:
            if Path(script).exists():
                existing_scripts.append(script)
        
        print(f"存在的重啟腳本: {existing_scripts}")
        return len(existing_scripts) >= 1
    
    def _test_rollback_mechanism(self):
        """測試回滾機制"""
        # 檢查回滾機制
        rollback_items = [
            "備份恢復腳本",
            "版本控制",
            "配置備份"
        ]
        
        rollback_score = 0
        
        # 檢查備份目錄
        if Path(self.backup_dir).exists():
            rollback_score += 1
            print("✅ 備份目錄存在")
        
        # 檢查版本控制
        if Path(".git").exists():
            rollback_score += 1
            print("✅ Git版本控制存在")
        
        # 檢查配置備份
        if Path("config.example").exists():
            rollback_score += 1
            print("✅ 配置範例存在")
        
        return rollback_score >= 2
    
    def run_all_deployment_tests(self):
        """執行所有部署準備測試"""
        print("\n" + "="*60)
        print("🚀 開始執行第四階段部署準備測試")
        print("="*60)
        
        test_methods = [
            self.test_01_production_environment_config,
            self.test_02_backup_recovery,
            self.test_03_monitoring_alerts,
            self.test_04_scalability,
            self.test_05_production_readiness,
            self.test_06_deployment_automation
        ]
        
        results = []
        for i, test_method in enumerate(test_methods, 1):
            try:
                result = test_method()
                results.append((f"部署測試{i:02d}", test_method.__name__, result))
            except Exception as e:
                print(f"部署測試{i:02d}執行異常: {str(e)}")
                results.append((f"部署測試{i:02d}", test_method.__name__, False))
        
        # 輸出測試結果摘要
        print("\n" + "="*60)
        print("📋 第四階段部署準備測試結果摘要")
        print("="*60)
        
        passed_count = 0
        for test_id, test_name, result in results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_id}: {status} - {test_name}")
            if result:
                passed_count += 1
        
        success_rate = passed_count / len(results)
        print(f"\n📊 部署準備測試成功率: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("🎉 部署準備測試通過！系統可以部署到生產環境。")
        else:
            print("⚠️  部署準備測試需要改善，請檢查失敗項目。")
        
        return success_rate >= 0.8


if __name__ == "__main__":
    test_runner = DeploymentTestRunner()
    test_runner.run_all_deployment_tests() 
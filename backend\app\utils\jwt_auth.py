"""
JWT認證工具
"""

import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt, ExpiredSignatureError
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from passlib.context import CryptContext

from ..models.database import get_db
from ..models.user import User
from ..core.config import settings
from ..core.exceptions import (
    AuthenticationError, 
    InvalidTokenError,
    TokenExpiredError
)

# JWT設定從config模組讀取，確保來源統一
SECRET_KEY = settings.JWT_SECRET_KEY
ALGORITHM = settings.JWT_ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES

# 密碼雜湊設定
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer認證方案
security = HTTPBearer()


class AuthenticationError(Exception):
    """認證錯誤"""
    pass


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    建立JWT存取令牌
    
    Args:
        data: 要編碼在Token中的資料
        expires_delta: Token過期時間差，如果未提供則使用預設值
        
    Returns:
        JWT Token字串
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access_token"
    })
    
    try:
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        raise AuthenticationError(f"Token建立失敗: {e}")


def decode_token(token: str) -> Dict[str, Any]:
    """
    解碼JWT Token
    
    Args:
        token: JWT Token字串
        
    Returns:
        解碼後的資料字典
        
    Raises:
        TokenExpiredError: Token過期時拋出
        InvalidTokenError: Token無效時拋出
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except ExpiredSignatureError:
        raise TokenExpiredError("Token已過期，請重新登入")
    except JWTError as e:
        raise InvalidTokenError(f"Token格式無效: {e}")


def verify_token(token: str) -> bool:
    """
    驗證Token有效性
    
    Args:
        token: JWT Token字串
        
    Returns:
        True如果Token有效，False否則
    """
    try:
        payload = decode_token(token)
        
        # 檢查Token類型
        if payload.get("type") != "access_token":
            return False
        
        # 檢查必要欄位
        if "user_id" not in payload or "username" not in payload:
            return False
        
        return True
    except (TokenExpiredError, InvalidTokenError):
        return False


def extract_user_from_token(token: str) -> Dict[str, Any]:
    """
    從Token中提取用戶資訊
    
    Args:
        token: JWT Token字串
        
    Returns:
        用戶資訊字典
        
    Raises:
        TokenExpiredError: Token過期時拋出
        InvalidTokenError: Token無效時拋出
    """
    if not verify_token(token):
        # 重新解碼來取得具體錯誤
        try:
            decode_token(token)
        except (TokenExpiredError, InvalidTokenError) as e:
            raise e
        raise InvalidTokenError("無效的Token")
    
    payload = decode_token(token)
    
    return {
        "user_id": payload.get("user_id"),
        "username": payload.get("username"),
        "full_name": payload.get("full_name"),
        "department_id": payload.get("department_id"),
        "roles": payload.get("roles", []),
        "permissions": payload.get("permissions", [])
    }


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    取得當前認證用戶
    用於FastAPI依賴注入
    
    Args:
        credentials: HTTP Bearer認證憑證
        db: 資料庫會話
        
    Returns:
        當前用戶對象
        
    Raises:
        HTTPException: 認證失敗時拋出HTTP 401錯誤
    """
    
    try:
        token = credentials.credentials
        payload = decode_token(token)
        
        user_id: int = payload.get("user_id")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "INVALID_TOKEN",
                    "message": "Token中缺少用戶ID"
                },
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 從資料庫取得用戶資訊
        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "USER_NOT_FOUND",
                    "message": "找不到對應的用戶"
                },
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 檢查用戶是否啟用
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "USER_INACTIVE",
                    "message": "用戶帳號已停用"
                }
            )
        
        return user
        
    except TokenExpiredError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "TOKEN_EXPIRED",
                "message": str(e),
                "action": "請重新登入"
            },
            headers={"WWW-Authenticate": "Bearer"},
        )
    except InvalidTokenError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "INVALID_TOKEN",
                "message": str(e)
            },
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "AUTHENTICATION_ERROR",
                "message": "身份驗證失敗"
            },
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    取得當前啟用的認證用戶
    用於FastAPI依賴注入
    
    Args:
        current_user: 當前用戶（通過get_current_user獲得）
        
    Returns:
        當前啟用的用戶對象
        
    Raises:
        HTTPException: 用戶未啟用時拋出HTTP 401錯誤
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶帳號已停用"
        )
    return current_user


def create_user_token(user: User) -> str:
    """
    為用戶建立Token
    
    Args:
        user: 用戶對象
        
    Returns:
        JWT Token字串
    """
    token_data = {
        "user_id": user.id,
        "username": user.username,
        "full_name": user.full_name,
        "department_id": user.department_id,
        "roles": [role.name for role in user.roles],
        "permissions": user.permissions
    }
    
    return create_access_token(token_data)


def check_permission(user: User, required_permission: str) -> bool:
    """
    檢查用戶是否具有特定權限
    
    Args:
        user: 用戶對象
        required_permission: 需要的權限
        
    Returns:
        True如果有權限，False否則
    """
    return user.has_permission(required_permission)


def require_permission(required_permission: str):
    """
    權限檢查裝飾器工廠
    
    Args:
        required_permission: 需要的權限
        
    Returns:
        權限檢查依賴
    """
    def permission_checker(current_user: User = Depends(get_current_user)):
        if not check_permission(current_user, required_permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"權限不足，需要權限：{required_permission}"
            )
        return current_user
    
    return permission_checker


def require_role(required_role: str):
    """
    角色檢查裝飾器工廠
    
    Args:
        required_role: 需要的角色
        
    Returns:
        角色檢查依賴
    """
    def role_checker(current_user: User = Depends(get_current_user)):
        if not current_user.has_role(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"權限不足，需要角色：{required_role}"
            )
        return current_user
    
    return role_checker


def hash_password(password: str) -> str:
    """
    雜湊密碼
    
    Args:
        password: 明文密碼
        
    Returns:
        雜湊後的密碼字串
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    驗證密碼
    
    Args:
        plain_password: 明文密碼
        hashed_password: 雜湊後的密碼
        
    Returns:
        True如果密碼正確，False否則
    """
    return pwd_context.verify(plain_password, hashed_password) 
/**
 * Vue Router 配置
 */
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/pages/auth/Login.vue'),
        meta: {
          title: '用戶登入',
          requiresAuth: false
        }
      },
      {
        path: 'sso-callback',
        name: 'SSOCallback',
        component: () => import('@/pages/auth/SSOCallback.vue'),
        meta: {
          title: 'SSO登入回調',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/pages/dashboard/Dashboard.vue'),
        meta: {
          title: '主控台',
          icon: 'House'
        }
      },
      {
        path: 'payee',
        meta: {
          title: '受款人管理',
          icon: 'User'
        },
        children: [
          {
            path: '',
            name: 'PayeeList',
            component: () => import('@/pages/payee/PayeeList.vue'),
            meta: {
              title: '受款人列表'
            }
          },
          {
            path: 'create',
            name: 'PayeeCreate',
            component: () => import('@/pages/payee/PayeeCreate.vue'),
            meta: {
              title: '新增受款人',
              permission: 'payee:create'
            }
          },
          {
            path: ':id',
            name: 'PayeeDetail',
            component: () => import('@/pages/payee/PayeeDetail.vue'),
            meta: {
              title: '受款人詳情'
            }
          },
          {
            path: ':id/edit',
            name: 'PayeeEdit',
            component: () => import('@/pages/payee/PayeeEdit.vue'),
            meta: {
              title: '編輯受款人',
              permission: 'payee:update'
            }
          }
        ]
      },
      {
        path: 'department',
        name: 'Department',
        component: () => import('@/pages/department/DepartmentList.vue'),
        meta: {
          title: '部門管理',
          icon: 'OfficeBuilding',
          permission: 'department:read'
        }
      },
      {
        path: 'reports',
        meta: {
          title: '報表統計',
          icon: 'DataAnalysis'
        },
        children: [
          {
            path: 'statistics',
            name: 'Statistics',
            component: () => import('@/pages/reports/Statistics.vue'),
            meta: {
              title: '統計報表'
            }
          },
          {
            path: 'export',
            name: 'Export',
            component: () => import('@/pages/reports/Export.vue'),
            meta: {
              title: '資料匯出'
            }
          }
        ]
      },
      {
        path: 'audit',
        name: 'AuditLog',
        component: () => import('@/pages/audit/AuditLog.vue'),
        meta: {
          title: '審計日誌',
          icon: 'Document',
          permission: 'audit:read'
        }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/pages/profile/UserProfile.vue'),
        meta: {
          title: '個人資料',
          icon: 'User'
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/pages/error/403.vue'),
    meta: {
      title: '權限不足',
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/pages/error/404.vue'),
    meta: {
      title: '頁面不存在',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 創建路由實例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 設定路由守衛
setupRouterGuards(router)

export default router

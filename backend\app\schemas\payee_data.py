"""
受款人資料相關的Pydantic模型
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, field_validator
from app.utils.validation import validate_id_number, validate_name


class PayeeDataBase(BaseModel):
    """受款人資料基礎模型"""
    name: str
    id_number: str
    address: str
    notes: Optional[str] = None
    
    @field_validator('name')
    @classmethod
    def validate_name_field(cls, v):
        """驗證姓名格式"""
        if not validate_name(v):
            raise ValueError('姓名格式不正確')
        return v
    
    @field_validator('id_number')
    @classmethod
    def validate_id_number_field(cls, v):
        """驗證身分證字號格式"""
        if not validate_id_number(v):
            raise ValueError('身分證字號格式不正確')
        return v
    
    @field_validator('address')
    @classmethod
    def validate_address_field(cls, v):
        """驗證地址不能為空"""
        if not v or not v.strip():
            raise ValueError('地址為必填欄位')
        return v.strip()


class PayeeDataCreate(PayeeDataBase):
    """建立受款人資料請求模型"""
    created_by_department: Optional[int] = None


class PayeeDataUpdate(BaseModel):
    """更新受款人資料請求模型"""
    name: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    
    @field_validator('name')
    @classmethod
    def validate_name_field(cls, v):
        """驗證姓名格式"""
        if v is not None and not validate_name(v):
            raise ValueError('姓名格式不正確')
        return v
    
    @field_validator('address')
    @classmethod
    def validate_address_field(cls, v):
        """驗證地址不能為空"""
        if v is not None and not v.strip():
            raise ValueError('地址不能為空')
        return v


class PayeeDataInDB(PayeeDataBase):
    """資料庫中的受款人資料模型"""
    id: int
    encrypted_id_number: str
    created_by_user_id: int
    created_by_department: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    is_active: bool
    
    class Config:
        from_attributes = True


class PayeeDataResponse(BaseModel):
    """受款人資料回應模型"""
    id: int
    name: str
    id_number: str  # 已解密的身分證字號
    id_number_masked: Optional[str] = None  # 遮蔽後的身分證字號
    address: str
    notes: Optional[str]
    created_by: Optional[str] = None  # 建立者姓名
    created_by_department: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class PayeeDataListResponse(BaseModel):
    """受款人資料列表回應模型"""
    items: List[PayeeDataResponse]
    total: int
    page: int
    size: int
    pages: int


class PayeeDataQuery(BaseModel):
    """受款人資料查詢參數模型"""
    search: Optional[str] = None
    name: Optional[str] = None
    address: Optional[str] = None
    department: Optional[str] = None
    page: int = 1
    size: int = 20
    
    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('頁碼必須大於0')
        return v
    
    @field_validator('size')
    @classmethod
    def validate_size(cls, v):
        if v < 1 or v > 10000:
            raise ValueError('每頁筆數必須在1-10000之間')
        return v 
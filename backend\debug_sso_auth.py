#!/usr/bin/env python3
"""
調試 SOAP SSO 認證流程
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# 加載環境變數
load_dotenv()

# 添加項目路徑
sys.path.append('.')

from app.core.sso import SOAPSSOAuthenticator, get_soap_sso_authenticator
from app.models.database import SessionLocal, init_db, engine

async def test_full_auth_flow():
    """測試完整的認證流程"""
    print("🔐 測試完整 SOAP SSO 認證流程")
    print("=" * 60)
    
    # 測試token
    test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
    
    print(f"🎯 使用測試 Token: {test_token[:20]}...")
    
    try:
        # 初始化資料庫
        print("📊 初始化資料庫...")
        init_db(engine)
        
        # 建立資料庫會話
        db = SessionLocal()
        
        # 取得 SOAP SSO 認證器
        print("🔧 取得 SOAP SSO 認證器...")
        authenticator = get_soap_sso_authenticator()
        
        if not authenticator:
            print("❌ SOAP SSO 認證器未配置")
            return False
        
        print("✅ SOAP SSO 認證器已建立")
        print(f"📍 配置的 SOAP URL: {authenticator.soap_ws_url}")
        
        # 執行認證
        print("\n🔍 開始認證流程...")
        result = await authenticator.authenticate_with_token(test_token, db)
        
        print("🎉 認證成功！")
        print(f"📋 認證結果:")
        print(f"  - Access Token: {result['access_token'][:50]}...")
        print(f"  - Token Type: {result['token_type']}")
        print(f"  - 用戶資訊: {result['user_info']}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 認證流程失敗: {str(e)}")
        print(f"🔍 錯誤類型: {type(e).__name__}")
        
        # 如果有資料庫會話，關閉它
        if 'db' in locals():
            db.close()
        
        return False

async def test_soap_service_directly():
    """直接測試 SOAP 服務呼叫"""
    print("\n🧪 直接測試 SOAP 服務呼叫")
    print("=" * 60)
    
    test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
    
    try:
        authenticator = SOAPSSOAuthenticator()
        
        print(f"📍 SOAP URL: {authenticator.soap_ws_url}")
        print(f"🔧 配置狀態: {authenticator.is_configured}")
        
        # 直接呼叫 SOAP 服務
        print("📡 呼叫 SOAP 服務...")
        user_info = await authenticator._call_soap_service(test_token)
        
        print("✅ SOAP 服務呼叫成功！")
        print(f"📋 用戶資訊: {user_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ SOAP 服務呼叫失敗: {str(e)}")
        print(f"🔍 錯誤類型: {type(e).__name__}")
        import traceback
        print(f"🔍 詳細錯誤: {traceback.format_exc()}")
        return False

async def main():
    """主要測試函數"""
    print("🔬 SOAP SSO 認證流程調試")
    print("=" * 80)
    
    # 測試直接 SOAP 服務呼叫
    soap_ok = await test_soap_service_directly()
    
    # 測試完整認證流程
    auth_ok = await test_full_auth_flow()
    
    print("\n" + "=" * 80)
    print("📊 測試結果:")
    print(f"  - SOAP 服務呼叫: {'✅ 成功' if soap_ok else '❌ 失敗'}")
    print(f"  - 完整認證流程: {'✅ 成功' if auth_ok else '❌ 失敗'}")
    
    if soap_ok and auth_ok:
        print("🎉 所有測試通過！")
        sys.exit(0)
    else:
        print("❌ 測試失敗，需要進一步調試。")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 
"""
資料庫配置和連接管理
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from sqlalchemy.exc import OperationalError
import json

# 載入環境變數（從backend目錄）
load_dotenv()

# 資料庫URL
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./database/cba_personal_data.db")

# 建立資料庫引擎
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特定設定
    echo=False  # 設為True可顯示SQL語句（開發時使用）
)

# 建立會話工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 建立基礎模型類
Base = declarative_base()


def get_db():
    """
    取得資料庫會話
    用於依賴注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """
    建立所有資料表
    """
    # 導入所有模型類以確保它們被註冊到Base中
    # 使用延遲導入避免循環導入問題
    from .user import Department, Role, User, UserRole, Permission, RolePermission  # noqa
    from .payee_data import PayeeData  # noqa
    from .audit_log import AuditLog  # noqa
    
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """
    刪除所有資料表（測試用）
    """
    Base.metadata.drop_all(bind=engine)


def init_database():
    """
    初始化資料庫
    建立表格並插入初始資料
    """
    create_tables()
    # 檢查 departments 表是否缺少 code 欄位，如果缺少則添加該欄位
    from .user import Department
    try:
        # 嘗試執行 count，如果缺少 code 欄位會拋出 OperationalError
        db = SessionLocal()
        db.query(Department).count()
    except OperationalError:
        # 添加 code 欄位，SQLite 支援 ADD COLUMN
        with engine.connect() as conn:
            conn.execute("ALTER TABLE departments ADD COLUMN code VARCHAR(50) NOT NULL DEFAULT ''")
        db.close()
        # 確保表結構更新
        create_tables()
    finally:
        try:
            db.close()
        except:
            pass
    
    # 插入初始資料
    from .user import Role, Department, Permission, RolePermission
    db = SessionLocal()
    
    try:
        # 檢查是否已有初始資料
        if db.query(Role).count() == 0:
            # 建立預設角色
            roles = [
                Role(
                    name="general",
                    description="一般帳號",
                    permissions=json.dumps(["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA"])
                ),
                Role(
                    name="global", 
                    description="全域帳號",
                    permissions=json.dumps(["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA"])
                ),
                Role(
                    name="admin",
                    description="管理者帳號", 
                    permissions=json.dumps(["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS", "VIEW_AUDIT_LOG"])
                )
            ]
            
            for role in roles:
                db.add(role)
            
        if db.query(Department).count() == 0:
            # 建立預設部門，code 使用與名稱相同以確保唯一性
            departments = [
                Department(code="資訊部", name="資訊部", description="負責系統維護與開發"),
                Department(code="人事部", name="人事部", description="負責人員管理"),
                Department(code="行政部", name="行政部", description="負責行政事務"),
            ]
            
            for dept in departments:
                db.add(dept)
                
        db.commit()
        
        # 確保權限表中有所需的權限
        permissions_data = [
            ('CREATE_PAYEE_DATA', '新增受款人資料', 'payee_data'),
            ('READ_PAYEE_DATA', '查詢受款人資料', 'payee_data'),
            ('UPDATE_PAYEE_DATA', '修改受款人資料', 'payee_data'),
            ('DELETE_PAYEE_DATA', '刪除受款人資料', 'payee_data'),
            ('READ_ALL_DATA', '查詢所有資料', 'global'),
            ('EXPORT_DATA', '匯出資料', 'global'),
            ('MANAGE_ADMIN', '管理管理者帳號', 'admin'),
            ('MANAGE_GLOBAL', '管理全域帳號', 'admin'),
            ('MANAGE_USERS', '管理用戶', 'admin'),
            ('VIEW_AUDIT_LOG', '查看審計日誌', 'admin')
        ]
        
        for perm_name, desc, category in permissions_data:
            perm = db.query(Permission).filter(Permission.name == perm_name).first()
            if not perm:
                perm = Permission(name=perm_name, description=desc, category=category, is_active=True)
                db.add(perm)
        
        db.commit()
        
        # 確保角色權限關聯表有數據 - 從JSON格式權限遷移到關聯表
        roles = db.query(Role).all()
        for role in roles:
            # 檢查角色是否已有關聯表數據
            if db.query(RolePermission).filter(RolePermission.role_id == role.id).count() == 0:
                try:
                    # 解析JSON權限
                    if role.permissions:
                        perm_names = json.loads(role.permissions)
                        for perm_name in perm_names:
                            # 查找權限ID
                            perm = db.query(Permission).filter(Permission.name == perm_name).first()
                            if perm:
                                # 建立關聯
                                role_perm = RolePermission(role_id=role.id, permission_id=perm.id)
                                db.add(role_perm)
                except Exception as e:
                    print(f"處理角色 {role.name} 的權限時發生錯誤: {e}")
        
        db.commit()
        
    except Exception as e:
        db.rollback()
        print(f"初始化資料庫時發生錯誤: {e}")
        raise
    finally:
        db.close()


def init_db(engine_instance=None):
    """
    初始化資料庫的便利函數
    用於與main.py的兼容性
    
    Args:
        engine_instance: 資料庫引擎實例（可選）
    """
    if engine_instance:
        # 如果提供了引擎實例，更新全域引擎
        global engine
        engine = engine_instance
    
    init_database() 
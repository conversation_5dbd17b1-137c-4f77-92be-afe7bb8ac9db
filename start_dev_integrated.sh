#!/bin/bash

# CBA系統 - 開發模式啟動腳本 (80埠後端 + 3000埠前端)

set -e

# 顏色定義
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 CBA系統 - 開發模式啟動${NC}"
echo "================================"

# 檢查依賴
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js未安裝${NC}"
    exit 1
fi

if ! command -v uv &> /dev/null; then
    echo -e "${RED}❌ uv未安裝${NC}"
    exit 1
fi

# 檢查80埠
if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}❌ 80埠已被佔用${NC}"
    read -p "是否要停止佔用進程？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo lsof -ti:80 | xargs sudo kill -9 2>/dev/null || true
        sleep 2
    else
        exit 1
    fi
fi

# 檢查3000埠
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}❌ 3000埠已被佔用${NC}"
    read -p "是否要停止佔用進程？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 2
    else
        exit 1
    fi
fi

# 安裝依賴
echo -e "${YELLOW}📦 檢查依賴...${NC}"

if [ ! -d "frontend-vue/node_modules" ]; then
    echo "安裝前端依賴..."
    cd frontend-vue
    npm install
    cd ..
fi

if [ ! -d "backend/.venv" ] && [ ! -f "backend/uv.lock" ]; then
    echo "安裝後端依賴..."
    cd backend
    uv sync
    cd ..
fi

echo -e "${GREEN}✅ 依賴檢查完成${NC}"

# 創建日誌目錄
mkdir -p logs

# 啟動後端 (80埠)
echo -e "${BLUE}🔧 啟動後端服務 (80埠)...${NC}"
cd backend
export ENVIRONMENT=development
export DEBUG=true
export HOST=0.0.0.0
export PORT=80

if [ "$EUID" -eq 0 ]; then
    nohup uv run uvicorn main:app --host $HOST --port $PORT --reload > ../logs/backend.log 2>&1 &
else
    nohup sudo -E uv run uvicorn main:app --host $HOST --port $PORT --reload > ../logs/backend.log 2>&1 &
fi

BACKEND_PID=$!
cd ..

# 等待後端啟動
echo "等待後端啟動..."
sleep 5

# 檢查後端是否啟動成功
if curl -f -s http://localhost:80/health > /dev/null; then
    echo -e "${GREEN}✅ 後端服務已啟動在 http://localhost:80${NC}"
else
    echo -e "${RED}❌ 後端服務啟動失敗${NC}"
    echo "查看日誌: tail -f logs/backend.log"
    exit 1
fi

# 啟動前端 (3000埠)
echo -e "${BLUE}🔧 啟動前端服務 (3000埠)...${NC}"
cd frontend-vue
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# 等待前端啟動
echo "等待前端啟動..."
sleep 8

# 檢查前端是否啟動成功
if curl -f -s http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ 前端服務已啟動在 http://localhost:3000${NC}"
else
    echo -e "${YELLOW}⚠️ 前端服務可能還在啟動中${NC}"
fi

echo ""
echo -e "${GREEN}🎉 開發環境啟動完成！${NC}"
echo ""
echo "📋 服務資訊:"
echo "  • 前端開發服務: http://localhost:3000"
echo "  • 後端API服務: http://localhost:80"
echo "  • API文檔: http://localhost:80/docs"
echo "  • 健康檢查: http://localhost:80/health"
echo ""
echo "📁 日誌檔案:"
echo "  • 後端日誌: logs/backend.log"
echo "  • 前端日誌: logs/frontend.log"
echo ""
echo "🔧 管理命令:"
echo "  • 查看後端日誌: tail -f logs/backend.log"
echo "  • 查看前端日誌: tail -f logs/frontend.log"
echo "  • 停止所有服務: ./stop_dev.sh"
echo ""

# 創建停止腳本
cat > stop_dev.sh << 'EOF'
#!/bin/bash
echo "停止開發服務..."

# 停止80埠服務
sudo lsof -ti:80 | xargs sudo kill -9 2>/dev/null || true

# 停止3000埠服務
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

echo "所有服務已停止"
EOF

chmod +x stop_dev.sh

echo -e "${YELLOW}按任意鍵退出 (服務將繼續在背景運行)${NC}"
read -n 1 -s

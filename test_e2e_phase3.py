"""
CBA人員資料調查系統 - 第三階段端到端測試
測試完整的用戶流程，包含前後端整合
"""

import os
import time
import requests
import subprocess
from datetime import datetime
import json

class E2ETestPhase3:
    """第三階段端到端測試"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:8501"
        self.backend_process = None
        self.frontend_process = None
        
    def setup_test_environment(self):
        """設定測試環境"""
        print("=== 設定測試環境 ===")
        
        # 設定環境變數
        os.environ["JWT_SECRET_KEY"] = "test-secret-key-for-phase3-testing"
        os.environ["ALGORITHM"] = "HS256"
        os.environ["ACCESS_TOKEN_EXPIRE_MINUTES"] = "1440"
        os.environ["SSO_SOAP_WS_URL"] = "https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL"
        os.environ["FRONTEND_URL"] = self.frontend_url
        
        print("✓ 環境變數設定完成")
        
    def check_backend_service(self):
        """檢查後端服務是否運行"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def test_backend_api_endpoints(self):
        """測試後端API端點"""
        print("=== 測試後端 API 端點 ===")
        
        # 首先檢查後端服務是否運行
        if not self.check_backend_service():
            print("⚠️  後端服務未運行，請先啟動後端服務")
            print("   執行: cd backend && uv run uvicorn main:app --reload")
            return False
        
        test_results = []
        
        # 測試基本端點
        endpoints = [
            ("/", "根端點"),
            ("/docs", "API文件"),
            ("/api/v1/auth/sso_login", "SOAP SSO登入端點")
        ]
        
        for endpoint, description in endpoints:
            try:
                if endpoint == "/api/v1/auth/sso_login":
                    # SSO端點沒有參數時應該返回302重導向，不跟隨重導向
                    response = requests.get(f"{self.backend_url}{endpoint}", timeout=10, allow_redirects=False)
                    if response.status_code == 302:
                        print(f"✓ {description}: {response.status_code}")
                        test_results.append(True)
                    else:
                        print(f"✗ {description}: {response.status_code}")
                        test_results.append(False)
                else:
                    response = requests.get(f"{self.backend_url}{endpoint}", timeout=10)
                    if response.status_code in [200, 307]:
                        print(f"✓ {description}: {response.status_code}")
                        test_results.append(True)
                    else:
                        print(f"✗ {description}: {response.status_code}")
                        test_results.append(False)
            except Exception as e:
                print(f"✗ {description}: {e}")
                test_results.append(False)
        
        return all(test_results)
    
    def test_sso_authentication_flow(self):
        """測試SSO認證流程"""
        print("=== 測試 SSO 認證流程 ===")
        
        try:
            # 測試SOAP SSO登入端點（無參數），不跟隨重導向
            response = requests.get(f"{self.backend_url}/api/v1/auth/sso_login", allow_redirects=False)
            
            if response.status_code == 302:
                print("✓ 無SSO Token時正確重導向到錯誤頁面")
                
                # 測試帶ssoToken1參數
                test_token = "test_sso_token_123"
                response = requests.get(f"{self.backend_url}/api/v1/auth/sso_login?ssoToken1={test_token}", allow_redirects=False)
                
                if response.status_code == 302:
                    print("✓ ssoToken1參數正確處理")
                
                # 測試帶SAMLart參數
                test_saml = "test_saml_artifact_456"
                response = requests.get(f"{self.backend_url}/api/v1/auth/sso_login?SAMLart={test_saml}", allow_redirects=False)
                
                if response.status_code == 302:
                    print("✓ SAMLart參數正確處理")
                    return True
                else:
                    print(f"✗ SAMLart參數處理失敗: {response.status_code}")
                    return False
            else:
                print(f"✗ SSO端點回應錯誤: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ SSO認證測試失敗: {e}")
            return False
    
    def run_e2e_tests(self):
        """執行端到端測試"""
        print("開始執行第三階段端到端測試...")
        print("=" * 60)
        
        test_results = []
        
        try:
            # 設定測試環境
            self.setup_test_environment()
            
            # 測試後端API
            api_test = self.test_backend_api_endpoints()
            test_results.append(api_test)
            
            # 測試SSO認證
            sso_test = self.test_sso_authentication_flow()
            test_results.append(sso_test)
            
        except Exception as e:
            print(f"測試執行過程發生錯誤: {e}")
            test_results.append(False)
        
        # 產出測試報告
        self.generate_test_report(test_results)
        
        return test_results
    
    def generate_test_report(self, test_results):
        """產生測試報告"""
        print("=" * 60)
        print("第三階段端到端測試報告")
        print("=" * 60)
        
        test_names = [
            "API端點測試", 
            "SSO認證流程"
        ]
        
        passed = 0
        total = len(test_results)
        
        for i, result in enumerate(test_results):
            if i < len(test_names):
                status = "✓ 通過" if result else "✗ 失敗"
                print(f"{test_names[i]}: {status}")
                if result:
                    passed += 1
        
        print("-" * 60)
        print(f"測試結果: {passed}/{total} 項測試通過")
        
        if passed == total:
            print("🎉 所有測試都通過！")
        else:
            print("⚠️  部分測試失敗，請檢查系統配置。")

def main():
    """主執行函數"""
    tester = E2ETestPhase3()
    results = tester.run_e2e_tests()
    return results

if __name__ == "__main__":
    main() 
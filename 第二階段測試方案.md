# CBA個人資料搜集系統 - 第二階段測試方案

## 階段概述
**階段名稱**：核心功能與權限管控  
**開發期間**：2-3週  
**測試目標**：確保核心業務功能正確實施，權限管控系統安全可靠  

## 測試範圍

### 1. 個人資料管理API測試
#### 1.1 RESTful API端點測試
- **測試項目**：API路由正確性
- **測試方法**：
  ```python
  # 測試各端點是否正確響應
  GET /api/v1/personal-data        # 查詢個人資料
  POST /api/v1/personal-data       # 建立個人資料
  PUT /api/v1/personal-data/{id}   # 更新個人資料
  DELETE /api/v1/personal-data/{id} # 刪除個人資料
  GET /api/v1/personal-data/{id}   # 取得特定個人資料
  ```
- **預期結果**：所有端點返回正確的HTTP狀態碼和響應格式

#### 1.2 資料CRUD操作測試
- **測試項目**：建立、讀取、更新、刪除操作
- **測試案例**：
  ```python
  def test_create_personal_data():
      # 建立個人資料，驗證返回正確ID和狀態
      pass
  
  def test_read_personal_data():
      # 查詢個人資料，驗證回傳資料正確性
      pass
  
  def test_update_personal_data():
      # 更新個人資料，驗證變更生效
      pass
  
  def test_delete_personal_data():
      # 刪除個人資料，驗證軟刪除或硬刪除
      pass
  ```

#### 1.3 資料分頁和查詢測試
- **測試項目**：分頁功能和查詢篩選
- **測試方法**：測試limit、offset、search等參數
- **預期結果**：分頁正確，查詢結果準確

### 2. 權限管控系統測試
#### 2.1 角色權限驗證測試
- **測試項目**：三級權限正確性
- **測試案例**：
  ```python
  def test_general_user_permissions():
      # 一般用戶：只能CRUD自己建立的資料
      assert can_create_personal_data(general_user)
      assert can_read_own_data(general_user)
      assert not can_read_all_data(general_user)
  
  def test_global_user_permissions():
      # 全域用戶：可查詢所有資料
      assert can_read_all_data(global_user)
      assert can_export_data(global_user)
  
  def test_admin_user_permissions():
      # 管理者：可管理用戶和角色
      assert can_manage_users(admin_user)
      assert can_view_audit_log(admin_user)
  ```

#### 2.2 權限檢查中間件測試
- **測試項目**：API端點權限攔截
- **測試方法**：使用不同角色用戶訪問各端點
- **預期結果**：無權限操作返回403 Forbidden

#### 2.3 資料存取權限測試
- **測試項目**：用戶只能存取有權限的資料
- **測試案例**：
  - 一般用戶無法查看其他部門資料
  - 全域用戶可查看跨部門資料
  - 管理者可存取所有管理功能

### 3. 資料驗證機制測試
#### 3.1 輸入驗證測試
- **測試項目**：請求資料格式驗證
- **測試案例**：
  ```python
  def test_invalid_id_number():
      # 測試無效身分證字號
      response = post_personal_data({"id_number": "INVALID123"})
      assert response.status_code == 400
      assert "身分證字號格式錯誤" in response.json()["detail"]
  
  def test_duplicate_id_number():
      # 測試重複身分證字號
      assert raises_duplicate_error()
  
  def test_required_fields():
      # 測試必填欄位驗證
      assert validates_required_fields()
  ```

#### 3.2 商業邏輯驗證測試
- **測試項目**：資料一致性和業務規則
- **測試方法**：
  - 身分證字號唯一性檢查
  - 必填欄位完整性驗證
  - 資料格式規範檢查

#### 3.3 錯誤處理測試
- **測試項目**：異常情況處理
- **測試案例**：
  - 資料庫連接失敗
  - 無效請求參數
  - 超大資料輸入

### 4. 加密解密功能測試
#### 4.1 身分證字號加密測試
- **測試項目**：AES-256-GCM加密正確性
- **測試案例**：
  ```python
  def test_id_number_encryption():
      original = "A123456789"
      encrypted = encrypt_id_number(original)
      decrypted = decrypt_id_number(encrypted)
      assert decrypted == original
      assert encrypted != original
  
  def test_encryption_iv_uniqueness():
      # 相同明文每次加密產生不同密文
      id_num = "A123456789"
      enc1 = encrypt_id_number(id_num)
      enc2 = encrypt_id_number(id_num)
      assert enc1 != enc2
  ```

#### 4.2 加密性能測試
- **測試項目**：批量加密效能
- **測試方法**：測試1000筆資料加密時間
- **預期結果**：單筆加密時間<10ms

#### 4.3 密鑰管理測試
- **測試項目**：密鑰安全性
- **測試方法**：驗證環境變數載入、密鑰格式檢查
- **預期結果**：密鑰正確載入且格式有效

### 5. 審計日誌系統測試
#### 5.1 操作記錄測試
- **測試項目**：CRUD操作自動記錄
- **測試案例**：
  ```python
  def test_create_operation_logging():
      # 建立資料時自動記錄審計日誌
      personal_data = create_personal_data(user, data)
      audit_log = get_latest_audit_log()
      assert audit_log.action == "CREATE"
      assert audit_log.user_id == user.id
      assert audit_log.table_name == "personal_data"
  ```

#### 5.2 敏感資料保護測試
- **測試項目**：審計日誌不記錄敏感資料
- **測試方法**：檢查日誌中身分證字號是否被遮罩
- **預期結果**：加密欄位顯示為"[ENCRYPTED]"

#### 5.3 審計日誌查詢測試
- **測試項目**：日誌查詢和篩選功能
- **測試案例**：
  - 按用戶篩選
  - 按操作類型篩選
  - 按時間範圍篩選
  - 分頁查詢

### 6. API整合測試
#### 6.1 完整流程測試
- **測試項目**：端到端業務流程
- **測試案例**：
  ```python
  def test_complete_workflow():
      # 1. 用戶登入取得Token
      token = login_user(username, password)
      
      # 2. 建立個人資料
      personal_data = create_personal_data(token, data)
      
      # 3. 查詢資料
      retrieved_data = get_personal_data(token, personal_data.id)
      
      # 4. 更新資料
      updated_data = update_personal_data(token, personal_data.id, new_data)
      
      # 5. 驗證審計日誌
      audit_logs = get_audit_logs(token)
      assert len(audit_logs) >= 3  # CREATE, READ, UPDATE
  ```

#### 6.2 併發測試
- **測試項目**：多用戶併發操作
- **測試方法**：模擬10個用戶同時操作
- **預期結果**：無資料競爭，事務隔離正確

#### 6.3 效能測試
- **測試項目**：API響應時間
- **測試指標**：
  - 單筆查詢 < 100ms
  - 批量查詢(100筆) < 500ms
  - 建立操作 < 200ms

## 測試環境設定
```python
# 測試專用環境變數
TEST_DATABASE_URL = "sqlite:///./database/test_phase2_cba.db"
TEST_ENCRYPTION_KEY = "dGVzdGVuY3J5cHRpb25rZXkzMmJ5dGVzbGVuZ3Rob2s="
TEST_JWT_SECRET = "test-jwt-secret-phase2"

# 測試用戶設定
TEST_USERS = {
    "general_user": {"username": "test_general", "role": "general"},
    "global_user": {"username": "test_global", "role": "global"}, 
    "admin_user": {"username": "test_admin", "role": "admin"}
}
```

## 測試資料準備
```python
# 測試用個人資料
TEST_PERSONAL_DATA = [
    {
        "name": "張測試",
        "id_number": "A123456789",
        "address": "台北市信義區測試路123號",
        "notes": "測試用資料1"
    },
    {
        "name": "李測試",
        "id_number": "B123456781", 
        "address": "高雄市前鎮區測試街456號",
        "notes": "測試用資料2"
    }
]
```

## 測試執行計畫
1. **單元測試**：各功能模組獨立測試
2. **整合測試**：模組間協作測試
3. **API測試**：端點功能完整性測試
4. **安全測試**：權限和加密安全性測試
5. **效能測試**：系統響應時間測試

## 成功標準
- [ ] 所有API端點正常運作
- [ ] 三級權限管控正確實施
- [ ] 身分證字號加密保護有效
- [ ] 審計日誌完整記錄
- [ ] 資料驗證機制可靠
- [ ] 系統效能符合要求
- [ ] 安全測試通過
- [ ] 測試覆蓋率 > 90%

## 風險評估
- **高風險**：加密功能失效、權限繞過
- **中風險**：效能不達標、資料驗證漏洞  
- **低風險**：API介面調整、日誌格式變更

## 測試交付物
- 第二階段測試報告
- API測試結果文件
- 安全測試證明
- 效能測試報告
- 缺陷修復記錄 
# CBA受款人資料搜集系統 - Vue.js前端 + FastAPI後端整合nginx配置

# 後端API伺服器
upstream cba_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

# HTTP重定向到HTTPS (生產環境)
server {
    listen 80;
    server_name localhost;
    
    # 開發環境直接提供HTTP服務
    # 生產環境請取消註釋下面的重定向配置
    # return 301 https://$server_name$request_uri;
    
    # 開發環境配置
    root /var/www/html/cba-system;
    index index.html;
    
    # 客戶端設定
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 後端API代理
    location /api/ {
        proxy_pass http://cba_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # CORS設定
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "*";
        
        # 處理OPTIONS請求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "*";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 200;
        }
    }
    
    # 健康檢查端點
    location /health {
        proxy_pass http://cba_backend/health;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # 靜態資源快取設定
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        access_log off;
        
        # 如果檔案不存在，不要fallback到index.html
        try_files $uri =404;
    }
    
    # Vue.js SPA路由處理
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全標頭
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        
        # CSP設定 (根據需要調整)
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' http://localhost:8080; font-src 'self' data:;";
    }
    
    # 禁止存取敏感檔案
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|config|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 錯誤頁面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # 安全性設定
    server_tokens off;
    
    # 日誌設定
    access_log /var/log/nginx/cba-vue.access.log;
    error_log /var/log/nginx/cba-vue.error.log;
}

# HTTPS配置 (生產環境)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     root /var/www/html/cba-system;
#     index index.html;
#     
#     # SSL憑證設定
#     ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
#     
#     # SSL安全設定
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 1d;
#     ssl_stapling on;
#     ssl_stapling_verify on;
#     
#     # 安全標頭
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     add_header X-Frame-Options DENY;
#     add_header X-Content-Type-Options nosniff;
#     add_header X-XSS-Protection "1; mode=block";
#     add_header Referrer-Policy "strict-origin-when-cross-origin";
#     
#     # 其他配置與HTTP相同...
# }

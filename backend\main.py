"""
CBA受款人資料搜集系統 - 主應用程式
"""
from fastapi import FastAPI, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import logging
from contextlib import asynccontextmanager
import time
import os
from pathlib import Path

from app.api.v1 import api_router
from app.models.database import init_db, engine
from app.core.config import settings
from app.core.exceptions import CBAException
from app.core.middleware import (
    UnifiedErrorHandlingMiddleware,
    SecurityHeadersMiddleware,
    RequestLoggingMiddleware,
    AuditLoggingMiddleware,
    RateLimitMiddleware
)


# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程式生命週期管理"""
    # 啟動時初始化資料庫
    logger.info("正在初始化資料庫...")
    try:
        init_db(engine)
        logger.info("資料庫初始化完成")
    except Exception as e:
        logger.error(f"資料庫初始化失敗: {e}")
        raise
    
    yield
    
    # 關閉時清理資源
    logger.info("正在關閉應用程式...")


# 建立FastAPI應用程式實例
app = FastAPI(
    title="CBA受款人資料搜集系統",
    description="提供受款人資料管理、權限控制、資料加密等功能的API服務",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中間件（順序很重要，先加的後執行）
# 1. 統一錯誤處理（最後執行，捕獲所有錯誤）
app.add_middleware(UnifiedErrorHandlingMiddleware)

# 2. 安全標頭中間件
app.add_middleware(SecurityHeadersMiddleware)

# 3. 請求日誌中間件
app.add_middleware(RequestLoggingMiddleware)

# 4. 審計日誌中間件
app.add_middleware(AuditLoggingMiddleware)

# 5. 速率限制中間件（僅生產環境）
if settings.ENVIRONMENT == "production":
    app.add_middleware(RateLimitMiddleware, calls=100, period=60)

# 6. CORS中間件 - 支援Vue.js前端
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# 7. 信任主機中間件（生產環境安全）
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )


# 健康檢查端點
@app.get("/health", tags=["系統狀態"])
async def health_check():
    """系統健康檢查"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "service": "CBA受款人資料搜集系統",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT
        },
        "timestamp": time.time()
    }





# 註冊API路由
app.include_router(api_router)

# 靜態檔案服務 - Vue.js前端
# 檢查前端建構檔案是否存在
frontend_dist_path = Path(__file__).parent.parent / "frontend-vue" / "dist"
if frontend_dist_path.exists():
    # 掛載靜態檔案
    app.mount("/assets", StaticFiles(directory=str(frontend_dist_path / "assets")), name="assets")

    # 處理SPA路由 - 所有非API請求都返回index.html
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        """
        服務Vue.js SPA應用
        所有非API路由都返回index.html，讓Vue Router處理前端路由
        """
        # 如果是API請求，不處理
        if full_path.startswith("api/") or full_path.startswith("docs") or full_path.startswith("health"):
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "API端點不存在"}
            )

        # 檢查是否為靜態檔案請求
        if "." in full_path.split("/")[-1]:  # 有副檔名的檔案
            static_file_path = frontend_dist_path / full_path
            if static_file_path.exists():
                from fastapi.responses import FileResponse
                return FileResponse(static_file_path)
            else:
                return JSONResponse(
                    status_code=404,
                    content={"success": False, "message": "檔案不存在"}
                )

        # 返回index.html給SPA路由
        index_path = frontend_dist_path / "index.html"
        if index_path.exists():
            from fastapi.responses import FileResponse
            return FileResponse(index_path)
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "success": False,
                    "message": "前端應用未建構，請執行 npm run build"
                }
            )
else:
    logger.warning("前端建構檔案不存在，僅提供API服務")

    # 修改根路徑回應，提供前端建構指引
    @app.get("/")
    async def root_no_frontend():
        """根路徑資訊 - 無前端版本"""
        return {
            "success": True,
            "data": {
                "message": "CBA受款人資料搜集系統API",
                "version": "1.0.0",
                "docs": "/docs",
                "health": "/health",
                "notice": "前端應用未建構，請執行: cd frontend-vue && npm run build"
            },
            "timestamp": time.time()
        }


if __name__ == "__main__":
    # 開發環境啟動設定
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    ) 
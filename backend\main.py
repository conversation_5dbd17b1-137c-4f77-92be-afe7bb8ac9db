"""
CBA受款人資料搜集系統 - 主應用程式
"""
from fastapi import FastAPI, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import logging
from contextlib import asynccontextmanager
import time
import os
from pathlib import Path

from app.api.v1 import api_router
from app.frontend.routes import frontend_router
from app.models.database import init_db, engine
from app.core.config import settings
from app.core.exceptions import CBAException
from app.core.middleware import (
    UnifiedErrorHandlingMiddleware,
    SecurityHeadersMiddleware,
    RequestLoggingMiddleware,
    AuditLoggingMiddleware,
    RateLimitMiddleware
)


# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程式生命週期管理"""
    # 啟動時初始化資料庫
    logger.info("正在初始化資料庫...")
    try:
        init_db(engine)
        logger.info("資料庫初始化完成")
    except Exception as e:
        logger.error(f"資料庫初始化失敗: {e}")
        raise
    
    yield
    
    # 關閉時清理資源
    logger.info("正在關閉應用程式...")


# 建立FastAPI應用程式實例
app = FastAPI(
    title="CBA受款人資料搜集系統",
    description="提供受款人資料管理、權限控制、資料加密等功能的API服務",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中間件（順序很重要，先加的後執行）
# 1. 統一錯誤處理（最後執行，捕獲所有錯誤）
app.add_middleware(UnifiedErrorHandlingMiddleware)

# 2. 安全標頭中間件
app.add_middleware(SecurityHeadersMiddleware)

# 3. 請求日誌中間件
app.add_middleware(RequestLoggingMiddleware)

# 4. 審計日誌中間件
app.add_middleware(AuditLoggingMiddleware)

# 5. 速率限制中間件（僅生產環境）
if settings.ENVIRONMENT == "production":
    app.add_middleware(RateLimitMiddleware, calls=100, period=60)

# 6. CORS中間件 - 支援Vue.js前端
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# 7. 信任主機中間件（生產環境安全）
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )


# 健康檢查端點
@app.get("/health", tags=["系統狀態"])
async def health_check():
    """系統健康檢查"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "service": "CBA受款人資料搜集系統",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT
        },
        "timestamp": time.time()
    }





# 註冊API路由
app.include_router(api_router)

# 註冊前端路由
app.include_router(frontend_router)

# 掛載靜態檔案
static_path = Path(__file__).parent / "app" / "frontend" / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
else:
    logger.warning(f"靜態檔案目錄不存在: {static_path}")


if __name__ == "__main__":
    # 開發環境啟動設定
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    ) 
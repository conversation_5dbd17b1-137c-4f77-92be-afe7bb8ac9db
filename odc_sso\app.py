from flask import Flask, request, render_template, session, redirect, url_for, jsonify
import requests
from datetime import datetime
import json
import logging
import os
from functools import wraps
from config import Config
from zeep import Client

# 建立 logs 目錄
if not os.path.exists('logs'):
    os.makedirs('logs')

# 初始化 Flask
app = Flask(__name__)
app.config.from_object(Config)
app.secret_key = app.config['SECRET_KEY']  # 確保 Config 中有 SECRET_KEY

# 設定日誌
logging.basicConfig(
    filename=os.path.join('logs', f'app_{datetime.now().strftime("%Y%m%d")}.log'),
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_info' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET'])
def login():
    print(f"login: {request.args}")
    try:
        artifact = request.args.get('SAMLart') or request.args.get('ssoToken1')
        print(f"artifact: {artifact}")
        if not artifact:
            return render_template('error.html', message="錯誤：未收到 SSO Token，請重新登入")

        logging.info(f"收到 SSO Token: {artifact}")

        # 使用 SOAP 呼叫 getUserProfile
        try:
            client = Client(app.config['SSO_CONFIG']['soap_ws_url'])
            result = client.service.getUserProfile(artifact)
            print(f"result: {result}")
            logging.info(f"SOAP 呼叫結果: {result}")
            
            if result:  # 如果成功獲取用戶信息
                # 解析 XML 格式的 result
                from xml.etree import ElementTree as ET
                root = ET.fromstring(result)
                if root.tag == 'Error':
                    user_info = {
                        '帳號': '',
                        '姓名': '',
                        'source_org_no': ''
                    }
                else:
                    # 讀取帳號及姓名
                    account = root.find('帳號').text
                    name = root.find('姓名').text
                    org_info = f"{root.find('機關名稱').text}:{root.find('機關代碼').text}:{root.find('單位代碼').text}"
                    
                    user_info = {
                        '帳號': account,
                        '姓名': name,
                        'source_org_no': org_info,
                        'ssoToken1': artifact   
                    }
                
                session['user_info'] = user_info
                session['artifact'] = artifact
                session.permanent = True  # 設置 session 持久化
                
                #print(f"設置 session 後的內容: {session}")
                logging.info(f"設置 session 後的內容: {session}")
                #print(f"session: {session}")
                return redirect(url_for('index'))
            else:
                return render_template('error.html', 
                                     message="無法取得使用者資訊")
                
        except Exception as soap_error:
            logging.error(f"SOAP 呼叫失敗: {str(soap_error)}")
            return render_template('error.html', 
                                 message=f"SOAP 呼叫失敗: {str(soap_error)}")

    except Exception as e:
        logging.error(f"登入過程發生錯誤: {str(e)}")
        return render_template('error.html', 
                             message=f"系統錯誤: {str(e)}")

@app.route('/')
@login_required
def index():
    #print(f"所有 session 內容: {session}")
    #print(f"所有 cookies: {request.cookies}")
    user_info = session.get('user_info', {})
    #print(f"user_info: {user_info}")
    return render_template('index.html', user_info=user_info)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/api/user_info')
@login_required
def get_user_info():
    return jsonify(session.get('user_info', {}))

if __name__ == '__main__':
    print("app.py 啟動")
    app.run(debug=True, host='************', port=80)
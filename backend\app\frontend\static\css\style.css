/* CBA系統自定義樣式 */

:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* 全域樣式 */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fc;
}

/* 導航欄樣式 */
.navbar-brand {
    font-weight: 800;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: rgba(255, 255, 255, 1) !important;
}

/* 卡片樣式 */
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: none;
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* 按鈕樣式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* 表格樣式 */
.table {
    color: #5a5c69;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 表單樣式 */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 徽章樣式 */
.badge {
    font-weight: 600;
    border-radius: 0.35rem;
}

/* 分頁樣式 */
.pagination .page-link {
    border-radius: 0.35rem;
    margin: 0 0.1rem;
    border: 1px solid #d1d3e2;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 統計卡片樣式 */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

/* 文字樣式 */
.text-xs {
    font-size: 0.7rem;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* 陰影效果 */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

/* 登入頁面樣式 */
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 動畫效果 */
.btn {
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.card {
    transition: all 0.15s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* 載入動畫 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 0.5rem;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示樣式 */
.tooltip {
    font-size: 0.75rem;
}

/* 模態框樣式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* 警告框樣式 */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 600;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 輸入群組樣式 */
.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #d1d3e2;
    color: #5a5c69;
}

/* 下拉選單樣式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    font-weight: 600;
    color: #5a5c69;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: var(--primary-color);
}

/* 進度條樣式 */
.progress {
    height: 0.5rem;
    border-radius: 0.35rem;
}

.progress-bar {
    border-radius: 0.35rem;
}

/* 列表群組樣式 */
.list-group-item {
    border: 1px solid #e3e6f0;
    color: #5a5c69;
}

.list-group-item:first-child {
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0.35rem;
    border-bottom-right-radius: 0.35rem;
}

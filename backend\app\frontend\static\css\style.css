/* CBA系統優化樣式 - 現代化設計 */

:root {
    /* 主要色彩系統 - 更現代化的配色 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --secondary-color: #64748b;
    --success-color: #059669;
    --success-light: #d1fae5;
    --info-color: #0891b2;
    --info-light: #cffafe;
    --warning-color: #d97706;
    --warning-light: #fef3c7;
    --danger-color: #dc2626;
    --danger-light: #fee2e2;

    /* 中性色系 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* 背景色系 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* 文字色系 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;

    /* 陰影系統 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* 圓角系統 */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* 間距系統 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字體系統 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* 行高系統 */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;

    /* 轉場動畫 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* 全域樣式優化 */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 改善文字可讀性 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: var(--leading-tight);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-md);
}

/* 連結樣式改進 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* 導航欄樣式優化 */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
    box-shadow: var(--shadow-md);
    border: none;
    padding: var(--spacing-md) 0;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: white !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: transform var(--transition-fast);
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: white !important;
}

.navbar-brand i {
    font-size: var(--font-size-2xl);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.navbar-nav {
    gap: var(--spacing-sm);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius);
    transition: all var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

.navbar-nav .nav-link i {
    font-size: var(--font-size-sm);
}

/* 下拉選單優化 */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-xs);
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
}

.dropdown-item {
    font-weight: 500;
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dropdown-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
    transform: translateX(4px);
}

.dropdown-item i {
    font-size: var(--font-size-sm);
    width: 16px;
    text-align: center;
}

/* 響應式導航 */
.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--radius);
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* 卡片樣式優化 */
.card {
    background: var(--bg-primary);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--gray-300);
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-header h6 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.card-header i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.card-body {
    padding: var(--spacing-xl);
}

.card-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
}

/* 統計卡片特殊樣式 */
.card.border-left-primary {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--primary-light) 100%);
}

.card.border-left-success {
    border-left: 4px solid var(--success-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--success-light) 100%);
}

.card.border-left-info {
    border-left: 4px solid var(--info-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--info-light) 100%);
}

.card.border-left-warning {
    border-left: 4px solid var(--warning-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--warning-light) 100%);
}

/* 按鈕樣式優化 */
.btn {
    border-radius: var(--radius);
    font-weight: 500;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid transparent;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 主要按鈕 */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%);
    border-color: var(--primary-hover);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* 次要按鈕 */
.btn-secondary {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--text-secondary);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-200);
    border-color: var(--gray-400);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* 成功按鈕 */
.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    transform: translateY(-1px);
    color: white;
}

/* 危險按鈕 */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #b91c1c 100%);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-1px);
    color: white;
}

/* 輪廓按鈕 */
.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* 按鈕尺寸 */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-md);
}

/* 按鈕群組 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
}

/* 載入狀態 */
.btn .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    margin-right: var(--spacing-xs);
}

/* 表格樣式優化 */
.table-responsive {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    background: var(--bg-primary);
}

.table {
    color: var(--text-primary);
    margin-bottom: 0;
    font-size: var(--font-size-sm);
}

.table th {
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
    font-weight: 600;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    padding: var(--spacing-lg);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.table td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.table-hover tbody tr {
    transition: all var(--transition-fast);
}

.table-hover tbody tr:hover {
    background-color: var(--gray-50);
    transform: scale(1.01);
    box-shadow: var(--shadow-sm);
}

/* 表格內的徽章和狀態 */
.table .badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

/* 表格內的按鈕群組 */
.table .btn-group-sm .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-sm);
}

/* 表格排序指示器 */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th.sortable:hover {
    background-color: var(--gray-200);
}

.table th.sortable::after {
    content: '↕';
    position: absolute;
    right: var(--spacing-sm);
    opacity: 0.5;
    font-size: var(--font-size-xs);
}

.table th.sortable.asc::after {
    content: '↑';
    opacity: 1;
    color: var(--primary-color);
}

.table th.sortable.desc::after {
    content: '↓';
    opacity: 1;
    color: var(--primary-color);
}

/* 空狀態樣式 */
.table-empty {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.table-empty i {
    font-size: var(--font-size-4xl);
    color: var(--gray-300);
    margin-bottom: var(--spacing-md);
    display: block;
}

.table-empty h4 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.table-empty p {
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

/* 表單樣式優化 */
.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-label i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.form-label .text-danger {
    margin-left: var(--spacing-xs);
}

.form-control {
    border-radius: var(--radius);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-control:hover:not(:disabled):not(:focus) {
    border-color: var(--gray-400);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: var(--gray-100);
    opacity: 0.7;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc2626'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc2626' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-valid {
    border-color: var(--success-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.invalid-feedback::before {
    content: "⚠️";
    font-size: var(--font-size-xs);
}

/* 表單群組間距 */
.mb-3 {
    margin-bottom: var(--spacing-xl) !important;
}

/* 表單區段標題 */
.form-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--gray-200);
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-section i {
    font-size: var(--font-size-lg);
}

/* 輸入群組 */
.input-group {
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius);
}

.input-group-text {
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
}

/* 表單檢查 */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0;
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
}

/* 表單選擇 */
.form-select {
    border-radius: var(--radius);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    appearance: none;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

/* 徽章樣式 */
.badge {
    font-weight: 600;
    border-radius: 0.35rem;
}

/* 分頁樣式 */
.pagination .page-link {
    border-radius: 0.35rem;
    margin: 0 0.1rem;
    border: 1px solid #d1d3e2;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 統計卡片樣式 */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

/* 文字樣式 */
.text-xs {
    font-size: 0.7rem;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* 陰影效果 */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

/* 登入頁面樣式 */
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 響應式設計優化 */
@media (max-width: 1200px) {
    .container-fluid {
        padding-left: var(--spacing-lg);
        padding-right: var(--spacing-lg);
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .btn-lg {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .table th,
    .table td {
        padding: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }

    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .card-header {
        padding: var(--spacing-md);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-xs);
    }

    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
    }

    .table-responsive {
        font-size: var(--font-size-xs);
        border-radius: var(--radius);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
        white-space: nowrap;
    }

    .navbar-nav {
        padding-top: var(--spacing-md);
        gap: var(--spacing-xs);
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-md) !important;
        border-radius: var(--radius);
        margin-bottom: var(--spacing-xs);
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: 1px solid var(--gray-200);
        margin-top: var(--spacing-sm);
    }

    /* 表單在行動裝置上的優化 */
    .form-control,
    .form-select {
        font-size: 16px; /* 防止iOS縮放 */
        padding: var(--spacing-md);
    }

    .form-label {
        font-size: var(--font-size-sm);
        font-weight: 600;
    }

    /* 統計卡片堆疊 */
    .row .col-xl-3,
    .row .col-md-6 {
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 576px) {
    html {
        font-size: 13px;
    }

    .container-fluid {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }

    .card-body {
        padding: var(--spacing-sm);
    }

    .btn-sm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .table th,
    .table td {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }

    .navbar-brand {
        font-size: var(--font-size-lg);
    }

    /* 隱藏不重要的表格欄位 */
    .table .d-none-mobile {
        display: none !important;
    }

    /* 表單欄位全寬 */
    .row .col-md-6 {
        margin-bottom: var(--spacing-md);
    }
}

/* 無障礙性改進 */
/* 跳過連結 */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    border-radius: var(--radius);
    z-index: 1000;
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
    color: white;
}

/* 焦點指示器 */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-primary: #000000;
        --bg-primary: #ffffff;
        --gray-200: #000000;
    }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 螢幕閱讀器專用 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* 動畫效果優化 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 載入動畫 */
.loading {
    animation: pulse 2s infinite;
}

.spinner {
    animation: spin 1s linear infinite;
}

/* 頁面載入動畫 */
.page-enter {
    animation: fadeIn 0.3s ease-out;
}

.card-enter {
    animation: fadeIn 0.5s ease-out;
}

/* 懸停效果 */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 成功/錯誤狀態動畫 */
.success-flash {
    animation: successFlash 0.6s ease-out;
}

.error-shake {
    animation: errorShake 0.5s ease-out;
}

@keyframes successFlash {
    0% { background-color: var(--success-light); }
    100% { background-color: transparent; }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 進度指示器 */
.progress-bar {
    transition: width var(--transition-slow) ease-out;
}

/* 工具提示動畫 */
.tooltip {
    animation: fadeIn 0.2s ease-out;
}

/* 模態框動畫 */
.modal.fade .modal-dialog {
    transition: transform var(--transition-normal) ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: translate(0, 0);
}

/* 統計卡片特殊樣式 */
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

.card.border-left-primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.card.border-left-success .stat-icon {
    background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
}

.card.border-left-info .stat-icon {
    background: linear-gradient(135deg, var(--info-color) 0%, #0e7490 100%);
}

.card.border-left-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color) 0%, #b45309 100%);
}

.card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

/* 文字樣式增強 */
.tracking-wide {
    letter-spacing: 0.05em;
}

.text-xs {
    font-size: var(--font-size-xs);
}

.font-weight-bold {
    font-weight: 600;
}

.text-gray-800 {
    color: var(--gray-800);
}

/* 快速操作卡片 */
.quick-action-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--gray-50) 100%);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--text-primary);
    display: block;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
}

.quick-action-card i {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    transition: all var(--transition-fast);
}

.quick-action-card:hover i {
    transform: scale(1.1);
    color: var(--primary-hover);
}

.quick-action-card h6 {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.quick-action-card p {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: 0;
}

/* 最近活動列表 */
.activity-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.activity-item:hover {
    background-color: var(--gray-50);
    transform: translateX(5px);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    margin-right: var(--spacing-md);
}

.activity-icon.bg-primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.activity-icon.bg-success {
    background-color: var(--success-light);
    color: var(--success-color);
}

.activity-icon.bg-warning {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.activity-content h6 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.activity-content p {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: 0;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    white-space: nowrap;
}

/* 波紋效果 */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 鍵盤焦點增強 */
.keyboard-focused {
    outline: 3px solid var(--primary-color) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 5px rgba(37, 99, 235, 0.2) !important;
}

/* 懶載入圖片 */
img.lazy {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

img.lazy.loaded {
    opacity: 1;
}

/* 載入骨架屏 */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 滾動條美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius);
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 選擇文字樣式 */
::selection {
    background: var(--primary-light);
    color: var(--primary-color);
}

::-moz-selection {
    background: var(--primary-light);
    color: var(--primary-color);
}

/* 打印樣式 */
@media print {
    .navbar,
    .btn,
    .dropdown,
    footer,
    .no-print {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --bg-tertiary: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
    }

    .card {
        background: var(--bg-primary);
        border-color: var(--gray-200);
    }

    .navbar {
        background: var(--bg-tertiary) !important;
    }
}

/* 高對比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --success-color: #008000;
        --warning-color: #ff8c00;
        --danger-color: #ff0000;
        --text-primary: #000000;
        --bg-primary: #ffffff;
    }

    .btn {
        border: 2px solid currentColor !important;
    }

    .card {
        border: 2px solid #000000 !important;
    }
}

/* 載入動畫 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 0.5rem;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示樣式 */
.tooltip {
    font-size: 0.75rem;
}

/* 模態框樣式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* 警告框樣式 */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 600;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 輸入群組樣式 */
.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #d1d3e2;
    color: #5a5c69;
}

/* 下拉選單樣式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    font-weight: 600;
    color: #5a5c69;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: var(--primary-color);
}

/* 進度條樣式 */
.progress {
    height: 0.5rem;
    border-radius: 0.35rem;
}

.progress-bar {
    border-radius: 0.35rem;
}

/* 列表群組樣式 */
.list-group-item {
    border: 1px solid #e3e6f0;
    color: #5a5c69;
}

.list-group-item:first-child {
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0.35rem;
    border-bottom-right-radius: 0.35rem;
}

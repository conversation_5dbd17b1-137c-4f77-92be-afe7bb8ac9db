/**
 * 受款人相關類型定義
 */

export interface Payee {
  id: string
  name: string
  id_number: string
  phone?: string
  email?: string
  address?: string
  bank_name?: string
  bank_code?: string
  account_number?: string
  account_name?: string
  department_id?: string
  department_name?: string
  notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
  created_by: string
  updated_by?: string
}

export interface PayeeCreateRequest {
  name: string
  id_number: string
  phone?: string
  email?: string
  address?: string
  bank_name?: string
  bank_code?: string
  account_number?: string
  account_name?: string
  department_id?: string
  notes?: string
}

export interface PayeeUpdateRequest extends Partial<PayeeCreateRequest> {
  id: string
}

export interface PayeeQueryParams {
  page?: number
  size?: number
  search?: string
  department_id?: string
  is_active?: boolean
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface PayeeListResponse {
  data: Payee[]
  total: number
  page: number
  size: number
  pages: number
}

export interface PayeeStatistics {
  total_count: number
  active_count: number
  inactive_count: number
  department_distribution: Array<{
    department_name: string
    count: number
  }>
  recent_additions: number
  monthly_trend: Array<{
    month: string
    count: number
  }>
}

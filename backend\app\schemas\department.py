from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class DepartmentBase(BaseModel):
    """部門基本模型"""
    code: str
    name: str
    description: Optional[str] = None


class DepartmentCreate(DepartmentBase):
    """建立部門請求模型"""
    pass


class DepartmentUpdate(BaseModel):
    """更新部門請求模型"""
    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class DepartmentResponse(BaseModel):
    """部門回應模型"""
    id: int
    code: str
    name: str
    description: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True 
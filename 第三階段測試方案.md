# CBA人員資料調查系統 - 第三階段測試方案

## 測試目標
完成SSO認證實作和真實API連接後的系統整合測試，確保所有功能在生產環境中正常運作。

## 測試範圍

### 1. SOAP SSO認證流程測試
#### 1.1 SOAP SSO登入處理
- 測試 `/api/v1/auth/sso_login` 端點
- 驗證SAMLart和ssoToken1參數接收
- 檢查SOAP Web Service呼叫
- 確認用戶資料解析和處理

#### 1.2 用戶資料處理
- 驗證XML格式用戶資料解析
- 檢查用戶建立/更新邏輯
- 確認部門自動對應功能

#### 1.3 JWT Token管理
- 驗證token生成和簽名
- 測試token過期和刷新機制
- 檢查會話持久性

### 2. API整合測試
#### 2.1 認證API
- 登入/登出功能
- 用戶資訊取得
- 權限驗證

#### 2.2 個人資料API
- CRUD操作 (Create, Read, Update, Delete)
- 搜尋和篩選功能
- 分頁處理
- 身分證字號驗證

#### 2.3 審計日誌API
- 操作記錄生成
- 日誌查詢和顯示
- 權限控制檢查

### 3. 權限控制測試
#### 3.1 角色權限驗證
- 一般用戶 (general_user): 建立和查看自己的資料
- 全域用戶 (global_user): 查看所有資料、統計報表、資料匯出
- 管理者 (admin): 完整系統管理、用戶管理、審計日誌、資料刪除

#### 3.2 功能存取控制
- 頁面級權限檢查
- API呼叫權限驗證
- 動態導航選單生成

### 4. 前端整合測試
#### 4.1 主程式 (main.py)
- SSO登入流程完整性
- 系統健康檢查
- 概覽統計顯示
- 動態權限控制

#### 4.2 個人資料表單 (personal_data_form.py)
- 表單驗證強化
- 真實API提交
- 身分證字號重複檢查
- 錯誤處理和用戶反饋

#### 4.3 資料查詢 (data_query.py)
- 關鍵字搜尋
- 篩選功能
- 分頁控制
- CSV匯出
- 資料刪除功能

### 5. 資料安全測試
#### 5.1 資料加密
- 身分證字號加密儲存
- 敏感資料遮罩顯示

#### 5.2 存取控制
- JWT token驗證
- 會話管理
- 權限檢查

### 6. 錯誤處理測試
#### 6.1 後端錯誤處理
- API錯誤響應
- 資料驗證錯誤
- 資料庫連接錯誤

#### 6.2 前端錯誤處理
- 網路連接錯誤
- 會話過期處理
- 用戶輸入驗證

## 測試執行計劃

### 階段一：後端API測試
1. 啟動後端服務
2. 執行API單元測試
3. 測試SSO認證流程
4. 驗證權限控制機制

### 階段二：前端整合測試
1. 啟動前端應用
2. 測試UI組件功能
3. 驗證前後端API連接
4. 檢查用戶體驗流程

### 階段三：端到端測試
1. 完整用戶流程測試
2. 不同角色權限測試
3. 資料操作完整性測試
4. 安全性驗證

### 階段四：壓力測試
1. 並發用戶測試
2. 大量資料處理測試
3. 系統效能評估

## 測試環境準備

### 必要條件
- Python 3.11+
- SQLite3資料庫
- JWT密鑰配置
- SOAP SSO認證配置
- 測試資料準備
- zeep庫 (SOAP客戶端)

### 環境變數
```bash
JWT_SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440
SSO_SOAP_WS_URL=https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL
FRONTEND_URL=http://localhost:8501
```

## 預期結果

### 成功標準
- 所有API端點正常回應
- SOAP SSO登入流程順利完成
- 權限控制正確實施
- 資料操作功能完整
- 審計日誌正確記錄
- 錯誤處理妥善
- UI/UX符合需求

### 效能指標
- API回應時間 < 1秒
- 頁面載入時間 < 3秒
- 資料查詢效率良好
- 系統穩定性高

## 測試工具

### 自動化測試
- pytest: 後端API測試
- requests: HTTP請求測試
- 自定義測試腳本: 端到端測試

### 手動測試
- 瀏覽器測試: UI功能驗證
- 權限測試: 不同角色登入測試
- 資料驗證: 完整性檢查

## 測試報告

測試完成後將產出：
1. 測試執行結果報告
2. 功能驗證清單
3. 發現問題列表
4. 效能評估報告
5. 安全性檢查報告

## 風險與緩解

### 主要風險
- SOAP SSO認證配置錯誤
- SOAP Web Service連接失敗
- API整合失敗
- 權限控制漏洞
- 資料安全問題

### 緩解措施
- 詳細的配置檢查
- 逐步測試驗證
- 安全性審查
- 錯誤處理強化 
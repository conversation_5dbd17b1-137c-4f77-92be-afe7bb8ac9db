#!/usr/bin/env python3
"""
測試新的受款人管理UI
"""

import sys
import os

def test_import_new_component():
    """測試新組件的導入"""
    print("🧪 測試新的受款人管理組件導入...")
    
    try:
        # 添加路徑
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        # 測試導入新組件
        from components.payee_management import render_payee_management_page
        print("✅ 成功導入 render_payee_management_page")
        
        # 測試導入驗證函數
        from components.payee_management import validate_id_number
        print("✅ 成功導入 validate_id_number")
        
        # 測試身分證字號驗證
        test_cases = [
            ("A123456789", True),
            ("B234567890", True),
            ("123456789", False),  # 缺少英文字母
            ("A12345678", False),  # 長度不足
            ("A1234567890", False), # 長度過長
            ("", False),  # 空字串
        ]
        
        print("\n🔍 測試身分證字號驗證功能...")
        for id_number, expected in test_cases:
            is_valid, error_msg = validate_id_number(id_number)
            status = "✅" if (is_valid == expected) else "❌"
            print(f"{status} {id_number or '(空)'}: {is_valid} - {error_msg}")
        
        print("\n✅ 所有組件導入測試通過！")
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        return False

def test_ui_structure():
    """測試UI結構"""
    print("\n📋 檢查UI結構...")
    
    try:
        # 檢查新組件檔案是否存在
        component_file = os.path.join(os.path.dirname(__file__), 'frontend', 'components', 'payee_management.py')
        if os.path.exists(component_file):
            print("✅ 新的受款人管理組件檔案存在")
            
            # 檢查檔案大小
            file_size = os.path.getsize(component_file)
            print(f"✅ 組件檔案大小: {file_size} bytes")
            
            if file_size > 1000:  # 至少應該有一些內容
                print("✅ 組件檔案內容充足")
            else:
                print("⚠️ 組件檔案內容可能不完整")
        else:
            print("❌ 新的受款人管理組件檔案不存在")
            return False
        
        # 檢查舊組件是否還存在（應該保留作為備份）
        old_files = [
            'frontend/components/payee_data_form.py',
            'frontend/components/data_query.py'
        ]
        
        for old_file in old_files:
            full_path = os.path.join(os.path.dirname(__file__), old_file)
            if os.path.exists(full_path):
                print(f"✅ 舊組件保留: {old_file}")
            else:
                print(f"⚠️ 舊組件不存在: {old_file}")
        
        print("✅ UI結構檢查完成")
        return True
        
    except Exception as e:
        print(f"❌ UI結構檢查失敗: {str(e)}")
        return False

def test_navigation_update():
    """測試導覽更新"""
    print("\n🧭 檢查導覽選單更新...")
    
    try:
        # 檢查auth_manager的更新
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        from utils.auth_manager import AuthManager
        
        # 模擬管理員權限
        class MockAuthManager:
            @staticmethod
            def is_admin():
                return True
            
            @staticmethod
            def get_navigation_pages():
                return AuthManager.get_navigation_pages()
        
        # 測試導覽頁面
        pages = MockAuthManager.get_navigation_pages()
        print(f"✅ 導覽頁面數量: {len(pages)}")
        
        # 檢查是否有受款人管理頁面
        payee_management_found = False
        for page in pages:
            print(f"   📄 {page.get('title', '')} ({page.get('key', '')})")
            if page.get('key') == 'create_data' and '受款人管理' in page.get('title', ''):
                payee_management_found = True
        
        if payee_management_found:
            print("✅ 找到受款人管理頁面")
        else:
            print("❌ 未找到受款人管理頁面")
            return False
        
        print("✅ 導覽選單更新檢查完成")
        return True
        
    except Exception as e:
        print(f"❌ 導覽選單檢查失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始測試新的受款人管理UI...\n")
    
    results = []
    
    # 執行各項測試
    results.append(test_import_new_component())
    results.append(test_ui_structure())
    results.append(test_navigation_update())
    
    # 總結測試結果
    print(f"\n📊 測試結果總結:")
    print(f"✅ 通過測試: {sum(results)}")
    print(f"❌ 失敗測試: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有UI測試通過！")
        print("\n💡 新的受款人管理UI特色：")
        print("   • 整合新增和查詢功能")
        print("   • 卡片式資料顯示")
        print("   • 快速搜尋功能")
        print("   • 簡化的操作流程")
        print("   • 更直觀的用戶介面")
        return 0
    else:
        print("\n⚠️ 部分測試失敗，請檢查UI實現")
        return 1

if __name__ == "__main__":
    sys.exit(main())

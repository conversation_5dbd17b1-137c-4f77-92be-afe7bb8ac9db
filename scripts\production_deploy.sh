#!/bin/bash

# CBA受款人資料搜集系統 - 生產環境部署腳本
# 此腳本將自動化大部分的部署流程

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查是否為root用戶
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "請使用sudo執行此腳本"
        exit 1
    fi
}

# 檢查作業系統
check_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "無法識別作業系統"
        exit 1
    fi
    
    log_info "檢測到作業系統: $OS $VER"
}

# 獲取用戶輸入
get_user_input() {
    echo "🔧 CBA系統生產環境部署配置"
    echo "================================"
    
    read -p "請輸入域名 (例: cba.example.com): " DOMAIN
    while [[ -z "$DOMAIN" ]]; do
        log_warning "域名不能為空"
        read -p "請輸入域名: " DOMAIN
    done
    
    read -p "請輸入管理員郵箱 (用於SSL憑證): " ADMIN_EMAIL
    while [[ -z "$ADMIN_EMAIL" ]]; do
        log_warning "郵箱不能為空"
        read -p "請輸入管理員郵箱: " ADMIN_EMAIL
    done
    
    read -s -p "請輸入資料庫密碼 (至少8字元): " DB_PASSWORD
    echo ""
    while [[ ${#DB_PASSWORD} -lt 8 ]]; do
        log_warning "密碼至少需要8字元"
        read -s -p "請輸入資料庫密碼: " DB_PASSWORD
        echo ""
    done
    
    # 生成隨機密鑰
    JWT_SECRET=$(openssl rand -base64 32)
    ENCRYPTION_KEY=$(openssl rand -base64 32)
    
    log_info "配置完成，開始部署..."
}

# 安裝系統依賴
install_dependencies() {
    log_info "更新系統套件..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        apt update && apt upgrade -y
        apt install -y python3.9 python3.9-venv python3-pip nginx postgresql postgresql-contrib \
                       supervisor git curl wget build-essential libssl-dev libffi-dev python3-dev \
                       certbot python3-certbot-nginx htop iotop nethogs unattended-upgrades
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        yum update -y
        yum install -y python39 python39-pip nginx postgresql-server postgresql-contrib \
                       supervisor git curl wget gcc openssl-devel libffi-devel python3-devel \
                       certbot python3-certbot-nginx htop iotop nethogs
    else
        log_error "不支援的作業系統: $OS"
        exit 1
    fi
    
    log_success "系統依賴安裝完成"
}

# 建立系統用戶和目錄
setup_user_and_directories() {
    log_info "建立系統用戶和目錄..."
    
    # 建立應用程式用戶
    if ! id "cba-app" &>/dev/null; then
        useradd -r -s /bin/bash -m -d /home/<USER>
        log_success "建立用戶 cba-app"
    else
        log_info "用戶 cba-app 已存在"
    fi
    
    # 建立目錄結構
    mkdir -p /opt/cba-system/{logs,backups,ssl,scripts}
    mkdir -p /opt/cba-system/backend
    mkdir -p /opt/cba-system/frontend
    
    # 設定權限
    chown -R cba-app:cba-app /opt/cba-system
    chmod 755 /opt/cba-system
    
    log_success "目錄結構建立完成"
}

# 設定PostgreSQL
setup_postgresql() {
    log_info "設定PostgreSQL資料庫..."
    
    # 啟動PostgreSQL
    if [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        postgresql-setup initdb
    fi
    
    systemctl start postgresql
    systemctl enable postgresql
    
    # 建立資料庫和用戶
    sudo -u postgres psql << EOF
CREATE DATABASE cba_system;
CREATE USER cba_user WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE cba_system TO cba_user;
ALTER USER cba_user CREATEDB;
\q
EOF
    
    # 配置PostgreSQL連線
    PG_VERSION=$(sudo -u postgres psql -t -c "SELECT version();" | grep -oP '\d+\.\d+' | head -1)
    PG_CONFIG_DIR="/etc/postgresql/$PG_VERSION/main"
    
    if [ -d "$PG_CONFIG_DIR" ]; then
        echo "host cba_system cba_user 127.0.0.1/32 md5" >> $PG_CONFIG_DIR/pg_hba.conf
        systemctl restart postgresql
    fi
    
    log_success "PostgreSQL設定完成"
}

# 建立環境配置檔案
create_env_config() {
    log_info "建立環境配置檔案..."
    
    cat > /opt/cba-system/.env.production << EOF
# 基本設定
ENVIRONMENT=production
DEBUG=false
HOST=127.0.0.1
PORT=8080

# 資料庫設定
DATABASE_URL=postgresql://cba_user:$DB_PASSWORD@localhost/cba_system

# JWT設定
JWT_SECRET_KEY=$JWT_SECRET
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480

# 加密設定
ENCRYPTION_KEY=$ENCRYPTION_KEY

# 前端設定
FRONTEND_URL=https://$DOMAIN

# 安全設定
ALLOWED_HOSTS=$DOMAIN,www.$DOMAIN

# 審計設定
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365

# API限制
API_RATE_LIMIT=100
MAX_QUERY_RESULTS=1000

# 日誌設定
LOG_LEVEL=INFO
LOG_FILE=/opt/cba-system/logs/application.log
EOF
    
    chown cba-app:cba-app /opt/cba-system/.env.production
    chmod 600 /opt/cba-system/.env.production
    
    log_success "環境配置檔案建立完成"
}

# 安裝Python依賴管理器
install_uv() {
    log_info "安裝uv套件管理器..."
    
    # 為cba-app用戶安裝uv
    sudo -u cba-app bash << 'EOF'
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc
EOF
    
    log_success "uv安裝完成"
}

# 設定防火牆
setup_firewall() {
    log_info "設定防火牆..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian
        ufw --force enable
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw deny 8000:8999/tcp
        log_success "UFW防火牆設定完成"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL
        systemctl start firewalld
        systemctl enable firewalld
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
        log_success "firewalld防火牆設定完成"
    else
        log_warning "未找到防火牆管理工具，請手動設定"
    fi
}

# 獲取SSL憑證
setup_ssl() {
    log_info "獲取SSL憑證..."
    
    # 建立基本nginx配置以通過域名驗證
    cat > /etc/nginx/sites-available/temp-cba << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location / {
        return 200 'CBA System Setup';
        add_header Content-Type text/plain;
    }
}
EOF
    
    ln -sf /etc/nginx/sites-available/temp-cba /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    nginx -t && systemctl reload nginx
    
    # 獲取SSL憑證
    certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $ADMIN_EMAIL
    
    # 設定自動續期
    echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
    
    log_success "SSL憑證設定完成"
}

# 建立部署後腳本
create_post_deploy_scripts() {
    log_info "建立部署後腳本..."
    
    # 複製腳本檔案到目標目錄
    cp -r scripts/* /opt/cba-system/scripts/ 2>/dev/null || true
    
    # 設定執行權限
    chmod +x /opt/cba-system/scripts/*.sh
    chown -R cba-app:cba-app /opt/cba-system/scripts
    
    log_success "部署後腳本建立完成"
}

# 主函數
main() {
    echo "🚀 CBA受款人資料搜集系統 - 生產環境部署"
    echo "============================================"
    
    check_root
    check_os
    get_user_input
    
    log_info "開始部署流程..."
    
    install_dependencies
    setup_user_and_directories
    setup_postgresql
    create_env_config
    install_uv
    setup_firewall
    setup_ssl
    create_post_deploy_scripts
    
    log_success "基礎部署完成！"
    echo ""
    echo "📋 接下來的步驟:"
    echo "1. 將應用程式檔案複製到 /opt/cba-system/"
    echo "2. 執行: sudo -u cba-app /opt/cba-system/scripts/install_application.sh"
    echo "3. 執行: /opt/cba-system/scripts/setup_services.sh"
    echo "4. 執行: /opt/cba-system/scripts/configure_nginx.sh"
    echo ""
    echo "🌐 您的網站將在以下地址可用:"
    echo "   https://$DOMAIN"
    echo ""
    echo "📁 重要檔案位置:"
    echo "   應用程式: /opt/cba-system/"
    echo "   環境配置: /opt/cba-system/.env.production"
    echo "   日誌檔案: /opt/cba-system/logs/"
    echo "   備份檔案: /opt/cba-system/backups/"
}

# 執行主函數
main "$@"

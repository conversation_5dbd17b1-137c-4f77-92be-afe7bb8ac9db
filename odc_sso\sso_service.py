from flask import Flask, request, Response, render_template_string
from spyne import Application, rpc, ServiceBase, String, Unicode
from spyne.protocol.soap import Soap11
from spyne.server.wsgi import WsgiApplication
import xml.etree.ElementTree as ET

app = Flask(__name__)

# HTML 模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>SSO 服務測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SSO 服務測試頁面</h1>
        
        <h2>測試連結</h2>
        <p>點擊下方按鈕測試 SSO 登入(正確token)：</p>
        <a href="http://127.0.0.1:8080/api/v1/auth/sso_login?ssoToken1=cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956" class="button">
            前往 測試登入系統
        </a>
        <p>點擊下方按鈕測試 SSO 登入(錯誤token)：</p>
        <a href="http://************/login?ssoToken1=1740367580912#" class="button">
            前往 測試登入系統
        </a>
        <h2>SOAP 服務資訊</h2>
        <p>WSDL 位置：</p>
        <div class="code">
            <a href="/SS/SS0/CommonWebService.asmx?wsdl">{{ request.host_url }}SS/SS0/CommonWebService.asmx?wsdl</a>
        </div>

        <h2>測試用 Token</h2>
        <p>您可以使用以下 token 進行測試：</p>
        <div class="code">1740367580910</div>

        <h2>範例程式碼</h2>
        <pre class="code">
from zeep import Client

# 建立客戶端
client = Client('{{ request.host_url }}SS/SS0/CommonWebService.asmx?wsdl')

# 直接使用 token 字串
token = "1740367580910"

# 呼叫服務
result = client.service.getUserProfile(token)
print(result)
        </pre>
    </div>
</body>
</html>
"""

# 添加首頁路由
@app.route('/')
def home():
    return render_template_string(HTML_TEMPLATE)

class CommonWebService(ServiceBase):
    __service_url__ = "/SS/SS0/CommonWebService.asmx"
    __target_namespace__ = "http://serivce.common.kangdainfo.com"
    
    @rpc(Unicode, _returns=Unicode, _operation_name="getUserProfile")
    def get_user_profile(ctx, in0):
        """處理 ssoToken1 字串並返回用戶資料"""
        # 直接檢查 token 字串
        if not in0 or 'cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956' not in in0:
            error_xml = """<?xml version="1.0" encoding="utf-8"?>
<Error>
    <Message>Invalid ssoToken1</Message>
    <Code>401</Code>
</Error>"""
            return error_xml
            
        # 建立用戶資料
        user_info = {
            'Account': '009998',
            'Name': '李大仁',
            'Department': '屏東縣政府',
            'Title': '',
            'Status': ''
        }
        
        # 建立用戶資料 XML
        user_data = ET.Element('userProfile')
        
        # 添加所有欄位
        fields = {
            '帳號': user_info['Account'],
            '姓名': user_info['Name'],
            '電話': '',
            '傳真': '',
            '手機': '',
            '統號': '',
            'Email': '',
            '機關代碼': '376530000A',
            '機關名稱': user_info['Department'],
            '職稱代碼': '',
            '職稱': user_info['Title'],
            '可使用系統': '',
            '單位代碼': '1030'
        }
        
        # 特殊處理需要重複的欄位
        for field_name, value in fields.items():
            if field_name in ['電話', '手機']:
                # 新增兩次
                ET.SubElement(user_data, field_name).text = value
                ET.SubElement(user_data, field_name).text = value
            else:
                ET.SubElement(user_data, field_name).text = value
        
        # 轉換為 XML 字串
        xml_declaration = '<?xml version="1.0" encoding="utf-8"?>\n'
        xml_content = ET.tostring(user_data, encoding='utf-8').decode()
        print(f"xml_content: {xml_content}")
        return xml_declaration + xml_content

# 建立 SOAP 應用
application = Application(
    services=[CommonWebService],
    tns='http://serivce.common.kangdainfo.com',
    in_protocol=Soap11(validator='lxml'),
    out_protocol=Soap11(),
    name='CommonWebService'
)

wsgi_app = WsgiApplication(application)

# 路由處理
@app.route('/SS/SS0/CommonWebService.asmx', methods=['GET', 'POST'])
def handle_soap():
    return Response(
        wsgi_app(request.environ, lambda s, h: h),
        mimetype='text/xml'
    )

@app.route('/SS/SS0/CommonWebService.asmx/wsdl')
def wsdl():
    return Response(
        wsgi_app(request.environ, lambda s, h: h),
        mimetype='text/xml'
    )

# 添加一個測試用的重定向路由
@app.route('/test-sso')
def test_sso():
    return redirect('https://odcsso.pthg.gov.tw/ptms/index.html?=1740367580910#')

if __name__ == '__main__':
    #print("SSO Service starting on http://0.0.0.0:4000")
    #print("Visit http://0.0.0.0:4000 for the test page")
    app.run(host='0.0.0.0', port=4000) 
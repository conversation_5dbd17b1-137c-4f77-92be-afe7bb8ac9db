from fastapi import APIRouter, Depends, Query, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from app.models.database import get_db
from app.models.user import User, Role, UserRole, Permission, RolePermission, Department
from app.schemas.user import UserResponse, UserAdminResponse, UpdateUserDepartmentRequest
from app.core.dependencies import get_current_active_user, get_client_info
from app.core.exceptions import PermissionDeniedError
from app.utils.audit import log_operation
from pydantic import BaseModel, conlist

router = APIRouter(prefix="/users", tags=["用戶管理"])

@router.get("/", response_model=List[UserAdminResponse])
async def get_users(
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(20, ge=1, le=100, description="每頁筆數"),
    role: Optional[str] = Query(None, description="角色名稱"),
    status: Optional[bool] = Query(None, description="啟用狀態"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取得用戶列表（僅管理者可用）
    """
    # 權限檢查
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢用戶列表")

    query = db.query(User)
    if status is not None:
        query = query.filter(User.is_active == status)
    if role:
        query = query.join(User.user_roles).join(Role).filter(Role.name == role)
    
    total = query.count()
    users = query.offset((page-1)*size).limit(size).all()
    
    return [UserAdminResponse(
        id=u.id,
        username=u.username,
        full_name=u.full_name,
        department=u.department.name if u.department else None,
        is_active=u.is_active,
        created_at=u.created_at,
        roles=[r.name for r in u.roles]
    ) for u in users]

class UpdateUserRoleRequest(BaseModel):
    role_id: int

@router.put("/{user_id}/role", response_model=UserAdminResponse)
async def update_user_role(
    user_id: int,
    data: UpdateUserRoleRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用戶角色（僅管理者可用）
    """
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可變更用戶角色")

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用戶不存在")

    role = db.query(Role).filter(Role.id == data.role_id).first()
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

    # 移除舊有角色
    db.query(UserRole).filter(UserRole.user_id == user_id).delete()
    # 新增新角色
    user_role = UserRole(user_id=user_id, role_id=role.id)
    db.add(user_role)
    db.commit()

    # 記錄審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_USER_ROLE",
        table_name="users",
        new_values={"user_id": user_id, "new_role": role.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )

    db.refresh(user)
    return UserAdminResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        department=user.department.name if user.department else None,
        is_active=user.is_active,
        created_at=user.created_at,
        roles=[r.name for r in user.roles]
    )

class UpdateUserStatusRequest(BaseModel):
    is_active: bool

@router.put("/{user_id}/status", response_model=UserAdminResponse)
async def update_user_status(
    user_id: int,
    data: UpdateUserStatusRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    啟用/停用用戶（僅管理者可用）
    """
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可變更用戶狀態")

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用戶不存在")

    user.is_active = data.is_active
    db.commit()

    # 記錄審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_USER_STATUS",
        table_name="users",
        new_values={"user_id": user_id, "is_active": data.is_active},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )

    db.refresh(user)
    return UserAdminResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        department=user.department.name if user.department else None,
        is_active=user.is_active,
        created_at=user.created_at,
        roles=[r.name for r in user.roles]
    )

class UpdateUserDepartmentRequest(BaseModel):
    department_id: int

class UpdateUserNameRequest(BaseModel):
    name: str

@router.put("/{user_id}/name", response_model=UserAdminResponse)
async def update_user_name(
    user_id: int,
    data: UpdateUserNameRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用戶名稱（僅管理者可用）
    """
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可變更用戶名稱")

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用戶不存在")

    # 更新用戶名稱
    user.full_name = data.name
    db.commit()

    # 記錄審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_USER_NAME",
        table_name="users",
        old_values={"full_name": user.full_name},
        new_values={"user_id": user_id, "full_name": data.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )

    db.refresh(user)
    return UserAdminResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        department=user.department.name if user.department else None,
        is_active=user.is_active,
        created_at=user.created_at,
        roles=[r.name for r in user.roles]
    )

@router.put("/{user_id}/department", response_model=UserAdminResponse)
async def update_user_department(
    user_id: int,
    data: UpdateUserDepartmentRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用戶部門（僅管理者可用）
    """
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可變更用戶部門")
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用戶不存在")
    department = db.query(Department).filter(Department.id == data.department_id, Department.is_active == True).first()
    if not department:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部門不存在")
    user.department_id = department.id
    db.commit()
    # 記錄審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_USER_DEPARTMENT",
        table_name="users",
        new_values={"user_id": user_id, "department_id": department.id},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    db.refresh(user)
    return UserAdminResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        department=user.department.name if user.department else None,
        is_active=user.is_active,
        created_at=user.created_at,
        roles=[r.name for r in user.roles]
    )

@router.get("/roles", response_model=List[Dict[str, str]])
async def get_roles(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    print(f"current_user: {current_user}")
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢角色清單")
    roles = db.query(Role).filter(Role.is_active == True).all()
    return [{"id": str(r.id), "name": r.name, "description": str(r.description) if r.description is not None else ""} for r in roles]

@router.get("/permissions", response_model=List[Dict[str, str]])
async def get_permissions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢權限清單")
    permissions = db.query(Permission).filter(Permission.is_active == True).all()
    return [{"id": str(p.id), "name": p.name, "description": p.description or ""} for p in permissions]

class UpdateRolePermissionsRequest(BaseModel):
    permission_ids: conlist(int, min_length=1)

@router.get("/roles/{role_id}/permissions", response_model=List[str])
async def get_role_permissions(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢角色權限")
    role = db.query(Role).filter(Role.id == role_id, Role.is_active == True).first()
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    return role.permission_names

@router.put("/roles/{role_id}/permissions", response_model=Dict[str, List[str]])
async def update_role_permissions(
    role_id: int,
    data: UpdateRolePermissionsRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可調整角色權限")
    role = db.query(Role).filter(Role.id == role_id, Role.is_active == True).first()
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    # 移除舊有權限
    db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()
    # 新增新權限
    for pid in data.permission_ids:
        db.add(RolePermission(role_id=role_id, permission_id=pid))
    db.commit()
    # 記錄審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_ROLE_PERMISSIONS",
        table_name="roles",
        new_values={"role_id": role_id, "permission_ids": data.permission_ids},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    db.refresh(role)
    return {"permissions": role.permission_names}

class CreateRoleRequest(BaseModel):
    name: str
    description: Optional[str] = None

class UpdateRoleNameRequest(BaseModel):
    name: str
    description: Optional[str] = None

@router.post("/roles", response_model=Dict[str, str])
async def create_role(
    data: CreateRoleRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可新增角色")
    if db.query(Role).filter(Role.name == data.name).first():
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="角色名稱已存在")
    role = Role(name=data.name, description=data.description, is_active=True)
    db.add(role)
    db.commit()
    db.refresh(role)
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="CREATE_ROLE",
        table_name="roles",
        new_values={"role_id": role.id, "name": data.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return {"message": "角色新增成功", "role_id": str(role.id)}

@router.put("/roles/{role_id}", response_model=Dict[str, str])
async def update_role_name(
    role_id: int,
    data: UpdateRoleNameRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可修改角色名稱")
    role = db.query(Role).filter(Role.id == role_id, Role.is_active == True).first()
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    if db.query(Role).filter(Role.name == data.name, Role.id != role_id).first():
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="角色名稱已存在")
    role.name = data.name
    role.description = data.description
    db.commit()
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_ROLE_NAME",
        table_name="roles",
        new_values={"role_id": role_id, "name": data.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return {"message": "角色名稱修改成功"}

@router.delete("/roles/{role_id}", response_model=Dict[str, str])
async def delete_role(
    role_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可刪除角色")
    role = db.query(Role).filter(Role.id == role_id, Role.is_active == True).first()
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    role.is_active = False
    db.commit()
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="DELETE_ROLE",
        table_name="roles",
        new_values={"role_id": role_id, "name": role.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return {"message": "角色已刪除"} 
#!/usr/bin/env python3
"""
加密功能測試
"""

import os
from dotenv import load_dotenv

def test_encryption():
    """測試加密功能"""
    print("🔍 測試加密功能")
    print("=" * 40)
    
    # 載入環境變數（明確指定.env路徑）
    load_dotenv('.env', override=True)
    
    print("1️⃣ 檢查環境變數...")
    encryption_key = os.getenv('ENCRYPTION_KEY')
    if not encryption_key:
        print("❌ ENCRYPTION_KEY 環境變數未設定")
        return False
    
    print(f"✅ ENCRYPTION_KEY 已設定 (長度: {len(encryption_key)})")
    print(f"📋 金鑰前10字元: {encryption_key[:10]}...")
    
    print("\n2️⃣ 測試加密模組...")
    try:
        from app.utils.encryption import encrypt_id_number, decrypt_id_number
        
        # 測試身分證字號
        test_id = "A123456789"
        print(f"📝 測試身分證字號: {test_id}")
        
        # 加密
        encrypted = encrypt_id_number(test_id)
        print(f"🔐 加密成功，結果長度: {len(encrypted)} 字元")
        print(f"🔐 加密結果前20字元: {encrypted[:20]}...")
        
        # 解密
        decrypted = decrypt_id_number(encrypted)
        print(f"🔓 解密結果: {decrypted}")
        
        # 驗證
        if decrypted == test_id:
            print("✅ 加密解密測試通過！")
            
            # 測試多次加密產生不同結果
            print("\n3️⃣ 測試多次加密...")
            encrypted2 = encrypt_id_number(test_id)
            print(f"🔐 第二次加密結果前20字元: {encrypted2[:20]}...")
            
            if encrypted != encrypted2:
                print("✅ 多次加密產生不同結果（正常）")
            else:
                print("⚠️ 多次加密產生相同結果（可能有問題）")
            
            # 驗證第二次解密
            decrypted2 = decrypt_id_number(encrypted2)
            if decrypted2 == test_id:
                print("✅ 第二次解密測試通過！")
                return True
            else:
                print("❌ 第二次解密失敗")
                return False
        else:
            print("❌ 加密解密結果不匹配")
            print(f"   期望: {test_id}")
            print(f"   實際: {decrypted}")
            return False
            
    except Exception as e:
        print(f"❌ 加密測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_encryption()
    
    if success:
        print("\n🎉 加密功能測試成功！")
        print("📋 下一步：重新啟動後端服務並測試個人資料建立")
    else:
        print("\n❌ 加密功能測試失敗")
    
    exit(0 if success else 1) 
#!/usr/bin/env python3
"""
簡單的身分證字號驗證測試腳本
"""

import re
from typing import List, <PERSON><PERSON>

def validate_id_number(id_number: str) -> bool:
    """
    驗證台灣身分證字號格式
    
    Args:
        id_number: 身分證字號
        
    Returns:
        True如果格式正確，False否則
    """
    if not id_number:
        return False
    
    # 移除空白字元並轉為大寫
    id_number = id_number.strip().upper()
    
    # 檢查基本格式：第一碼為英文字母，後9碼為數字
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False
    
    # 英文字母對應的數值表
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    # 取得英文字母對應的數值
    letter = id_number[0]
    if letter not in letter_values:
        return False
    
    letter_value = letter_values[letter]
    
    # 計算檢查碼
    # 英文字母的十位數 * 1 + 個位數 * 9
    checksum = (letter_value // 10) * 1 + (letter_value % 10) * 9
    
    # 第2到第9碼分別乘以8,7,6,5,4,3,2,1後加總
    for i in range(1, 9):
        checksum += int(id_number[i]) * (9 - i)
    
    # 加上第10碼（檢查碼）
    checksum += int(id_number[9])
    
    # 檢查是否能被10整除
    return checksum % 10 == 0

def get_id_number_errors(id_number: str) -> List[str]:
    """
    取得身分證字號驗證錯誤詳細信息
    
    Args:
        id_number: 身分證字號
        
    Returns:
        錯誤信息列表
    """
    errors = []
    
    if not id_number:
        errors.append("身分證字號不能為空")
        return errors
    
    id_number = id_number.strip().upper()
    
    if len(id_number) != 10:
        errors.append("身分證字號必須為10位數")
    
    if not re.match(r'^[A-Z]', id_number):
        errors.append("第一位必須為英文字母")
    
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        errors.append("格式錯誤：第一位為英文字母，後9位為數字")
    
    # 如果基本格式正確，檢查檢查碼
    if len(errors) == 0 and not validate_id_number(id_number):
        errors.append("檢查碼驗證失敗")
    
    return errors

def frontend_validate_id_number(id_number: str) -> Tuple[bool, str]:
    """
    前端驗證身分證字號格式（模擬frontend/components/personal_data_form.py的函數）
    
    Args:
        id_number: 身分證字號
        
    Returns:
        (is_valid, error_message)
    """
    if not id_number:
        return False, "身分證字號為必填欄位"
    
    # 移除空格
    id_number = id_number.strip().upper()
    
    # 檢查格式：第一位英文字母，後續9位數字
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False, "身分證字號格式錯誤，應為1位英文字母加9位數字"
    
    # 身分證字號檢核邏輯
    # 英文字母對應數字表
    letter_mapping = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    first_letter = id_number[0]
    if first_letter not in letter_mapping:
        return False, "身分證字號首位字母無效"
    
    # 計算檢核碼
    letter_value = letter_mapping[first_letter]
    total = (letter_value // 10) + (letter_value % 10) * 9
    
    for i in range(1, 9):
        total += int(id_number[i]) * (9 - i)
    
    check_digit = (10 - (total % 10)) % 10
    
    if check_digit != int(id_number[9]):
        return False, "身分證字號檢核碼錯誤"
    
    return True, ""

def generate_valid_id_numbers(count: int = 10) -> List[str]:
    """
    生成有效的身分證字號
    
    Args:
        count: 要生成的數量
        
    Returns:
        有效身分證字號列表
    """
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    valid_ids = []
    
    # 生成有效的身分證字號
    for letter in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K']:
        for gender in ['1', '2']:
            for i in range(1, 4):
                if len(valid_ids) >= count:
                    break
                
                # 生成前8碼
                id_prefix = f"{letter}{gender}{i:07d}"
                
                # 計算檢查碼
                letter_value = letter_values[letter]
                checksum = (letter_value // 10) * 1 + (letter_value % 10) * 9
                
                for j in range(1, 9):
                    checksum += int(id_prefix[j]) * (9 - j)
                
                check_digit = (10 - (checksum % 10)) % 10
                
                # 完整身分證字號
                full_id = f"{id_prefix}{check_digit}"
                valid_ids.append(full_id)
            
            if len(valid_ids) >= count:
                break
        
        if len(valid_ids) >= count:
            break
    
    return valid_ids[:count]

def test_validation():
    """測試身分證字號驗證功能"""
    print("🧪 身分證字號驗證功能測試")
    print("=" * 80)
    
    # 生成有效的身分證字號
    valid_ids = generate_valid_id_numbers(5)
    
    # 測試案例
    test_cases = [
        # 有效的身分證字號
        *[(id_num, True, "計算生成的有效身分證字號") for id_num in valid_ids],
        
        # 無效的身分證字號
        ("", False, "空字串"),
        ("A12345678", False, "長度不足"),
        ("A1234567890", False, "長度過長"),
        ("123456789A", False, "沒有英文字母開頭"),
        ("ABCDEFGHIJ", False, "全部英文字母"),
        ("A12345678A", False, "最後一位是英文字母"),
        ("a123456789", False, "小寫英文字母"),
        ("A123456788", False, "檢查碼錯誤"),
        ("A123-56789", False, "包含特殊字元"),
        ("A123 56789", False, "包含空格"),
        ("A12345678X", False, "包含無效字元"),
        ("?123456789", False, "開頭非英文字母"),
        ("A123456789 ", False, "尾部有空格"),
        (" A123456789", False, "開頭有空格"),
    ]
    
    print("🔍 測試後端驗證函數...")
    print("-" * 80)
    
    backend_passed = 0
    backend_failed = 0
    
    for i, (id_number, expected_valid, description) in enumerate(test_cases, 1):
        try:
            # 測試後端驗證函數
            is_valid = validate_id_number(id_number)
            errors = get_id_number_errors(id_number)
            
            # 檢查結果
            if is_valid == expected_valid:
                status = "✅ 通過"
                backend_passed += 1
            else:
                status = "❌ 失敗"
                backend_failed += 1
            
            # 輸出結果
            print(f"測試 {i:2d}: {status}")
            print(f"  身分證字號: {id_number!r}")
            print(f"  描述: {description}")
            print(f"  預期結果: {'有效' if expected_valid else '無效'}")
            print(f"  實際結果: {'有效' if is_valid else '無效'}")
            
            if errors:
                print(f"  錯誤訊息: {', '.join(errors)}")
            
            print()
            
        except Exception as e:
            print(f"測試 {i:2d}: ⚠️ 異常")
            print(f"  身分證字號: {id_number!r}")
            print(f"  錯誤: {str(e)}")
            print()
            backend_failed += 1
    
    print("🔍 測試前端驗證函數...")
    print("-" * 80)
    
    frontend_passed = 0
    frontend_failed = 0
    
    # 測試前端驗證函數
    for i, (id_number, expected_valid, description) in enumerate(test_cases, 1):
        try:
            # 測試前端驗證函數
            is_valid, error_msg = frontend_validate_id_number(id_number)
            
            # 檢查結果
            if is_valid == expected_valid:
                status = "✅ 通過"
                frontend_passed += 1
            else:
                status = "❌ 失敗"
                frontend_failed += 1
            
            # 輸出結果
            print(f"測試 {i:2d}: {status}")
            print(f"  身分證字號: {id_number!r}")
            print(f"  描述: {description}")
            print(f"  預期結果: {'有效' if expected_valid else '無效'}")
            print(f"  實際結果: {'有效' if is_valid else '無效'}")
            
            if error_msg:
                print(f"  錯誤訊息: {error_msg}")
            
            print()
            
        except Exception as e:
            print(f"測試 {i:2d}: ⚠️ 異常")
            print(f"  身分證字號: {id_number!r}")
            print(f"  錯誤: {str(e)}")
            print()
            frontend_failed += 1
    
    # 輸出測試總結
    print("=" * 80)
    print("📊 測試結果總結")
    print(f"後端驗證: {backend_passed} 通過, {backend_failed} 失敗")
    print(f"前端驗證: {frontend_passed} 通過, {frontend_failed} 失敗")
    print(f"總計: {backend_passed + frontend_passed} 通過, {backend_failed + frontend_failed} 失敗")
    
    if backend_failed == 0 and frontend_failed == 0:
        print("🎉 所有測試通過！身分證字號驗證功能正常運作")
    else:
        print("⚠️ 部分測試失敗，請檢查驗證邏輯")
    
    print("\n🔍 測試一些真實的身分證字號範例:")
    print("-" * 80)
    
    # 測試一些常見的身分證字號格式
    real_examples = [
        "A123456789",  # 台北市
        "B123456789",  # 台中市
        "C123456789",  # 基隆市
        "D123456789",  # 台南市
        "E123456789",  # 高雄市
        "F123456789",  # 台北縣
        "G123456789",  # 宜蘭縣
        "H123456789",  # 桃園縣
        "J123456789",  # 新竹縣
        "K123456789",  # 苗栗縣
    ]
    
    for id_num in real_examples:
        is_valid = validate_id_number(id_num)
        print(f"{id_num}: {'✅ 有效' if is_valid else '❌ 無效'}")
    
    return backend_passed, backend_failed, frontend_passed, frontend_failed

if __name__ == "__main__":
    test_validation() 
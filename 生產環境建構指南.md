# CBA受款人資料搜集系統 - 生產環境建構指南

## 📋 目錄
- [系統需求](#系統需求)
- [環境準備](#環境準備)
- [安全性配置](#安全性配置)
- [資料庫設定](#資料庫設定)
- [應用程式部署](#應用程式部署)
- [反向代理設定](#反向代理設定)
- [SSL憑證配置](#ssl憑證配置)
- [監控與日誌](#監控與日誌)
- [備份策略](#備份策略)
- [維護與更新](#維護與更新)

---

## 🖥️ 系統需求

### 最低硬體需求
- **CPU**: 4核心 2.4GHz 或以上
- **記憶體**: 8GB RAM 或以上
- **硬碟**: 100GB SSD 可用空間
- **網路**: 穩定的網際網路連線

### 建議硬體需求
- **CPU**: 8核心 3.0GHz 或以上
- **記憶體**: 16GB RAM 或以上
- **硬碟**: 500GB SSD 或以上
- **網路**: 高速穩定連線，備援網路

### 作業系統需求
- **Linux**: Ubuntu 20.04 LTS / CentOS 8 / RHEL 8 或以上
- **Windows**: Windows Server 2019 或以上
- **容器**: Docker 20.10+ / Kubernetes 1.20+

### 軟體需求
- **Python**: 3.9 或以上
- **資料庫**: PostgreSQL 13+ (建議) 或 SQLite 3.x
- **反向代理**: Nginx 1.18+ 或 Apache 2.4+
- **程序管理**: systemd 或 Supervisor
- **SSL憑證**: Let's Encrypt 或商業憑證

---

## 🚀 環境準備

### 1. 建立專用用戶和目錄

```bash
# 建立系統用戶
sudo useradd -r -s /bin/bash -m -d /home/<USER>

# 建立應用程式目錄
sudo mkdir -p /opt/cba-system
sudo mkdir -p /opt/cba-system/logs
sudo mkdir -p /opt/cba-system/backups
sudo mkdir -p /opt/cba-system/ssl

# 設定權限
sudo chown -R cba-app:cba-app /opt/cba-system
sudo chmod 755 /opt/cba-system
```

### 2. 安裝系統依賴

#### Ubuntu/Debian:
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3.9 python3.9-venv python3-pip
sudo apt install -y nginx postgresql postgresql-contrib
sudo apt install -y supervisor git curl wget
sudo apt install -y build-essential libssl-dev libffi-dev python3-dev
sudo apt install -y certbot python3-certbot-nginx
```

#### CentOS/RHEL:
```bash
sudo yum update -y
sudo yum install -y python39 python39-pip nginx postgresql-server postgresql-contrib
sudo yum install -y supervisor git curl wget
sudo yum groupinstall -y "Development Tools"
sudo yum install -y certbot python3-certbot-nginx
```

### 3. 安裝Python套件管理器

```bash
# 安裝uv (推薦)
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc

# 或使用pip
pip3 install uv
```

---

## 🔒 安全性配置

### 1. 防火牆設定

```bash
# Ubuntu (ufw)
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 8000:8999/tcp  # 拒絕內部服務埠

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSH安全設定

編輯 `/etc/ssh/sshd_config`:
```bash
# 禁用root登入
PermitRootLogin no

# 使用金鑰認證
PasswordAuthentication no
PubkeyAuthentication yes

# 限制登入嘗試
MaxAuthTries 3
MaxSessions 2

# 重啟SSH服務
sudo systemctl restart sshd
```

### 3. 系統安全更新

```bash
# 設定自動安全更新
sudo apt install unattended-upgrades  # Ubuntu
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

# 或手動定期更新
sudo crontab -e
# 加入: 0 2 * * 0 apt update && apt upgrade -y
```

---

## 🗄️ 資料庫設定

### PostgreSQL設定 (建議)

```bash
# 初始化PostgreSQL
sudo postgresql-setup initdb  # CentOS
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 建立資料庫和用戶
sudo -u postgres psql << EOF
CREATE DATABASE cba_system;
CREATE USER cba_user WITH ENCRYPTED PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE cba_system TO cba_user;
ALTER USER cba_user CREATEDB;
\q
EOF

# 配置PostgreSQL連線
sudo nano /etc/postgresql/*/main/pg_hba.conf
# 加入: host cba_system cba_user 127.0.0.1/32 md5

# 重啟PostgreSQL
sudo systemctl restart postgresql
```

### 資料庫備份設定

```bash
# 建立備份腳本
sudo nano /opt/cba-system/scripts/backup_db.sh
```

---

## 📦 應用程式部署

### 1. 部署應用程式檔案

```bash
# 切換到應用程式用戶
sudo su - cba-app

# 複製應用程式檔案到生產目錄
cd /opt/cba-system
git clone <your-repository-url> .
# 或使用scp/rsync複製檔案

# 建立Python虛擬環境
python3.9 -m venv .venv
source .venv/bin/activate

# 安裝依賴
cd backend
uv sync --frozen
cd ../frontend
pip install -r requirements.txt
```

### 2. 生產環境配置

建立 `/opt/cba-system/.env.production`:
```bash
# 基本設定
ENVIRONMENT=production
DEBUG=false
HOST=127.0.0.1
PORT=8080

# 資料庫設定
DATABASE_URL=postgresql://cba_user:your_secure_password_here@localhost/cba_system

# JWT設定 (使用強密鑰)
JWT_SECRET_KEY=your-super-secure-jwt-key-minimum-32-characters-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480

# 加密設定 (使用強密鑰)
ENCRYPTION_KEY=your-super-secure-encryption-key-32-chars

# 前端設定
FRONTEND_URL=https://your-domain.com

# 安全設定
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 審計設定
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365

# API限制
API_RATE_LIMIT=100
MAX_QUERY_RESULTS=1000
```

### 3. 資料庫初始化

```bash
# 執行資料庫遷移
cd /opt/cba-system/backend
source ../.venv/bin/activate
python -c "from app.models.database import init_db, engine; init_db(engine)"

# 建立初始管理員用戶（如果需要）
python scripts/create_admin_user.py
```

---

## 🌐 反向代理設定

### Nginx配置

建立 `/etc/nginx/sites-available/cba-system`:
```nginx
# 後端API伺服器
upstream cba_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

# 前端Streamlit伺服器
upstream cba_frontend {
    server 127.0.0.1:8501;
    keepalive 32;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL憑證設定
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL安全設定
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全標頭
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'";
    
    # 客戶端設定
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 前端應用 (Streamlit)
    location / {
        proxy_pass http://cba_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 86400;
        proxy_buffering off;
        proxy_redirect off;
    }
    
    # 後端API
    location /api/ {
        proxy_pass http://cba_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康檢查
    location /health {
        proxy_pass http://cba_backend;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # 靜態檔案快取
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://cba_frontend;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 安全性設定
    server_tokens off;
    
    # 日誌設定
    access_log /var/log/nginx/cba-system.access.log;
    error_log /var/log/nginx/cba-system.error.log;
}
```

啟用站點:
```bash
sudo ln -s /etc/nginx/sites-available/cba-system /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔐 SSL憑證配置

### 使用Let's Encrypt (免費SSL)

```bash
# 安裝Certbot
sudo apt install certbot python3-certbot-nginx  # Ubuntu
sudo yum install certbot python3-certbot-nginx  # CentOS

# 獲取SSL憑證
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 設定自動續期
sudo crontab -e
# 加入: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 使用商業SSL憑證

```bash
# 生成私鑰和CSR
sudo openssl genrsa -out /opt/cba-system/ssl/private.key 2048
sudo openssl req -new -key /opt/cba-system/ssl/private.key -out /opt/cba-system/ssl/certificate.csr

# 將CSR提交給憑證頒發機構
# 獲得憑證後，將檔案放置在:
# /opt/cba-system/ssl/certificate.crt
# /opt/cba-system/ssl/ca-bundle.crt

# 更新Nginx配置中的SSL路徑
```

---

## 🔧 系統服務配置

### 1. 後端API服務 (systemd)

建立 `/etc/systemd/system/cba-backend.service`:
```ini
[Unit]
Description=CBA Backend API Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=exec
User=cba-app
Group=cba-app
WorkingDirectory=/opt/cba-system/backend
Environment=PATH=/opt/cba-system/.venv/bin
EnvironmentFile=/opt/cba-system/.env.production
ExecStart=/opt/cba-system/.venv/bin/uvicorn main:app --host 127.0.0.1 --port 8080 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cba-backend

# 安全設定
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/cba-system

[Install]
WantedBy=multi-user.target
```

### 2. 前端UI服務 (systemd)

建立 `/etc/systemd/system/cba-frontend.service`:
```ini
[Unit]
Description=CBA Frontend UI Service
After=network.target cba-backend.service
Wants=cba-backend.service

[Service]
Type=exec
User=cba-app
Group=cba-app
WorkingDirectory=/opt/cba-system/frontend
Environment=PATH=/opt/cba-system/.venv/bin
EnvironmentFile=/opt/cba-system/.env.production
ExecStart=/opt/cba-system/.venv/bin/streamlit run main.py --server.port 8501 --server.address 127.0.0.1 --server.headless true
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cba-frontend

# 安全設定
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/cba-system

[Install]
WantedBy=multi-user.target
```

### 3. 啟用和啟動服務

```bash
# 重新載入systemd配置
sudo systemctl daemon-reload

# 啟用服務
sudo systemctl enable cba-backend.service
sudo systemctl enable cba-frontend.service
sudo systemctl enable nginx.service
sudo systemctl enable postgresql.service

# 啟動服務
sudo systemctl start cba-backend.service
sudo systemctl start cba-frontend.service

# 檢查服務狀態
sudo systemctl status cba-backend.service
sudo systemctl status cba-frontend.service
```

---

## 📊 監控與日誌

### 1. 日誌管理

```bash
# 配置日誌輪替
sudo nano /etc/logrotate.d/cba-system
```

內容:
```
/opt/cba-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cba-app cba-app
    postrotate
        systemctl reload cba-backend cba-frontend
    endscript
}

/var/log/nginx/cba-system.*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 2. 系統監控腳本

建立 `/opt/cba-system/scripts/health_check.sh`:
```bash
#!/bin/bash

# 健康檢查腳本
LOG_FILE="/opt/cba-system/logs/health_check.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 檢查服務狀態
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "[$DATE] ✅ $service is running" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] ❌ $service is not running" >> $LOG_FILE
        return 1
    fi
}

# 檢查HTTP端點
check_endpoint() {
    local url=$1
    local name=$2
    if curl -f -s $url > /dev/null; then
        echo "[$DATE] ✅ $name endpoint is healthy" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] ❌ $name endpoint is not responding" >> $LOG_FILE
        return 1
    fi
}

# 執行檢查
echo "[$DATE] Starting health check..." >> $LOG_FILE

check_service "cba-backend.service"
check_service "cba-frontend.service"
check_service "nginx.service"
check_service "postgresql.service"

check_endpoint "http://localhost:8080/health" "Backend API"
check_endpoint "http://localhost:8501" "Frontend UI"
check_endpoint "https://your-domain.com/health" "Public endpoint"

echo "[$DATE] Health check completed" >> $LOG_FILE
echo "---" >> $LOG_FILE
```

設定定期執行:
```bash
sudo chmod +x /opt/cba-system/scripts/health_check.sh
sudo crontab -e
# 加入: */5 * * * * /opt/cba-system/scripts/health_check.sh
```

### 3. 效能監控

安裝監控工具:
```bash
# 安裝htop, iotop等監控工具
sudo apt install htop iotop nethogs  # Ubuntu
sudo yum install htop iotop nethogs  # CentOS

# 安裝Prometheus Node Exporter (可選)
wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.3.1.linux-amd64.tar.gz
sudo mv node_exporter-1.3.1.linux-amd64/node_exporter /usr/local/bin/
```

---

## 💾 備份策略

### 1. 資料庫備份腳本

建立 `/opt/cba-system/scripts/backup_database.sh`:
```bash
#!/bin/bash

BACKUP_DIR="/opt/cba-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="cba_system"
DB_USER="cba_user"

# 建立備份目錄
mkdir -p $BACKUP_DIR

# 執行資料庫備份
pg_dump -h localhost -U $DB_USER -d $DB_NAME | gzip > $BACKUP_DIR/cba_db_backup_$DATE.sql.gz

# 刪除30天前的備份
find $BACKUP_DIR -name "cba_db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: cba_db_backup_$DATE.sql.gz"
```

### 2. 應用程式檔案備份

建立 `/opt/cba-system/scripts/backup_files.sh`:
```bash
#!/bin/bash

BACKUP_DIR="/opt/cba-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/cba-system"

# 建立應用程式檔案備份
tar -czf $BACKUP_DIR/cba_files_backup_$DATE.tar.gz \
    --exclude="$APP_DIR/backups" \
    --exclude="$APP_DIR/logs" \
    --exclude="$APP_DIR/.venv" \
    --exclude="$APP_DIR/__pycache__" \
    $APP_DIR

# 刪除30天前的備份
find $BACKUP_DIR -name "cba_files_backup_*.tar.gz" -mtime +30 -delete

echo "Files backup completed: cba_files_backup_$DATE.tar.gz"
```

### 3. 設定自動備份

```bash
sudo chmod +x /opt/cba-system/scripts/backup_*.sh
sudo crontab -e
# 加入以下行:
# 每日凌晨2點備份資料庫
0 2 * * * /opt/cba-system/scripts/backup_database.sh
# 每週日凌晨3點備份檔案
0 3 * * 0 /opt/cba-system/scripts/backup_files.sh
```

---

## 🔄 維護與更新

### 1. 應用程式更新腳本

建立 `/opt/cba-system/scripts/update_application.sh`:
```bash
#!/bin/bash

APP_DIR="/opt/cba-system"
BACKUP_DIR="/opt/cba-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🔄 開始應用程式更新..."

# 1. 建立更新前備份
echo "📦 建立更新前備份..."
$APP_DIR/scripts/backup_database.sh
$APP_DIR/scripts/backup_files.sh

# 2. 停止服務
echo "🛑 停止應用程式服務..."
sudo systemctl stop cba-frontend.service
sudo systemctl stop cba-backend.service

# 3. 備份當前版本
echo "💾 備份當前應用程式版本..."
cp -r $APP_DIR $BACKUP_DIR/app_backup_$DATE

# 4. 更新程式碼
echo "📥 更新程式碼..."
cd $APP_DIR
git pull origin main

# 5. 更新依賴
echo "📦 更新依賴套件..."
source .venv/bin/activate
cd backend && uv sync
cd ../frontend && pip install -r requirements.txt

# 6. 執行資料庫遷移（如果有）
echo "🗄️ 執行資料庫遷移..."
cd $APP_DIR/backend
python -c "from app.models.database import upgrade_db; upgrade_db()" || echo "⚠️ 無資料庫遷移或遷移失敗"

# 7. 重新啟動服務
echo "🚀 重新啟動服務..."
sudo systemctl start cba-backend.service
sudo systemctl start cba-frontend.service

# 8. 檢查服務狀態
echo "🔍 檢查服務狀態..."
sleep 10
sudo systemctl status cba-backend.service --no-pager
sudo systemctl status cba-frontend.service --no-pager

# 9. 健康檢查
echo "🏥 執行健康檢查..."
$APP_DIR/scripts/health_check.sh

echo "✅ 應用程式更新完成！"
```

### 2. 系統維護腳本

建立 `/opt/cba-system/scripts/system_maintenance.sh`:
```bash
#!/bin/bash

LOG_FILE="/opt/cba-system/logs/maintenance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] 🔧 開始系統維護..." >> $LOG_FILE

# 1. 清理日誌檔案
echo "[$DATE] 📝 清理舊日誌檔案..." >> $LOG_FILE
find /opt/cba-system/logs -name "*.log" -mtime +30 -delete
find /var/log/nginx -name "*.log.*" -mtime +30 -delete

# 2. 清理臨時檔案
echo "[$DATE] 🗑️ 清理臨時檔案..." >> $LOG_FILE
find /tmp -name "streamlit-*" -mtime +7 -delete
find /opt/cba-system -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null

# 3. 更新系統套件
echo "[$DATE] 📦 更新系統套件..." >> $LOG_FILE
sudo apt update && sudo apt upgrade -y >> $LOG_FILE 2>&1

# 4. 檢查磁碟空間
echo "[$DATE] 💾 檢查磁碟空間..." >> $LOG_FILE
df -h >> $LOG_FILE

# 5. 檢查記憶體使用
echo "[$DATE] 🧠 檢查記憶體使用..." >> $LOG_FILE
free -h >> $LOG_FILE

# 6. 重新啟動服務（如果需要）
if [ "$1" = "--restart-services" ]; then
    echo "[$DATE] 🔄 重新啟動服務..." >> $LOG_FILE
    sudo systemctl restart cba-backend.service
    sudo systemctl restart cba-frontend.service
    sudo systemctl reload nginx.service
fi

echo "[$DATE] ✅ 系統維護完成" >> $LOG_FILE
echo "---" >> $LOG_FILE
```

### 3. 災難恢復腳本

建立 `/opt/cba-system/scripts/disaster_recovery.sh`:
```bash
#!/bin/bash

BACKUP_DIR="/opt/cba-system/backups"
APP_DIR="/opt/cba-system"

echo "🚨 災難恢復程序啟動..."

# 列出可用的備份
echo "📋 可用的備份檔案:"
echo "資料庫備份:"
ls -la $BACKUP_DIR/cba_db_backup_*.sql.gz | tail -5
echo ""
echo "應用程式備份:"
ls -la $BACKUP_DIR/cba_files_backup_*.tar.gz | tail -5

# 讓用戶選擇要恢復的備份
read -p "請輸入要恢復的資料庫備份檔案名稱: " db_backup
read -p "請輸入要恢復的應用程式備份檔案名稱 (可選): " app_backup

# 恢復資料庫
if [ -f "$BACKUP_DIR/$db_backup" ]; then
    echo "🗄️ 恢復資料庫..."
    sudo systemctl stop cba-backend.service cba-frontend.service

    # 刪除現有資料庫並重建
    sudo -u postgres psql << EOF
DROP DATABASE IF EXISTS cba_system;
CREATE DATABASE cba_system;
GRANT ALL PRIVILEGES ON DATABASE cba_system TO cba_user;
EOF

    # 恢復資料
    gunzip -c $BACKUP_DIR/$db_backup | sudo -u postgres psql cba_system
    echo "✅ 資料庫恢復完成"
else
    echo "❌ 找不到指定的資料庫備份檔案"
fi

# 恢復應用程式檔案（如果指定）
if [ -n "$app_backup" ] && [ -f "$BACKUP_DIR/$app_backup" ]; then
    echo "📁 恢復應用程式檔案..."
    cd /opt
    tar -xzf $BACKUP_DIR/$app_backup
    echo "✅ 應用程式檔案恢復完成"
fi

# 重新啟動服務
echo "🚀 重新啟動服務..."
sudo systemctl start cba-backend.service cba-frontend.service

echo "✅ 災難恢復完成！"
```

---

## 📋 部署檢查清單

### 部署前檢查
- [ ] 系統需求確認
- [ ] 安全性設定完成
- [ ] 資料庫設定完成
- [ ] SSL憑證配置完成
- [ ] 防火牆規則設定
- [ ] 備份策略建立

### 部署步驟
- [ ] 建立系統用戶和目錄
- [ ] 安裝系統依賴
- [ ] 部署應用程式檔案
- [ ] 配置環境變數
- [ ] 初始化資料庫
- [ ] 設定systemd服務
- [ ] 配置nginx反向代理
- [ ] 測試所有功能

### 部署後驗證
- [ ] 服務狀態檢查
- [ ] 健康檢查端點測試
- [ ] SSL憑證驗證
- [ ] 效能測試
- [ ] 安全性掃描
- [ ] 備份功能測試

---

## 🚀 快速部署腳本

建立 `/opt/cba-system/scripts/quick_deploy.sh`:
```bash
#!/bin/bash

echo "🚀 CBA系統快速部署腳本"
echo "=========================="

# 檢查是否為root用戶
if [ "$EUID" -ne 0 ]; then
    echo "❌ 請使用sudo執行此腳本"
    exit 1
fi

# 設定變數
DOMAIN=""
DB_PASSWORD=""
JWT_SECRET=""
ENCRYPTION_KEY=""

# 獲取用戶輸入
read -p "請輸入域名 (例: example.com): " DOMAIN
read -s -p "請輸入資料庫密碼: " DB_PASSWORD
echo ""
read -s -p "請輸入JWT密鑰 (至少32字元): " JWT_SECRET
echo ""
read -s -p "請輸入加密密鑰 (32字元): " ENCRYPTION_KEY
echo ""

echo "🔧 開始部署..."

# 1. 更新系統
echo "📦 更新系統套件..."
apt update && apt upgrade -y

# 2. 安裝依賴
echo "📥 安裝系統依賴..."
apt install -y python3.9 python3.9-venv python3-pip nginx postgresql postgresql-contrib supervisor git curl wget build-essential libssl-dev libffi-dev python3-dev certbot python3-certbot-nginx

# 3. 建立用戶和目錄
echo "👤 建立系統用戶..."
useradd -r -s /bin/bash -m -d /home/<USER>
mkdir -p /opt/cba-system/{logs,backups,ssl,scripts}
chown -R cba-app:cba-app /opt/cba-system

# 4. 設定PostgreSQL
echo "🗄️ 設定資料庫..."
systemctl start postgresql
systemctl enable postgresql
sudo -u postgres psql << EOF
CREATE DATABASE cba_system;
CREATE USER cba_user WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE cba_system TO cba_user;
ALTER USER cba_user CREATEDB;
\q
EOF

# 5. 建立環境配置檔案
echo "⚙️ 建立環境配置..."
cat > /opt/cba-system/.env.production << EOF
ENVIRONMENT=production
DEBUG=false
HOST=127.0.0.1
PORT=8080
DATABASE_URL=postgresql://cba_user:$DB_PASSWORD@localhost/cba_system
JWT_SECRET_KEY=$JWT_SECRET
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480
ENCRYPTION_KEY=$ENCRYPTION_KEY
FRONTEND_URL=https://$DOMAIN
ALLOWED_HOSTS=$DOMAIN,www.$DOMAIN
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365
API_RATE_LIMIT=100
MAX_QUERY_RESULTS=1000
EOF

chown cba-app:cba-app /opt/cba-system/.env.production
chmod 600 /opt/cba-system/.env.production

# 6. 獲取SSL憑證
echo "🔐 獲取SSL憑證..."
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

echo "✅ 基礎部署完成！"
echo ""
echo "📋 接下來的步驟:"
echo "1. 將應用程式檔案複製到 /opt/cba-system/"
echo "2. 執行 /opt/cba-system/scripts/update_application.sh"
echo "3. 配置nginx (使用提供的配置檔案)"
echo "4. 啟動服務"
echo ""
echo "🌐 您的網站將在以下地址可用:"
echo "   https://$DOMAIN"
```

---

## 📞 技術支援與故障排除

### 常見問題

1. **服務無法啟動**
   ```bash
   # 檢查服務狀態
   sudo systemctl status cba-backend.service
   sudo journalctl -u cba-backend.service -f
   ```

2. **資料庫連接失敗**
   ```bash
   # 檢查PostgreSQL狀態
   sudo systemctl status postgresql
   sudo -u postgres psql -c "\l"
   ```

3. **SSL憑證問題**
   ```bash
   # 檢查憑證狀態
   sudo certbot certificates
   # 手動更新憑證
   sudo certbot renew
   ```

4. **效能問題**
   ```bash
   # 檢查系統資源
   htop
   iotop
   df -h
   ```

### 緊急聯絡資訊
- 系統管理員: [您的聯絡資訊]
- 技術支援: [技術支援聯絡方式]
- 緊急熱線: [緊急聯絡電話]

---

## 📚 相關文檔

- [應用程式使用手冊](./使用手冊.md)
- [API文檔](http://localhost:8080/docs)
- [安全性政策](./安全性政策.md)
- [備份與恢復程序](./備份恢復程序.md)

---

**注意**: 本指南提供了完整的生產環境部署流程。請根據您的具體環境和需求進行調整。在生產環境中部署前，建議先在測試環境中完整測試所有功能。

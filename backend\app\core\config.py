"""
應用程式配置設定
"""

import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import ConfigDict, ValidationError
from dotenv import load_dotenv

# 載入環境變數（從backend目錄）
load_dotenv()


class Settings(BaseSettings):
    """應用程式設定類別"""
    
    # 基本設定
    APP_NAME: str = "CBA個人資料搜集系統"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "True").lower() == "true"
    
    # 伺服器設定
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8080"))  # 修改為8080避免與SSO服務衝突
    
    # 資料庫設定
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./database/cba_personal_data.db")
    
    # JWT設定
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "your-secret-key-here-change-in-production")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "480"))  # 8小時
    
    # 加密設定
    ENCRYPTION_KEY: str = os.getenv("ENCRYPTION_KEY", "your-encryption-key-here-change-in-production")
    
    # OAuth2 SSO設定已移除 - 只保留SOAP SSO
    
    # SOAP SSO設定 (政府SSO)
    sso_soap_ws_url: str = os.getenv("SSO_SOAP_WS_URL", "https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL")
    
    # 前端設定
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")  # 改為Vue.js預設埠

    # CORS設定 - 支援Vue.js開發和生產環境
    CORS_ORIGINS: List[str] = [
        "http://localhost",
        "http://localhost:3000",  # Vue.js開發服務器
        "http://localhost:5173",  # Vite開發服務器
        "http://localhost:8080",  # Vue.js替代埠
        "http://localhost:8501",  # 保留Streamlit支援
        "http://127.0.0.1",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:8080",
        "http://127.0.0.1:8501",
        "http://0.0.0.0:8501"
    ]

    # 允許的主機（安全設定）
    ALLOWED_HOSTS: List[str] = [
        "localhost",
        "127.0.0.1",
        "0.0.0.0"
    ]
    
    # 審計設定
    ENABLE_AUDIT_LOG: bool = os.getenv("ENABLE_AUDIT_LOG", "True").lower() == "true"
    AUDIT_LOG_RETENTION_DAYS: int = int(os.getenv("AUDIT_LOG_RETENTION_DAYS", "365"))
    
    # 密碼政策設定
    MIN_PASSWORD_LENGTH: int = int(os.getenv("MIN_PASSWORD_LENGTH", "8"))
    REQUIRE_UPPERCASE: bool = os.getenv("REQUIRE_UPPERCASE", "True").lower() == "true"
    REQUIRE_LOWERCASE: bool = os.getenv("REQUIRE_LOWERCASE", "True").lower() == "true"
    REQUIRE_NUMBERS: bool = os.getenv("REQUIRE_NUMBERS", "True").lower() == "true"
    REQUIRE_SPECIAL_CHARS: bool = os.getenv("REQUIRE_SPECIAL_CHARS", "False").lower() == "true"
    
    # API限制設定
    API_RATE_LIMIT: int = int(os.getenv("API_RATE_LIMIT", "100"))  # 每分鐘請求次數
    MAX_QUERY_RESULTS: int = int(os.getenv("MAX_QUERY_RESULTS", "1000"))  # 最大查詢結果數量
    
    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=False,
        extra='ignore'
    )


# 建立全域設定實例
settings = Settings()

# --- 安全性增強：檢查生產環境中的預設密鑰 ---
if settings.ENVIRONMENT.lower() == "production":
    if settings.JWT_SECRET_KEY == "your-secret-key-here-change-in-production":
        raise ValueError("安全風險：在生產環境中偵測到預設的JWT_SECRET_KEY！請在.env檔案或環境變數中設定一個安全的密鑰。")
    if settings.ENCRYPTION_KEY == "your-encryption-key-here-change-in-production":
        raise ValueError("安全風險：在生產環境中偵測到預設的ENCRYPTION_KEY！請在.env檔案或環境變數中設定一個安全的密鑰。")


def get_settings():
    """取得設定實例"""
    return settings
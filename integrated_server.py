#!/usr/bin/env python3
"""
CBA受款人資料搜集系統 - 純Python整合服務器
不依賴nginx，直接在80埠提供整合服務

依賴套件：
pip install aiohttp
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
import subprocess
import time
import threading

try:
    import aiohttp
    from aiohttp import web, ClientSession
except ImportError:
    print("❌ 錯誤: 缺少 aiohttp 套件")
    print("請執行: pip install aiohttp")
    sys.exit(1)

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedServer:
    """整合服務器"""
    
    def __init__(self):
        self.app = web.Application()
        self.setup_routes()
        self.services = {}
        self.running = False
        
        # 子服務配置 (僅前端+後端)
        self.service_configs = {
            "backend": {
                "name": "後端API",
                "port": 8080,
                "url": "http://localhost:8080",
                "cwd": Path(__file__).parent / "backend",
                "command": ["uv", "run", "uvicorn", "main:app", "--host", "127.0.0.1", "--port", "8080"]
            },
            "frontend": {
                "name": "前端UI",
                "port": 8501,
                "url": "http://localhost:8501",
                "cwd": Path(__file__).parent / "frontend",
                "command": ["streamlit", "run", "main.py", "--server.port", "8501", "--server.address", "127.0.0.1", "--server.headless", "true"]
            }
        }
    
    def setup_routes(self):
        """設定路由"""
        # 健康檢查
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/status', self.status_check)
        
        # API代理
        self.app.router.add_route('*', '/api/{path:.*}', self.proxy_backend)

        # 前端代理（所有其他請求）
        self.app.router.add_route('*', '/{path:.*}', self.proxy_frontend)
    
    async def health_check(self, request):
        """健康檢查端點"""
        status = {
            "status": "healthy",
            "service": "CBA受款人資料搜集系統整合服務",
            "version": "1.0.0",
            "services": {}
        }
        
        # 檢查各子服務狀態
        for service_name, config in self.service_configs.items():
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                    health_url = f"{config['url']}/health" if service_name == "backend" else config['url']
                    async with session.get(health_url) as resp:
                        status["services"][service_name] = {
                            "status": "healthy" if resp.status == 200 else "unhealthy",
                            "port": config["port"]
                        }
            except Exception as e:
                status["services"][service_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "port": config["port"]
                }
        
        return web.json_response(status)
    
    async def status_check(self, request):
        """簡單狀態檢查"""
        return web.Response(text="OK")
    
    async def proxy_backend(self, request):
        """代理後端API請求"""
        return await self.proxy_request(request, "backend", "/api")

    async def proxy_frontend(self, request):
        """代理前端請求"""
        return await self.proxy_request(request, "frontend", "")
    
    async def proxy_request(self, request, service_name, prefix):
        """通用代理請求處理"""
        config = self.service_configs[service_name]
        
        # 構建目標URL
        path = request.path_qs
        if prefix and path.startswith(prefix):
            path = path[len(prefix):]
        if not path.startswith('/'):
            path = '/' + path
        
        target_url = f"{config['url']}{path}"
        
        try:
            # 準備請求數據
            headers = dict(request.headers)
            # 移除可能導致問題的標頭
            headers.pop('host', None)
            headers.pop('content-length', None)
            
            # 讀取請求體
            data = await request.read() if request.can_read_body else None
            
            # 發送代理請求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    data=data,
                    params=request.query
                ) as resp:
                    # 讀取回應
                    body = await resp.read()
                    
                    # 準備回應標頭
                    response_headers = {}
                    for key, value in resp.headers.items():
                        if key.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                            response_headers[key] = value
                    
                    return web.Response(
                        body=body,
                        status=resp.status,
                        headers=response_headers
                    )
        
        except Exception as e:
            logger.error(f"代理請求失敗 {service_name}: {e}")
            return web.json_response(
                {"error": f"服務 {config['name']} 不可用", "detail": str(e)},
                status=502
            )
    
    def start_service(self, service_name):
        """啟動子服務"""
        config = self.service_configs[service_name]
        logger.info(f"🚀 啟動 {config['name']}...")
        
        try:
            process = subprocess.Popen(
                config["command"],
                cwd=config["cwd"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services[service_name] = process
            
            # 等待服務啟動
            time.sleep(3)
            
            # 檢查服務是否正常啟動
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {config['name']} 啟動失敗")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
            
            logger.info(f"✅ {config['name']} 已啟動在埠 {config['port']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 啟動 {config['name']} 時發生錯誤: {e}")
            return False
    
    def stop_service(self, service_name):
        """停止子服務"""
        if service_name in self.services:
            process = self.services[service_name]
            config = self.service_configs[service_name]
            
            logger.info(f"🛑 停止 {config['name']}...")
            
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️ {config['name']} 未在10秒內停止，強制終止")
                process.kill()
                process.wait()
            
            del self.services[service_name]
            logger.info(f"✅ {config['name']} 已停止")
    
    def start_all_services(self):
        """啟動所有子服務"""
        logger.info("🚀 啟動所有子服務...")

        service_order = ["backend", "frontend"]
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ 啟動 {self.service_configs[service_name]['name']} 失敗")
                self.stop_all_services()
                return False
        
        logger.info("🎉 所有子服務已啟動！")
        return True
    
    def stop_all_services(self):
        """停止所有子服務"""
        logger.info("🛑 停止所有子服務...")

        service_order = ["frontend", "backend"]
        
        for service_name in service_order:
            if service_name in self.services:
                self.stop_service(service_name)
    
    async def start_server(self):
        """啟動整合服務器"""
        # 啟動子服務
        if not self.start_all_services():
            return False
        
        # 啟動整合服務器
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, '0.0.0.0', 80)
        await site.start()
        
        self.running = True
        logger.info("🌐 整合服務器已啟動在 http://localhost:80")
        logger.info("📋 服務路由:")
        logger.info("  • 前端UI: http://localhost:80/")
        logger.info("  • 後端API: http://localhost:80/api/")
        logger.info("  • 健康檢查: http://localhost:80/health")
        
        return True
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        logger.info("📡 收到停止信號，正在關閉系統...")
        self.stop_all_services()
        self.running = False
        sys.exit(0)

async def main():
    """主函數"""
    print("🏢 CBA受款人資料搜集系統 - 純Python整合服務器")
    print("=" * 60)
    
    server = IntegratedServer()
    
    # 註冊信號處理器
    signal.signal(signal.SIGINT, server.signal_handler)
    signal.signal(signal.SIGTERM, server.signal_handler)
    
    try:
        # 啟動服務器
        if await server.start_server():
            # 保持運行
            while server.running:
                await asyncio.sleep(1)
        else:
            logger.error("❌ 服務器啟動失敗")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("📡 收到中斷信號")
    finally:
        server.stop_all_services()

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
身分證字號驗證測試腳本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.utils.validation import validate_id_number, get_id_number_errors

def test_id_validation():
    """測試身分證字號驗證功能"""
    print("🔍 開始測試身分證字號驗證功能...")
    print("=" * 60)
    
    # 測試案例
    test_cases = [
        # 有效的身分證字號
        ("A123456789", True, "有效身分證字號"),
        ("B234567890", True, "有效身分證字號"),
        ("C345678901", True, "有效身分證字號"),
        ("D456789012", True, "有效身分證字號"),
        ("E567890123", True, "有效身分證字號"),
        ("F678901234", True, "有效身分證字號"),
        ("G789012345", True, "有效身分證字號"),
        ("H890123456", True, "有效身分證字號"),
        ("J901234567", True, "有效身分證字號"),
        ("K012345678", True, "有效身分證字號"),
        
        # 無效的身分證字號
        ("", False, "空字串"),
        ("A12345678", False, "長度不足"),
        ("A1234567890", False, "長度過長"),
        ("123456789", False, "沒有英文字母"),
        ("ABCDEFGHIJ", False, "全部英文字母"),
        ("A12345678A", False, "最後一位是英文字母"),
        ("a123456789", False, "小寫英文字母"),
        ("A123456788", False, "檢查碼錯誤"),
        ("Z999999999", False, "檢查碼錯誤"),
        ("A123-56789", False, "包含特殊字元"),
        ("A123 56789", False, "包含空格"),
        ("A12345678X", False, "包含無效字元"),
        ("?123456789", False, "開頭非英文字母"),
        ("A1234567八九", False, "包含中文字元"),
        ("A123456789 ", False, "尾部有空格"),
        (" A123456789", False, "開頭有空格"),
    ]
    
    # 計算一些真正有效的身分證字號
    valid_real_ids = []
    
    # 使用標準演算法生成一些有效的身分證字號
    letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 
              'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    # 生成一些有效的身分證字號
    for letter in ['A', 'B', 'C', 'D', 'E']:
        for gender in ['1', '2']:
            for i in range(1, 4):
                # 生成前8碼
                id_prefix = f"{letter}{gender}{i:07d}"
                
                # 計算檢查碼
                letter_value = letter_values[letter]
                checksum = (letter_value // 10) * 1 + (letter_value % 10) * 9
                
                for j in range(1, 9):
                    checksum += int(id_prefix[j]) * (9 - j)
                
                check_digit = (10 - (checksum % 10)) % 10
                
                # 完整身分證字號
                full_id = f"{id_prefix}{check_digit}"
                valid_real_ids.append(full_id)
    
    # 將真正有效的身分證字號添加到測試案例
    for valid_id in valid_real_ids[:5]:  # 取前5個
        test_cases.insert(0, (valid_id, True, "計算生成的有效身分證字號"))
    
    # 執行測試
    total_tests = len(test_cases)
    passed_tests = 0
    failed_tests = []
    
    for i, (id_number, expected_valid, description) in enumerate(test_cases, 1):
        try:
            # 測試驗證函數
            is_valid = validate_id_number(id_number)
            
            # 取得錯誤訊息
            errors = get_id_number_errors(id_number)
            
            # 檢查結果
            if is_valid == expected_valid:
                status = "✅ 通過"
                passed_tests += 1
            else:
                status = "❌ 失敗"
                failed_tests.append((id_number, description, expected_valid, is_valid))
            
            # 輸出結果
            print(f"測試 {i:2d}: {status}")
            print(f"  身分證字號: {id_number!r}")
            print(f"  描述: {description}")
            print(f"  預期結果: {'有效' if expected_valid else '無效'}")
            print(f"  實際結果: {'有效' if is_valid else '無效'}")
            
            if errors:
                print(f"  錯誤訊息: {', '.join(errors)}")
            
            print()
            
        except Exception as e:
            print(f"測試 {i:2d}: ⚠️ 異常")
            print(f"  身分證字號: {id_number!r}")
            print(f"  錯誤: {str(e)}")
            print()
            failed_tests.append((id_number, description, expected_valid, f"Exception: {e}"))
    
    # 輸出測試總結
    print("=" * 60)
    print("📊 測試結果總結")
    print(f"總測試數: {total_tests}")
    print(f"通過數量: {passed_tests}")
    print(f"失敗數量: {len(failed_tests)}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if failed_tests:
        print("\n❌ 失敗的測試:")
        for id_number, description, expected, actual in failed_tests:
            print(f"  • {id_number!r}: {description}")
            print(f"    預期: {'有效' if expected else '無效'}, 實際: {actual}")
    
    print("\n✅ 測試完成!")
    return passed_tests, len(failed_tests)

def test_frontend_validation():
    """測試前端驗證函數"""
    print("\n🔍 開始測試前端驗證功能...")
    print("=" * 60)
    
    # 導入前端驗證函數
    sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
    
    try:
        from frontend.components.personal_data_form import validate_id_number as frontend_validate_id_number
        
        # 測試一些案例
        test_cases = [
            ("A123456789", True),
            ("B234567890", True),
            ("", False),
            ("A12345678", False),
            ("123456789", False),
            ("A123456788", False),
        ]
        
        for id_number, expected in test_cases:
            try:
                is_valid, error_msg = frontend_validate_id_number(id_number)
                status = "✅ 通過" if is_valid == expected else "❌ 失敗"
                print(f"{status}: {id_number!r} -> {'有效' if is_valid else '無效'}")
                if error_msg:
                    print(f"  錯誤訊息: {error_msg}")
            except Exception as e:
                print(f"⚠️ 異常: {id_number!r} -> {e}")
        
        print("✅ 前端驗證測試完成!")
        
    except ImportError as e:
        print(f"⚠️ 無法導入前端驗證函數: {e}")

if __name__ == "__main__":
    print("🧪 身分證字號驗證功能測試")
    print("=" * 60)
    
    # 測試後端驗證
    backend_passed, backend_failed = test_id_validation()
    
    # 測試前端驗證
    test_frontend_validation()
    
    print("\n🎯 測試總結:")
    print(f"後端驗證: {backend_passed} 通過, {backend_failed} 失敗")
    
    if backend_failed == 0:
        print("🎉 所有測試通過！身分證字號驗證功能正常運作")
    else:
        print("⚠️ 部分測試失敗，請檢查驗證邏輯") 
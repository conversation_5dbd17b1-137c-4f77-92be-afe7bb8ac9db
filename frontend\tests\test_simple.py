"""
簡化測試 - 測試基本功能，避免複雜依賴
"""

import pytest
import sys
import os

# 確保可以導入組件
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_id_number_validation():
    """測試身分證字號驗證功能"""
    from components.personal_data_form import validate_id_number
    
    # 測試有效身分證字號
    valid_ids = ["A123456789", "B234567890"]
    for id_num in valid_ids:
        is_valid, error_msg = validate_id_number(id_num)
        assert is_valid, f"身分證字號 {id_num} 應該有效，但驗證失敗: {error_msg}"
    
    # 測試無效身分證字號
    invalid_ids = ["", "123456789", "A12345678", "A1234567890"]
    for id_num in invalid_ids:
        is_valid, error_msg = validate_id_number(id_num)
        assert not is_valid, f"身分證字號 {id_num} 應該無效，但驗證通過"

def test_data_export():
    """測試資料匯出功能"""
    from components.data_query import export_data_to_csv
    
    sample_data = [
        {
            "id": 1,
            "name": "測試用戶",
            "id_number_masked": "A123****89",
            "address": "台北市",
            "notes": "測試",
            "created_by": "test_user",
            "created_at": "2024-12-19 10:30:00",
            "updated_at": "2024-12-19 10:30:00"
        }
    ]
    
    user_info = {"roles": ["admin"]}
    csv_data = export_data_to_csv(sample_data, user_info)
    
    # 檢查是否產生CSV資料
    assert isinstance(csv_data, bytes)
    assert len(csv_data) > 0

def test_permission_logic():
    """測試權限邏輯"""
    # 模擬權限檢查
    def has_permission(user_permissions, required_permission):
        return required_permission in user_permissions
    
    # 測試一般用戶權限
    general_permissions = ["CREATE_PERSONAL_DATA", "READ_OWN_DATA"]
    assert has_permission(general_permissions, "CREATE_PERSONAL_DATA") == True
    assert has_permission(general_permissions, "READ_ALL_DATA") == False
    
    # 測試管理者權限
    admin_permissions = ["CREATE_PERSONAL_DATA", "READ_ALL_DATA", "MANAGE_USERS"]
    assert has_permission(admin_permissions, "CREATE_PERSONAL_DATA") == True
    assert has_permission(admin_permissions, "READ_ALL_DATA") == True
    assert has_permission(admin_permissions, "MANAGE_USERS") == True

def test_data_filtering():
    """測試資料篩選"""
    sample_data = [
        {"name": "張三", "created_by": "user1"},
        {"name": "李四", "created_by": "user2"},
        {"name": "王五", "created_by": "user1"}
    ]
    
    # 測試按建立者篩選
    def filter_by_creator(data, creator):
        return [item for item in data if item["created_by"] == creator]
    
    user1_data = filter_by_creator(sample_data, "user1")
    assert len(user1_data) == 2
    
    user2_data = filter_by_creator(sample_data, "user2")
    assert len(user2_data) == 1

def test_data_masking():
    """測試身分證字號遮罩"""
    def mask_id_number(id_number):
        if len(id_number) == 10:
            return f"{id_number[:4]}****{id_number[-2:]}"
        return id_number
    
    # 測試遮罩功能
    test_cases = [
        ("A123456789", "A123****89"),
        ("B234567890", "B234****90"),
    ]
    
    for original, expected in test_cases:
        masked = mask_id_number(original)
        assert masked == expected, f"遮罩結果不正確: {original} -> {masked}, 期望: {expected}"

def test_pagination():
    """測試分頁功能"""
    # 建立測試資料
    test_data = [{"id": i, "name": f"用戶{i}"} for i in range(25)]
    
    def paginate(data, page, page_size):
        start = (page - 1) * page_size
        end = start + page_size
        return data[start:end]
    
    # 測試第一頁
    page1 = paginate(test_data, 1, 10)
    assert len(page1) == 10
    assert page1[0]["id"] == 0
    
    # 測試第二頁
    page2 = paginate(test_data, 2, 10)
    assert len(page2) == 10
    assert page2[0]["id"] == 10
    
    # 測試最後一頁
    page3 = paginate(test_data, 3, 10)
    assert len(page3) == 5  # 剩餘5筆
    assert page3[0]["id"] == 20

def test_form_validation():
    """測試表單驗證邏輯"""
    import re
    
    # 測試姓名驗證
    def validate_name(name):
        if not name or not name.strip():
            return False, "姓名不能為空"
        
        name = name.strip()
        if len(name) < 2 or len(name) > 20:
            return False, "姓名長度應在2-20字符之間"
        
        if not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', name):
            return False, "姓名格式不正確"
        
        return True, ""
    
    # 測試有效姓名
    valid_names = ["張三", "李小明", "John Smith"]
    for name in valid_names:
        is_valid, error = validate_name(name)
        assert is_valid, f"姓名 {name} 應該有效: {error}"
    
    # 測試無效姓名
    invalid_names = ["", "A", "123", "張三@"]
    for name in invalid_names:
        is_valid, error = validate_name(name)
        assert not is_valid, f"姓名 {name} 應該無效"

if __name__ == "__main__":
    # 直接執行簡單測試
    print("執行簡化測試...")
    
    test_functions = [
        test_id_number_validation,
        test_data_export,
        test_permission_logic,
        test_data_filtering,
        test_data_masking,
        test_pagination,
        test_form_validation
    ]
    
    passed = 0
    failed = 0
    
    for test_func in test_functions:
        try:
            test_func()
            print(f"✅ {test_func.__name__}")
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__}: {e}")
            failed += 1
    
    print(f"\n測試結果: {passed} 通過, {failed} 失敗") 
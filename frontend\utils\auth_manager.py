"""
認證管理模組
處理用戶認證、權限檢查、會話管理等功能
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse, parse_qs
from .api_client import api_client


class AuthManager:
    """認證管理器"""
    
    @staticmethod
    def init_session_state():
        """初始化Streamlit會話狀態"""
        if 'logged_in' not in st.session_state:
            st.session_state.logged_in = False
        
        if 'user_info' not in st.session_state:
            st.session_state.user_info = None
        
        if 'access_token' not in st.session_state:
            st.session_state.access_token = None
        
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "登入"
        
        # 新增：Token驗證緩存
        if 'token_verified' not in st.session_state:
            st.session_state.token_verified = False
        
        if 'token_verify_time' not in st.session_state:
            st.session_state.token_verify_time = None
    
    @staticmethod
    def check_url_token():
        """檢查URL中是否有token參數（SSO回調）"""
        try:
            # 嘗試使用新的 API
            try:
                query_params = st.query_params
                if 'token' in query_params:
                    token = query_params['token']
                    if token:
                        # 設置token並清除URL參數（不要加Bearer前綴，API客戶端會自動加上）
                        st.session_state.access_token = token
                        
                        # 清除URL參數
                        st.query_params.clear()
                        
                        # 取得用戶資訊並設置登入狀態
                        try:
                            user_info = api_client.get_current_user()
                            st.session_state.user_info = user_info
                            st.session_state.logged_in = True
                            st.session_state.current_page = "資料概覽"
                            
                            # 設置Token驗證緩存
                            import time
                            st.session_state.token_verified = True
                            st.session_state.token_verify_time = time.time()
                            
                            st.success("🎉 SSO登入成功！歡迎使用個人資料搜集系統")
                            st.rerun()
                        except Exception as e:
                            st.error(f"取得用戶資訊失敗: {str(e)}")
                            AuthManager.logout()
            except AttributeError:
                # 回退到舊的 API
                query_params = st.experimental_get_query_params()
                if 'token' in query_params:
                    token = query_params['token'][0]
                    if token:
                        # 設置token並清除URL參數（不要加Bearer前綴，API客戶端會自動加上）
                        st.session_state.access_token = token
                        st.experimental_set_query_params()  # 清除URL參數
                        
                        # 取得用戶資訊並設置登入狀態
                        try:
                            user_info = api_client.get_current_user()
                            st.session_state.user_info = user_info
                            st.session_state.logged_in = True
                            st.session_state.current_page = "dashboard"
                            
                            # 設置Token驗證緩存
                            import time
                            st.session_state.token_verified = True
                            st.session_state.token_verify_time = time.time()
                            
                            st.success("🎉 SSO登入成功！歡迎使用個人資料搜集系統")
                            st.rerun()
                        except Exception as e:
                            st.error(f"取得用戶資訊失敗: {str(e)}")
                            AuthManager.logout()
        except Exception as e:
            # URL參數處理失敗，忽略錯誤
            pass
    
    @staticmethod
    def check_authentication() -> bool:
        """檢查用戶認證狀態"""
        if not st.session_state.logged_in or not st.session_state.access_token:
            return False
        
        # 檢查是否需要驗證Token（避免重複請求）
        import time
        current_time = time.time()
        
        # 如果最近30秒內已經驗證過，直接返回緩存結果
        if (st.session_state.token_verified and 
            st.session_state.token_verify_time and
            current_time - st.session_state.token_verify_time < 30):
            return True
        
        # 驗證token是否仍然有效
        try:
            verify_result = api_client.verify_token()
            if not verify_result.get("valid", False):
                AuthManager.logout()
                return False
            
            # 更新驗證緩存
            st.session_state.token_verified = True
            st.session_state.token_verify_time = current_time
            return True
        except Exception:
            AuthManager.logout()
            return False
    
    @staticmethod
    def login(username: str, password: str) -> bool:
        """用戶登入"""
        try:
            login_result = api_client.login(username, password)
            
            # 設置會話狀態
            st.session_state.access_token = login_result["access_token"]
            st.session_state.user_info = login_result["user_info"]
            st.session_state.logged_in = True
            st.session_state.current_page = "dashboard"
            
            # 設置Token驗證緩存
            import time
            st.session_state.token_verified = True
            st.session_state.token_verify_time = time.time()
            
            st.success("登入成功！")
            return True
            
        except Exception as e:
            st.error(f"登入失敗: {str(e)}")
            return False
    
    @staticmethod
    def sso_login():
        """SSO登入"""
        try:
            # 取得SSO登入URL並重導向
            sso_url = api_client.get_sso_login_url(redirect_url="/")
            st.markdown(f'<meta http-equiv="refresh" content="0; url={sso_url}">', unsafe_allow_html=True)
            st.info("正在導向SSO認證頁面...")
        except Exception as e:
            st.error(f"SSO登入失敗: {str(e)}")
    
    @staticmethod
    def logout():
        """用戶登出"""
        try:
            # 向後端發送登出請求
            if st.session_state.get('access_token'):
                api_client.logout()
        except Exception:
            # 登出請求失敗時仍清除本地狀態
            pass
        finally:
            # 清除會話狀態
            st.session_state.logged_in = False
            st.session_state.user_info = None
            st.session_state.access_token = None
            st.session_state.current_page = "login"
            
            # 清除Token驗證緩存
            st.session_state.token_verified = False
            st.session_state.token_verify_time = None
            
            st.rerun()
    
    @staticmethod
    def get_current_user() -> Optional[Dict[str, Any]]:
        """取得當前用戶資訊"""
        return st.session_state.get('user_info')
    
    @staticmethod
    def get_user_roles() -> List[str]:
        """取得當前用戶角色"""
        if not st.session_state.user_info:
            return []
        return st.session_state.user_info.get('roles', [])
    
    @staticmethod
    def has_role(role: str) -> bool:
        """檢查用戶是否具有指定角色"""
        return role in AuthManager.get_user_roles()
    
    @staticmethod
    def has_any_role(roles: List[str]) -> bool:
        """檢查用戶是否具有任一指定角色"""
        user_roles = AuthManager.get_user_roles()
        return any(role in user_roles for role in roles)
    
    @staticmethod
    def is_admin() -> bool:
        """檢查是否為管理員"""
        return AuthManager.has_any_role(['admin', 'global'])
    
    @staticmethod
    def is_global_user() -> bool:
        """檢查是否為全域用戶"""
        return AuthManager.has_role('global')
    
    @staticmethod
    def is_general_user() -> bool:
        """檢查是否為一般用戶"""
        return AuthManager.has_role('user')
    
    @staticmethod
    def can_create_data() -> bool:
        """檢查是否可以建立資料"""
        return True  # 所有已登入用戶都可以建立資料
    
    @staticmethod
    def can_read_all_data() -> bool:
        """檢查是否可以讀取所有資料"""
        return AuthManager.is_admin()
    
    @staticmethod
    def can_export_data() -> bool:
        """檢查是否可以匯出資料"""
        return AuthManager.is_admin()
    
    @staticmethod
    def can_manage_users() -> bool:
        """檢查是否可以管理用戶"""
        return AuthManager.is_admin()
    
    @staticmethod
    def can_view_audit_logs() -> bool:
        """檢查是否可以查看審計日誌"""
        return AuthManager.is_admin()
    
    @staticmethod
    def get_navigation_pages() -> List[Dict[str, str]]:
        """取得導覽頁面列表"""
        pages = [
            {
                "key": "create_data",
                "title": "💰 受款人管理",
                "requires_role": None
            }
        ]

        # 管理員和全域用戶可見的頁面
        if AuthManager.is_admin() or AuthManager.has_role("global"):
            pages.insert(0, {
                "key": "dashboard",
                "title": "📊 資料概覽",
                "requires_role": ["admin", "global"]
            })

        # 管理員專用頁面
        if AuthManager.is_admin():
            pages.extend([
                {
                    "key": "statistics",
                    "title": "📈 統計報表",
                    "requires_role": ["admin", "global"]
                },
                {
                    "key": "data_export",
                    "title": "💾 資料匯出",
                    "requires_role": ["admin", "global"]
                },
                {
                    "key": "user_management",
                    "title": "👥 用戶管理",
                    "requires_role": ["admin", "global"]
                },
                {
                    "key": "role_management",
                    "title": "🔑 角色管理",
                    "requires_role": ["admin"]
                },
                {
                    "key": "department_management",
                    "title": "🏢 部門管理",
                    "requires_role": ["admin"]
                },
                {
                    "key": "audit_log",
                    "title": "📋 審計日誌",
                    "requires_role": ["admin", "global"]
                }
            ])
        
        return pages
    
    @staticmethod
    def require_authentication():
        """要求用戶認證"""
        if not AuthManager.check_authentication():
            st.error("請先登入系統")
            st.session_state.current_page = "login"
            st.rerun()
    
    @staticmethod
    def require_role(required_roles: List[str], error_message: str = "您沒有權限訪問此功能"):
        """要求用戶具有指定角色"""
        if not AuthManager.has_any_role(required_roles):
            st.error(error_message)
            st.session_state.current_page = "dashboard"
            st.rerun()


# 建立全域認證管理器實例
auth_manager = AuthManager() 
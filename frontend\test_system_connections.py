#!/usr/bin/env python3
"""
CBA 系統連接測試腳本
測試前端、後端、SSO 服務之間的連接
"""

import requests
import sys
import json
from datetime import datetime
from typing import Dict, Any

def test_connection(url: str, description: str, timeout: int = 5) -> Dict[str, Any]:
    """測試單一連接"""
    try:
        response = requests.get(url, timeout=timeout, allow_redirects=False)
        return {
            "success": True,
            "status_code": response.status_code,
            "response_time": response.elapsed.total_seconds(),
            "description": description
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": str(e),
            "description": description
        }

def test_sso_service():
    """測試 SSO 服務"""
    print("🔑 測試 SSO 服務連接...")
    
    # 測試 SSO 服務首頁
    result = test_connection("http://127.0.0.1:8000/", "SSO 服務首頁")
    if result["success"]:
        print(f"✅ SSO 服務首頁正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ SSO 服務首頁連接失敗: {result['error']}")
        return False
    
    # 測試 WSDL 端點
    result = test_connection("http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx?wsdl", "SOAP WSDL 端點")
    if result["success"]:
        print(f"✅ SOAP WSDL 端點正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ SOAP WSDL 端點連接失敗: {result['error']}")
        return False
    
    # 測試 SSO 認證
    sso_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
    result = test_connection(
        f"http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx",
        "SOAP 服務端點"
    )
    if result["success"]:
        print(f"✅ SOAP 服務端點正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ SOAP 服務端點連接失敗: {result['error']}")
    
    return True

def test_backend_api():
    """測試後端 API 服務"""
    print("\n🖥️  測試後端 API 服務...")
    
    # 測試健康檢查
    result = test_connection("http://127.0.0.1:8080/health", "後端健康檢查")
    if result["success"]:
        print(f"✅ 後端健康檢查正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ 後端健康檢查失敗: {result['error']}")
        return False
    
    # 測試根路徑
    result = test_connection("http://127.0.0.1:8080/", "後端根路徑")
    if result["success"]:
        print(f"✅ 後端根路徑正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ 後端根路徑連接失敗: {result['error']}")
    
    # 測試 SSO 登入端點
    result = test_connection("http://127.0.0.1:8080/api/v1/auth/sso_login", "SSO 登入端點")
    if result["success"]:
        print(f"✅ SSO 登入端點正常 (狀態碼: {result['status_code']})")
    else:
        print(f"❌ SSO 登入端點連接失敗: {result['error']}")
    
    return True

def test_sso_integration():
    """測試 SSO 整合"""
    print("\n🔗 測試 SSO 整合...")
    
    # 測試完整的 SSO 登入流程
    sso_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
    sso_url = f"http://127.0.0.1:8080/api/v1/auth/sso_login?ssoToken1={sso_token}"
    
    try:
        response = requests.get(sso_url, allow_redirects=False, timeout=10)
        print(f"📡 SSO 登入請求狀態碼: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"🔄 重導向到: {redirect_url}")
            
            if 'error' in redirect_url:
                print("❌ SSO 登入失敗 - 發生錯誤")
                return False
            else:
                print("✅ SSO 登入成功 - 正常重導向")
                return True
        else:
            print(f"⚠️  SSO 登入回應異常 (狀態碼: {response.status_code})")
            if response.text:
                print(f"回應內容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ SSO 登入測試失敗: {e}")
        return False

def main():
    """主要測試函數"""
    print("=" * 60)
    print("🧪 CBA 系統連接測試")
    print("=" * 60)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試結果統計
    tests_passed = 0
    total_tests = 3
    
    # 測試 SSO 服務
    if test_sso_service():
        tests_passed += 1
    
    # 測試後端 API
    if test_backend_api():
        tests_passed += 1
    
    # 測試 SSO 整合
    if test_sso_integration():
        tests_passed += 1
    
    # 顯示測試結果
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {tests_passed}/{total_tests} 通過")
    
    if tests_passed == total_tests:
        print("🎉 所有測試通過！系統配置正確。")
        sys.exit(0)
    else:
        print("❌ 有測試失敗，請檢查系統配置。")
        sys.exit(1)

if __name__ == "__main__":
    main() 
"""
FastAPI前端路由
"""
from fastapi import APIRouter, Request, Depends, Form, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import logging

from app.models.database import get_db
from app.models.user import User
from app.core.dependencies import get_current_user_optional, get_current_user_from_cookie
from app.utils.jwt_auth import create_access_token, verify_password
from app.utils.audit import log_operation

logger = logging.getLogger(__name__)

# 創建路由器
frontend_router = APIRouter()

# 設定模板
templates = Jinja2Templates(directory="app/frontend/templates")

@frontend_router.get("/", response_class=HTMLResponse)
async def home(request: Request, current_user: User = Depends(get_current_user_optional)):
    """首頁"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)
    
    # 如果已登入，重定向到儀表板
    return RedirectResponse(url="/dashboard", status_code=302)

@frontend_router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request, current_user: User = Depends(get_current_user_optional)):
    """登入頁面"""
    if current_user:
        return RedirectResponse(url="/dashboard", status_code=302)
    
    return templates.TemplateResponse("auth/login.html", {
        "request": request,
        "title": "用戶登入"
    })

@frontend_router.post("/login")
async def login_submit(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """處理登入表單"""
    try:
        # 查詢用戶
        user = db.query(User).filter(
            User.username == username,
            User.is_active == True
        ).first()

        if not user or not verify_password(password, user.hashed_password):
            return templates.TemplateResponse("auth/login.html", {
                "request": request,
                "title": "用戶登入",
                "error": "用戶名或密碼錯誤"
            })

        # 創建access token
        access_token = create_access_token(data={"user_id": user.id})

        # 記錄登入日誌
        log_operation(
            db=db,
            user_id=user.id,
            action="LOGIN",
            table_name="user",
            record_id=user.id
        )

        # 設定cookie並重定向
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=False,  # 開發環境設為False
            samesite="lax"
        )
        return response

    except Exception as e:
        logger.error(f"登入錯誤: {e}")
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "title": "用戶登入",
            "error": "登入失敗，請稍後再試"
        })

@frontend_router.get("/logout")
async def logout():
    """登出"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie("access_token")
    return response

@frontend_router.get("/dashboard", response_class=HTMLResponse)
async def dashboard(
    request: Request,
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """儀表板"""
    try:
        # 簡單的統計數據查詢
        from app.models.payee_data import PayeeData
        from app.models.user import Department
        from datetime import datetime, timedelta

        total_payees = db.query(PayeeData).count()
        active_payees = db.query(PayeeData).filter(PayeeData.is_active == True).count()
        total_departments = db.query(Department).count()

        # 計算本月新增
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_new = db.query(PayeeData).filter(PayeeData.created_at >= current_month).count()

        # 構建統計數據
        stats = {
            "total_payees": total_payees,
            "active_payees": active_payees,
            "total_departments": total_departments,
            "monthly_new": monthly_new,
            "recent_activities": []  # 暫時為空
        }

        return templates.TemplateResponse("dashboard/index.html", {
            "request": request,
            "title": "主控台",
            "user": current_user,
            "stats": stats
        })
    except Exception as e:
        logger.error(f"儀表板錯誤: {e}")
        return templates.TemplateResponse("dashboard/index.html", {
            "request": request,
            "title": "主控台",
            "user": current_user,
            "stats": {
                "total_payees": 0,
                "active_payees": 0,
                "total_departments": 0,
                "monthly_new": 0,
                "recent_activities": []
            },
            "error": "載入數據失敗"
        })

@frontend_router.get("/payee", response_class=HTMLResponse)
async def payee_list(
    request: Request,
    page: int = 1,
    size: int = 20,
    search: str = "",
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """受款人列表"""
    try:
        from app.models.payee_data import PayeeData
        from app.utils.encryption import decrypt_id_number

        # 構建查詢
        query = db.query(PayeeData)

        # 搜尋條件
        if search:
            query = query.filter(
                PayeeData.name.contains(search) |
                PayeeData.id_number.contains(search) |
                PayeeData.phone.contains(search)
            )

        # 計算總數
        total = query.count()

        # 分頁
        offset = (page - 1) * size
        payees_raw = query.offset(offset).limit(size).all()

        # 解密身分證字號並準備顯示數據
        payees = []
        for payee in payees_raw:
            try:
                # 嘗試解密身分證字號
                decrypted_id = decrypt_id_number(payee.id_number)
            except Exception as e:
                logger.warning(f"解密身分證字號失敗 (受款人: {payee.name}): {e}")
                # 如果解密失敗，顯示錯誤訊息
                decrypted_id = "解密失敗"

            # 創建包含解密資料的字典
            payee_data = {
                'id': payee.id,
                'name': payee.name,
                'id_number': decrypted_id,  # 完整的解密後身分證字號
                'phone': payee.phone,
                'email': payee.email,
                'address': payee.address,
                'bank_name': payee.bank_name,
                'bank_code': payee.bank_code,
                'account_number': payee.account_number,
                'account_name': payee.account_name,
                'department': payee.department,
                'notes': payee.notes,
                'is_active': payee.is_active,
                'created_at': payee.created_at,
                'updated_at': payee.updated_at
            }
            payees.append(payee_data)

        # 計算總頁數
        pages = (total + size - 1) // size

        return templates.TemplateResponse("payee/list.html", {
            "request": request,
            "title": "受款人管理",
            "user": current_user,
            "payees": payees,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": pages
            },
            "search": search
        })
    except Exception as e:
        logger.error(f"受款人列表錯誤: {e}")
        return templates.TemplateResponse("payee/list.html", {
            "request": request,
            "title": "受款人管理",
            "user": current_user,
            "payees": [],
            "pagination": {"page": 1, "size": 20, "total": 0, "pages": 0},
            "search": search,
            "error": "載入受款人列表失敗"
        })

@frontend_router.get("/payee/create", response_class=HTMLResponse)
async def payee_create_page(
    request: Request,
    current_user: User = Depends(get_current_user_from_cookie)
):
    """新增受款人頁面"""
    return templates.TemplateResponse("payee/create.html", {
        "request": request,
        "title": "新增受款人",
        "user": current_user
    })

@frontend_router.post("/payee/create")
async def payee_create_submit(
    request: Request,
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """處理新增受款人表單"""
    try:
        from app.models.payee_data import PayeeData
        import uuid
        from datetime import datetime

        # 獲取表單數據
        form_data = await request.form()

        # 創建受款人記錄
        payee = PayeeData(
            id=str(uuid.uuid4()),
            name=form_data.get("name"),
            id_number=form_data.get("id_number"),
            phone=form_data.get("phone"),
            email=form_data.get("email"),
            address=form_data.get("address"),
            bank_name=form_data.get("bank_name"),
            bank_code=form_data.get("bank_code"),
            account_number=form_data.get("account_number"),
            account_name=form_data.get("account_name"),
            department=form_data.get("department"),
            notes=form_data.get("notes"),
            created_by=current_user.id,
            created_at=datetime.utcnow(),
            is_active=True
        )

        db.add(payee)
        db.commit()

        # 記錄操作日誌
        log_operation(
            db=db,
            user_id=current_user.id,
            action="CREATE",
            table_name="payee_data"
        )

        return RedirectResponse(url="/payee", status_code=302)

    except Exception as e:
        logger.error(f"新增受款人錯誤: {e}")
        db.rollback()

        # 重新獲取表單數據以顯示錯誤
        form_data = await request.form()
        return templates.TemplateResponse("payee/create.html", {
            "request": request,
            "title": "新增受款人",
            "user": current_user,
            "error": "新增受款人失敗，請稍後再試",
            "form_data": dict(form_data)
        })

@frontend_router.get("/payee/{payee_id}", response_class=HTMLResponse)
async def payee_detail(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """受款人詳情"""
    try:
        from app.models.payee_data import PayeeData

        payee = db.query(PayeeData).filter(PayeeData.id == payee_id).first()

        if not payee:
            raise HTTPException(status_code=404, detail="受款人不存在")

        return templates.TemplateResponse("payee/detail.html", {
            "request": request,
            "title": "受款人詳情",
            "user": current_user,
            "payee": payee
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"受款人詳情錯誤: {e}")
        raise HTTPException(status_code=500, detail="載入受款人詳情失敗")

@frontend_router.get("/payee/{payee_id}/edit", response_class=HTMLResponse)
async def payee_edit_page(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """編輯受款人頁面"""
    try:
        from app.models.payee_data import PayeeData

        payee = db.query(PayeeData).filter(PayeeData.id == payee_id).first()

        if not payee:
            raise HTTPException(status_code=404, detail="受款人不存在")

        return templates.TemplateResponse("payee/edit.html", {
            "request": request,
            "title": "編輯受款人",
            "user": current_user,
            "payee": payee
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"編輯受款人頁面錯誤: {e}")
        raise HTTPException(status_code=500, detail="載入編輯頁面失敗")

@frontend_router.post("/payee/{payee_id}/edit")
async def payee_edit_submit(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user_from_cookie),
    db: Session = Depends(get_db)
):
    """處理編輯受款人表單"""
    try:
        from app.models.payee_data import PayeeData
        from datetime import datetime

        # 獲取表單數據
        form_data = await request.form()

        # 查詢受款人
        payee = db.query(PayeeData).filter(PayeeData.id == payee_id).first()

        if not payee:
            raise HTTPException(status_code=404, detail="受款人不存在")

        # 更新受款人數據
        payee.name = form_data.get("name")
        payee.id_number = form_data.get("id_number")
        payee.phone = form_data.get("phone")
        payee.email = form_data.get("email")
        payee.address = form_data.get("address")
        payee.bank_name = form_data.get("bank_name")
        payee.bank_code = form_data.get("bank_code")
        payee.account_number = form_data.get("account_number")
        payee.account_name = form_data.get("account_name")
        payee.department = form_data.get("department")
        payee.notes = form_data.get("notes")
        payee.updated_by = current_user.id
        payee.updated_at = datetime.utcnow()

        db.commit()

        # 記錄操作日誌
        log_operation(
            db=db,
            user_id=current_user.id,
            action="UPDATE",
            table_name="payee_data"
        )

        return RedirectResponse(url=f"/payee/{payee_id}", status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"編輯受款人錯誤: {e}")
        db.rollback()

        # 重新獲取受款人數據以顯示表單
        payee = db.query(PayeeData).filter(PayeeData.id == payee_id).first()
        return templates.TemplateResponse("payee/edit.html", {
            "request": request,
            "title": "編輯受款人",
            "user": current_user,
            "payee": payee or {},
            "error": "更新受款人失敗，請稍後再試"
        })

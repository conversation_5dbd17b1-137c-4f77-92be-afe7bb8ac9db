"""
FastAPI前端路由
"""
from fastapi import APIRouter, Request, Depends, Form, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import logging

from app.models.database import get_db
from app.services.auth_service import AuthService
from app.services.payee_service import PayeeService
from app.core.security import get_current_user_optional, get_current_user
from app.models.user import User

logger = logging.getLogger(__name__)

# 創建路由器
frontend_router = APIRouter()

# 設定模板
templates = Jinja2Templates(directory="app/frontend/templates")

@frontend_router.get("/", response_class=HTMLResponse)
async def home(request: Request, current_user: User = Depends(get_current_user_optional)):
    """首頁"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)
    
    # 如果已登入，重定向到儀表板
    return RedirectResponse(url="/dashboard", status_code=302)

@frontend_router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request, current_user: User = Depends(get_current_user_optional)):
    """登入頁面"""
    if current_user:
        return RedirectResponse(url="/dashboard", status_code=302)
    
    return templates.TemplateResponse("auth/login.html", {
        "request": request,
        "title": "用戶登入"
    })

@frontend_router.post("/login")
async def login_submit(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """處理登入表單"""
    try:
        auth_service = AuthService(db)
        result = await auth_service.authenticate_user(username, password)
        
        if result["success"]:
            # 設定session或cookie
            response = RedirectResponse(url="/dashboard", status_code=302)
            response.set_cookie(
                key="access_token",
                value=result["data"]["access_token"],
                httponly=True,
                secure=False,  # 開發環境設為False
                samesite="lax"
            )
            return response
        else:
            return templates.TemplateResponse("auth/login.html", {
                "request": request,
                "title": "用戶登入",
                "error": result["message"]
            })
    except Exception as e:
        logger.error(f"登入錯誤: {e}")
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "title": "用戶登入",
            "error": "登入失敗，請稍後再試"
        })

@frontend_router.get("/logout")
async def logout():
    """登出"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie("access_token")
    return response

@frontend_router.get("/dashboard", response_class=HTMLResponse)
async def dashboard(
    request: Request, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """儀表板"""
    try:
        payee_service = PayeeService(db)
        
        # 獲取統計數據
        stats = await payee_service.get_statistics()
        
        return templates.TemplateResponse("dashboard/index.html", {
            "request": request,
            "title": "主控台",
            "user": current_user,
            "stats": stats
        })
    except Exception as e:
        logger.error(f"儀表板錯誤: {e}")
        return templates.TemplateResponse("dashboard/index.html", {
            "request": request,
            "title": "主控台",
            "user": current_user,
            "stats": {},
            "error": "載入數據失敗"
        })

@frontend_router.get("/payee", response_class=HTMLResponse)
async def payee_list(
    request: Request,
    page: int = 1,
    size: int = 20,
    search: str = "",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """受款人列表"""
    try:
        payee_service = PayeeService(db)
        
        # 構建查詢參數
        query_params = {
            "page": page,
            "size": size,
            "search": search if search else None
        }
        
        # 獲取受款人列表
        result = await payee_service.get_payees(query_params)
        
        return templates.TemplateResponse("payee/list.html", {
            "request": request,
            "title": "受款人管理",
            "user": current_user,
            "payees": result["data"]["data"],
            "pagination": {
                "page": result["data"]["page"],
                "size": result["data"]["size"],
                "total": result["data"]["total"],
                "pages": result["data"]["pages"]
            },
            "search": search
        })
    except Exception as e:
        logger.error(f"受款人列表錯誤: {e}")
        return templates.TemplateResponse("payee/list.html", {
            "request": request,
            "title": "受款人管理",
            "user": current_user,
            "payees": [],
            "pagination": {"page": 1, "size": 20, "total": 0, "pages": 0},
            "search": search,
            "error": "載入受款人列表失敗"
        })

@frontend_router.get("/payee/create", response_class=HTMLResponse)
async def payee_create_page(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """新增受款人頁面"""
    return templates.TemplateResponse("payee/create.html", {
        "request": request,
        "title": "新增受款人",
        "user": current_user
    })

@frontend_router.post("/payee/create")
async def payee_create_submit(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """處理新增受款人表單"""
    try:
        # 獲取表單數據
        form_data = await request.form()
        
        payee_service = PayeeService(db)
        
        # 構建受款人數據
        payee_data = {
            "name": form_data.get("name"),
            "id_number": form_data.get("id_number"),
            "phone": form_data.get("phone"),
            "email": form_data.get("email"),
            "address": form_data.get("address"),
            "bank_name": form_data.get("bank_name"),
            "bank_code": form_data.get("bank_code"),
            "account_number": form_data.get("account_number"),
            "account_name": form_data.get("account_name"),
            "department": form_data.get("department"),
            "notes": form_data.get("notes")
        }
        
        # 創建受款人
        result = await payee_service.create_payee(payee_data, current_user.id)
        
        if result["success"]:
            return RedirectResponse(url="/payee", status_code=302)
        else:
            return templates.TemplateResponse("payee/create.html", {
                "request": request,
                "title": "新增受款人",
                "user": current_user,
                "error": result["message"],
                "form_data": payee_data
            })
    except Exception as e:
        logger.error(f"新增受款人錯誤: {e}")
        return templates.TemplateResponse("payee/create.html", {
            "request": request,
            "title": "新增受款人",
            "user": current_user,
            "error": "新增受款人失敗，請稍後再試"
        })

@frontend_router.get("/payee/{payee_id}", response_class=HTMLResponse)
async def payee_detail(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """受款人詳情"""
    try:
        payee_service = PayeeService(db)
        result = await payee_service.get_payee(payee_id)
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail="受款人不存在")
        
        return templates.TemplateResponse("payee/detail.html", {
            "request": request,
            "title": "受款人詳情",
            "user": current_user,
            "payee": result["data"]
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"受款人詳情錯誤: {e}")
        raise HTTPException(status_code=500, detail="載入受款人詳情失敗")

@frontend_router.get("/payee/{payee_id}/edit", response_class=HTMLResponse)
async def payee_edit_page(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """編輯受款人頁面"""
    try:
        payee_service = PayeeService(db)
        result = await payee_service.get_payee(payee_id)
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail="受款人不存在")
        
        return templates.TemplateResponse("payee/edit.html", {
            "request": request,
            "title": "編輯受款人",
            "user": current_user,
            "payee": result["data"]
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"編輯受款人頁面錯誤: {e}")
        raise HTTPException(status_code=500, detail="載入編輯頁面失敗")

@frontend_router.post("/payee/{payee_id}/edit")
async def payee_edit_submit(
    request: Request,
    payee_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """處理編輯受款人表單"""
    try:
        # 獲取表單數據
        form_data = await request.form()
        
        payee_service = PayeeService(db)
        
        # 構建更新數據
        update_data = {
            "name": form_data.get("name"),
            "id_number": form_data.get("id_number"),
            "phone": form_data.get("phone"),
            "email": form_data.get("email"),
            "address": form_data.get("address"),
            "bank_name": form_data.get("bank_name"),
            "bank_code": form_data.get("bank_code"),
            "account_number": form_data.get("account_number"),
            "account_name": form_data.get("account_name"),
            "department": form_data.get("department"),
            "notes": form_data.get("notes")
        }
        
        # 更新受款人
        result = await payee_service.update_payee(payee_id, update_data, current_user.id)
        
        if result["success"]:
            return RedirectResponse(url=f"/payee/{payee_id}", status_code=302)
        else:
            # 重新獲取受款人數據以顯示表單
            payee_result = await payee_service.get_payee(payee_id)
            return templates.TemplateResponse("payee/edit.html", {
                "request": request,
                "title": "編輯受款人",
                "user": current_user,
                "payee": payee_result["data"] if payee_result["success"] else {},
                "error": result["message"]
            })
    except Exception as e:
        logger.error(f"編輯受款人錯誤: {e}")
        return templates.TemplateResponse("payee/edit.html", {
            "request": request,
            "title": "編輯受款人",
            "user": current_user,
            "payee": {},
            "error": "更新受款人失敗，請稍後再試"
        })

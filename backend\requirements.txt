# CBA 受款人資料搜集系統 - 完整套件清單
# 支援 FastAPI + JWT + Streamlit + SQLite 架構

# ==========================================
# 核心 Web 框架
# ==========================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
streamlit==1.28.2

# ==========================================
# 資料庫相關
# ==========================================
sqlalchemy==2.0.23
alembic==1.13.1

# ==========================================
# 認證與安全
# ==========================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.3.0
cryptography>=41.0.0

# ==========================================
# 資料驗證與設定管理
# ==========================================
pydantic==2.5.0
pydantic[email]==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# ==========================================
# HTTP 客戶端與網路請求
# ==========================================
httpx==0.25.2
requests==2.32.4

# ==========================================
# 資料處理與分析
# ==========================================
pandas==2.1.3
numpy==1.25.2
python-dateutil==2.8.2
pytz==2023.3

# ==========================================
# Streamlit 前端增強套件
# ==========================================
streamlit-authenticator==0.2.3
streamlit-extras==0.3.5
streamlit-option-menu==0.3.6

# ==========================================
# SOAP 客戶端 (SSO 整合)
# ==========================================
zeep==4.3.1
spyne==2.14.0
lxml==6.0.0

# ==========================================
# Flask 相關 (SSO 服務)
# ==========================================
flask==3.1.1
werkzeug==3.1.3

# ==========================================
# 環境與配置管理
# ==========================================
python-dotenv==1.1.1

# ==========================================
# 測試框架
# ==========================================
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-html==4.1.1
pytest-xdist==3.5.0
requests-mock==1.11.0

# ==========================================
# 開發工具
# ==========================================
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# ==========================================
# 系統監控與工具
# ==========================================
psutil==7.0.0

# ==========================================
# 其他實用工具
# ==========================================
jinja2==3.1.2
aiofiles==23.2.1
python-jwt==4.1.0

# ==========================================
# 部署相關 (可選)
# ==========================================
supervisor==4.2.5
gunicorn==21.2.0

# ==========================================
# 文檔生成 (可選)
# ==========================================
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# ==========================================
# 日誌處理
# ==========================================
loguru==0.7.2

# ==========================================
# 資料序列化
# ==========================================
orjson==3.9.10

# ==========================================
# 時區處理增強
# ==========================================
tzdata==2023.3

# ==========================================
# 檔案處理
# ==========================================
openpyxl==3.1.2
xlsxwriter==3.1.9

# ==========================================
# 快取 (可選)
# ==========================================
redis==5.0.1

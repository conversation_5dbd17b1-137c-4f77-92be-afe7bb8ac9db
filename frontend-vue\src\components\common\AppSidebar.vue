<template>
  <div class="app-sidebar">
    <!-- Logo區域 -->
    <div class="sidebar-logo">
      <router-link to="/" class="logo-link">
        <img src="/logo.png" alt="Logo" class="logo-img" />
        <span v-show="!collapsed" class="logo-text">CBA系統</span>
      </router-link>
    </div>
    
    <!-- 導航菜單 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <template v-for="item in menuItems" :key="item.path">
        <!-- 有子菜單的項目 -->
        <el-sub-menu 
          v-if="item.children && item.children.length > 0" 
          :index="item.path"
        >
          <template #title>
            <el-icon><component :is="item.icon" /></el-icon>
            <span>{{ item.title }}</span>
          </template>
          
          <el-menu-item
            v-for="child in item.children"
            :key="child.path"
            :index="child.path"
          >
            <el-icon v-if="child.icon"><component :is="child.icon" /></el-icon>
            <span>{{ child.title }}</span>
          </el-menu-item>
        </el-sub-menu>
        
        <!-- 單一菜單項目 -->
        <el-menu-item v-else :index="item.path">
          <el-icon><component :is="item.icon" /></el-icon>
          <span>{{ item.title }}</span>
        </el-menu-item>
      </template>
    </el-menu>
    
    <!-- 底部信息 -->
    <div v-show="!collapsed" class="sidebar-footer">
      <div class="user-role">
        <el-tag :type="roleTagType" size="small">
          {{ roleText }}
        </el-tag>
      </div>
      <div class="version-info">
        v{{ appVersion }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  House,
  User,
  OfficeBuilding,
  DataAnalysis,
  Document,
  Download,
  TrendCharts
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { filterRoutes } from '@/router/guards'

interface Props {
  collapsed: boolean
}

defineProps<Props>()

const route = useRoute()
const authStore = useAuthStore()

// 應用版本
const appVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'

// 當前激活的菜單
const activeMenu = computed(() => {
  const { path } = route
  return path
})

// 用戶角色標籤
const roleTagType = computed(() => {
  const role = authStore.userRole
  switch (role) {
    case 'admin':
      return 'danger'
    case 'global':
      return 'warning'
    default:
      return 'info'
  }
})

const roleText = computed(() => {
  const role = authStore.userRole
  switch (role) {
    case 'admin':
      return '管理員'
    case 'global':
      return '全域用戶'
    default:
      return '一般用戶'
  }
})

// 基礎菜單配置
const baseMenuItems = [
  {
    path: '/',
    title: '主控台',
    icon: House
  },
  {
    path: '/payee',
    title: '受款人管理',
    icon: User,
    children: [
      {
        path: '/payee',
        title: '受款人列表',
        icon: User
      },
      {
        path: '/payee/create',
        title: '新增受款人',
        icon: User,
        permission: 'payee:create'
      }
    ]
  },
  {
    path: '/department',
    title: '部門管理',
    icon: OfficeBuilding,
    permission: 'department:read'
  },
  {
    path: '/reports',
    title: '報表統計',
    icon: DataAnalysis,
    children: [
      {
        path: '/reports/statistics',
        title: '統計報表',
        icon: TrendCharts
      },
      {
        path: '/reports/export',
        title: '資料匯出',
        icon: Download
      }
    ]
  },
  {
    path: '/audit',
    title: '審計日誌',
    icon: Document,
    permission: 'audit:read'
  }
]

// 根據用戶權限過濾菜單
const menuItems = computed(() => {
  const userPermissions = authStore.permissions
  const userRole = authStore.userRole || ''
  
  return filterRoutes(baseMenuItems, userPermissions, userRole)
})
</script>

<style lang="scss" scoped>
.app-sidebar {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #001529;
  
  .sidebar-logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #1f2937;
    
    .logo-link {
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      color: #fff;
      
      .logo-img {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
      
      .logo-text {
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
      }
    }
  }
  
  .sidebar-menu {
    flex: 1;
    border: none;
    background: transparent;
    
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      color: rgba(255, 255, 255, 0.8);
      
      &:hover {
        background-color: #1f2937 !important;
        color: #fff !important;
      }
      
      &.is-active {
        background-color: #1890ff !important;
        color: #fff !important;
      }
    }
    
    :deep(.el-sub-menu .el-menu-item) {
      background-color: #0c1419 !important;
      
      &:hover {
        background-color: #1f2937 !important;
      }
      
      &.is-active {
        background-color: #1890ff !important;
      }
    }
    
    :deep(.el-menu-item .el-icon),
    :deep(.el-sub-menu__title .el-icon) {
      color: inherit;
    }
  }
  
  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid #1f2937;
    
    .user-role {
      margin-bottom: 8px;
      text-align: center;
    }
    
    .version-info {
      text-align: center;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

// 收縮狀態樣式
.app-sidebar:has(.sidebar-menu.el-menu--collapse) {
  .sidebar-logo .logo-text {
    display: none;
  }
}
</style>

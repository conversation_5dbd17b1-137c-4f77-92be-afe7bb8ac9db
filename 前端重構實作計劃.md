# CBA受款人資料搜集系統 - 前端重構實作計劃

## 📋 重構概述

將現有的Streamlit前端完全重構為基於Vue.js 3的現代化SPA應用，保持所有業務功能不變。

## 🛠️ 技術棧選型

### 前端技術棧
- **框架**: Vue.js 3 (Composition API)
- **路由**: Vue Router 4
- **狀態管理**: Pinia
- **UI組件庫**: Element Plus
- **HTTP客戶端**: Axios
- **建構工具**: Vite
- **CSS預處理器**: SCSS
- **圖表庫**: ECharts (vue-echarts)
- **表格組件**: Element Plus Table (支援虛擬滾動)
- **表單驗證**: VeeValidate + Yup
- **日期處理**: Day.js
- **檔案上傳**: Element Plus Upload

### 開發工具
- **TypeScript**: 提供型別安全
- **ESLint + Prettier**: 程式碼品質和格式化
- **Husky**: Git hooks
- **Commitizen**: 規範化提交訊息

## 📁 新前端目錄結構

```
frontend-vue/
├── public/
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/                    # API請求模組
│   │   ├── auth.ts
│   │   ├── payee.ts
│   │   ├── department.ts
│   │   ├── audit.ts
│   │   └── index.ts
│   ├── assets/                 # 靜態資源
│   │   ├── images/
│   │   ├── icons/
│   │   └── styles/
│   │       ├── main.scss
│   │       ├── variables.scss
│   │       └── components.scss
│   ├── components/             # 通用組件
│   │   ├── common/
│   │   │   ├── AppHeader.vue
│   │   │   ├── AppSidebar.vue
│   │   │   ├── AppFooter.vue
│   │   │   ├── LoadingSpinner.vue
│   │   │   └── ConfirmDialog.vue
│   │   ├── forms/
│   │   │   ├── PayeeForm.vue
│   │   │   ├── DepartmentForm.vue
│   │   │   └── SearchForm.vue
│   │   ├── tables/
│   │   │   ├── PayeeTable.vue
│   │   │   ├── AuditTable.vue
│   │   │   └── DataTable.vue
│   │   └── charts/
│   │       ├── StatisticsChart.vue
│   │       └── ReportChart.vue
│   ├── composables/            # 組合式函數
│   │   ├── useAuth.ts
│   │   ├── usePermissions.ts
│   │   ├── useApi.ts
│   │   ├── useTable.ts
│   │   └── useExport.ts
│   ├── layouts/                # 佈局組件
│   │   ├── DefaultLayout.vue
│   │   ├── AuthLayout.vue
│   │   └── EmptyLayout.vue
│   ├── pages/                  # 頁面組件
│   │   ├── auth/
│   │   │   ├── Login.vue
│   │   │   └── SSOCallback.vue
│   │   ├── dashboard/
│   │   │   └── Dashboard.vue
│   │   ├── payee/
│   │   │   ├── PayeeList.vue
│   │   │   ├── PayeeDetail.vue
│   │   │   ├── PayeeCreate.vue
│   │   │   └── PayeeEdit.vue
│   │   ├── department/
│   │   │   ├── DepartmentList.vue
│   │   │   └── DepartmentManage.vue
│   │   ├── reports/
│   │   │   ├── Statistics.vue
│   │   │   └── Export.vue
│   │   ├── audit/
│   │   │   └── AuditLog.vue
│   │   └── profile/
│   │       └── UserProfile.vue
│   ├── router/                 # 路由配置
│   │   ├── index.ts
│   │   ├── guards.ts
│   │   └── routes.ts
│   ├── stores/                 # Pinia狀態管理
│   │   ├── auth.ts
│   │   ├── payee.ts
│   │   ├── department.ts
│   │   ├── audit.ts
│   │   └── app.ts
│   ├── types/                  # TypeScript型別定義
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   ├── payee.ts
│   │   └── common.ts
│   ├── utils/                  # 工具函數
│   │   ├── request.ts
│   │   ├── auth.ts
│   │   ├── validation.ts
│   │   ├── format.ts
│   │   ├── export.ts
│   │   └── constants.ts
│   ├── App.vue                 # 根組件
│   └── main.ts                 # 應用入口
├── .env.development            # 開發環境配置
├── .env.production             # 生產環境配置
├── .gitignore
├── .eslintrc.js
├── .prettierrc
├── index.html
├── package.json
├── tsconfig.json
├── vite.config.ts
└── README.md
```

## 🔄 功能對應表

| Streamlit功能 | Vue.js對應頁面/組件 | 說明 |
|--------------|-------------------|------|
| 登入頁面 | `/auth/Login.vue` | SSO + 開發登入 |
| 主控台 | `/dashboard/Dashboard.vue` | 統計概覽 |
| 受款人管理 | `/payee/PayeeList.vue` | 列表、搜尋、CRUD |
| 受款人詳情 | `/payee/PayeeDetail.vue` | 詳細資訊檢視 |
| 新增受款人 | `/payee/PayeeCreate.vue` | 新增表單 |
| 編輯受款人 | `/payee/PayeeEdit.vue` | 編輯表單 |
| 部門管理 | `/department/DepartmentList.vue` | 部門列表管理 |
| 統計報表 | `/reports/Statistics.vue` | 圖表統計 |
| 資料匯出 | `/reports/Export.vue` | 匯出功能 |
| 審計日誌 | `/audit/AuditLog.vue` | 日誌查看 |
| 用戶資料 | `/profile/UserProfile.vue` | 個人資料 |

## 🎯 核心組件設計

### 1. 認證系統 (useAuth)
```typescript
// composables/useAuth.ts
export const useAuth = () => {
  const authStore = useAuthStore()
  
  const login = async (credentials: LoginCredentials) => {
    // 登入邏輯
  }
  
  const logout = () => {
    // 登出邏輯
  }
  
  const checkPermission = (permission: string) => {
    // 權限檢查
  }
  
  return {
    user: computed(() => authStore.user),
    isAuthenticated: computed(() => authStore.isAuthenticated),
    permissions: computed(() => authStore.permissions),
    login,
    logout,
    checkPermission
  }
}
```

### 2. API請求封裝
```typescript
// utils/request.ts
import axios from 'axios'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000
})

// 請求攔截器
request.interceptors.request.use(config => {
  const token = localStorage.getItem('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 回應攔截器
request.interceptors.response.use(
  response => response.data,
  error => {
    // 錯誤處理
    return Promise.reject(error)
  }
)
```

### 3. 狀態管理 (Pinia)
```typescript
// stores/payee.ts
export const usePayeeStore = defineStore('payee', () => {
  const payees = ref<Payee[]>([])
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    size: 20,
    total: 0
  })
  
  const fetchPayees = async (params?: PayeeQueryParams) => {
    loading.value = true
    try {
      const response = await payeeApi.getPayees(params)
      payees.value = response.data
      pagination.value = response.pagination
    } finally {
      loading.value = false
    }
  }
  
  return {
    payees: readonly(payees),
    loading: readonly(loading),
    pagination: readonly(pagination),
    fetchPayees
  }
})
```

## 🛣️ 路由規劃

```typescript
// router/routes.ts
export const routes: RouteRecordRaw[] = [
  {
    path: '/auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/pages/auth/Login.vue')
      },
      {
        path: 'sso-callback',
        name: 'SSOCallback',
        component: () => import('@/pages/auth/SSOCallback.vue')
      }
    ]
  },
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/pages/dashboard/Dashboard.vue')
      },
      {
        path: 'payee',
        children: [
          {
            path: '',
            name: 'PayeeList',
            component: () => import('@/pages/payee/PayeeList.vue')
          },
          {
            path: 'create',
            name: 'PayeeCreate',
            component: () => import('@/pages/payee/PayeeCreate.vue'),
            meta: { permission: 'payee:create' }
          },
          {
            path: ':id',
            name: 'PayeeDetail',
            component: () => import('@/pages/payee/PayeeDetail.vue')
          },
          {
            path: ':id/edit',
            name: 'PayeeEdit',
            component: () => import('@/pages/payee/PayeeEdit.vue'),
            meta: { permission: 'payee:update' }
          }
        ]
      },
      {
        path: 'department',
        name: 'Department',
        component: () => import('@/pages/department/DepartmentList.vue'),
        meta: { permission: 'department:read' }
      },
      {
        path: 'reports',
        children: [
          {
            path: 'statistics',
            name: 'Statistics',
            component: () => import('@/pages/reports/Statistics.vue')
          },
          {
            path: 'export',
            name: 'Export',
            component: () => import('@/pages/reports/Export.vue')
          }
        ]
      },
      {
        path: 'audit',
        name: 'AuditLog',
        component: () => import('@/pages/audit/AuditLog.vue'),
        meta: { permission: 'audit:read' }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/pages/profile/UserProfile.vue')
      }
    ]
  }
]
```

## 🔐 權限控制系統

```typescript
// router/guards.ts
export const setupRouterGuards = (router: Router) => {
  router.beforeEach(async (to, from, next) => {
    const { isAuthenticated, checkPermission } = useAuth()
    
    // 檢查是否需要認證
    if (to.meta.requiresAuth && !isAuthenticated.value) {
      return next('/auth/login')
    }
    
    // 檢查權限
    if (to.meta.permission && !checkPermission(to.meta.permission)) {
      return next('/403')
    }
    
    next()
  })
}
```

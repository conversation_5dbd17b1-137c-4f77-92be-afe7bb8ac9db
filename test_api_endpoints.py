#!/usr/bin/env python3
"""
測試API端點可用性
"""
import requests
import json
import sys
import os

# 後端API基礎URL
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api/v1"

def test_health_check():
    """測試健康檢查API"""
    print("=== 測試健康檢查API ===")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"狀態碼: {response.status_code}")
        print(f"回應: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"錯誤: {e}")
        return False

def test_auth_endpoints():
    """測試認證相關API端點"""
    print("\n=== 測試認證API端點 ===")
    
    # 測試登入端點
    login_url = f"{API_URL}/auth/login-json"
    print(f"登入端點: {login_url}")
    
    try:
        response = requests.post(
            login_url,
            json={"username": "test", "password": "test"},
            timeout=5
        )
        print(f"登入API狀態碼: {response.status_code}")
        print(f"登入API回應: {response.text}")
    except Exception as e:
        print(f"登入API錯誤: {e}")

def test_personal_data_endpoints():
    """測試個人資料API端點"""
    print("\n=== 測試個人資料API端點 ===")
    
    # 測試GET /personal-data/
    list_url = f"{API_URL}/personal-data/"
    print(f"列表端點: {list_url}")
    
    try:
        response = requests.get(list_url, timeout=5)
        print(f"列表API狀態碼: {response.status_code}")
        print(f"列表API回應: {response.text}")
    except Exception as e:
        print(f"列表API錯誤: {e}")
    
    # 測試POST /personal-data/
    create_url = f"{API_URL}/personal-data/"
    print(f"創建端點: {create_url}")
    
    try:
        response = requests.post(
            create_url,
            json={"name": "測試", "id_number": "A123456789"},
            timeout=5
        )
        print(f"創建API狀態碼: {response.status_code}")
        print(f"創建API回應: {response.text}")
    except Exception as e:
        print(f"創建API錯誤: {e}")

def test_openapi_docs():
    """測試OpenAPI文檔端點"""
    print("\n=== 測試OpenAPI文檔端點 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        print(f"Swagger UI狀態碼: {response.status_code}")
        
        response = requests.get(f"{BASE_URL}/openapi.json", timeout=5)
        print(f"OpenAPI JSON狀態碼: {response.status_code}")
        if response.status_code == 200:
            openapi_data = response.json()
            print(f"API路徑數量: {len(openapi_data.get('paths', {}))}")
            print("可用的API路徑:")
            for path in openapi_data.get('paths', {}).keys():
                print(f"  - {path}")
        
    except Exception as e:
        print(f"OpenAPI文檔錯誤: {e}")

def main():
    """主函數"""
    print("開始測試API端點可用性...")
    
    # 測試健康檢查
    if not test_health_check():
        print("❌ 健康檢查失敗，後端服務可能未啟動")
        return False
    
    print("✅ 健康檢查通過")
    
    # 測試各個端點
    test_auth_endpoints()
    test_personal_data_endpoints()
    test_openapi_docs()
    
    print("\n測試完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
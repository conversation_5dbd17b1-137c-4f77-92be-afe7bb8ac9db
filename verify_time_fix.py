#!/usr/bin/env python3
"""
驗證時間修復
"""

import pytz
from datetime import datetime

def verify_time_conversion():
    """驗證時間轉換邏輯"""
    print("🕒 驗證時間轉換邏輯...")
    
    # 模擬資料庫中的UTC時間
    db_time = "2025-07-14 07:46:27"
    expected_taipei_time = "2025-07-14 15:46:27"
    
    taipei_tz = pytz.timezone('Asia/Taipei')
    
    try:
        # 模擬前端轉換邏輯
        timestamp_str = db_time
        if 'T' in timestamp_str:
            # ISO格式時間
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
        else:
            # 假設資料庫時間是UTC時間（無時區資訊）
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
        
        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"✅ 資料庫時間（UTC）: {db_time}")
        print(f"✅ 轉換後台北時間: {formatted_time}")
        print(f"✅ 期望台北時間: {expected_taipei_time}")
        
        if formatted_time == expected_taipei_time:
            print("🎉 時間轉換正確！")
            return True
        else:
            print("❌ 時間轉換錯誤！")
            return False
            
    except Exception as e:
        print(f"❌ 轉換失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🧪 驗證審計日誌時間修復...\n")
    
    success = verify_time_conversion()
    
    if success:
        print("\n✅ 時間轉換修復驗證成功！")
        print("💡 現在前端應該能正確顯示台北時區時間了。")
    else:
        print("\n❌ 時間轉換修復驗證失敗！")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

"""
pytest配置檔案 - 測試fixtures和全域設定
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch
from typing import Dict, Any

# 將專案根目錄加入Python路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture
def mock_streamlit():
    """模擬Streamlit模組"""
    with patch('streamlit.session_state') as mock_state:
        # 設定session_state模擬
        mock_state.logged_in = False
        mock_state.user_info = None
        mock_state.access_token = None
        mock_state.current_page = "登入"
        yield mock_state

@pytest.fixture
def sample_user_general():
    """一般用戶測試資料"""
    return {
        "user_id": 1,
        "username": "general_user",
        "full_name": "張三",
        "department_name": "業務部",
        "roles": ["general_user"],
        "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA"]
    }

@pytest.fixture
def sample_user_global():
    """全域用戶測試資料"""
    return {
        "user_id": 2,
        "username": "global_user",
        "full_name": "李四",
        "department_name": "資訊部",
        "roles": ["global_user"],
        "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA"]
    }

@pytest.fixture
def sample_user_admin():
    """管理者測試資料"""
    return {
        "user_id": 3,
        "username": "admin",
        "full_name": "王五",
        "department_name": "管理部",
        "roles": ["admin"],
        "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_USERS", "MANAGE_ROLES", "VIEW_AUDIT_LOG"]
    }

@pytest.fixture
def sample_personal_data():
    """個人資料測試資料"""
    return [
        {
            "id": 1,
            "name": "測試用戶一",
            "id_number_masked": "A123****89",
            "address": "台北市信義區信義路100號",
            "notes": "測試資料一",
            "created_by": "general_user",
            "created_at": "2024-12-19 10:30:00",
            "updated_at": "2024-12-19 10:30:00"
        },
        {
            "id": 2,
            "name": "測試用戶二",
            "id_number_masked": "B234****90",
            "address": "新北市板橋區中山路200號",
            "notes": "測試資料二",
            "created_by": "global_user",
            "created_at": "2024-12-19 11:15:00",
            "updated_at": "2024-12-19 11:15:00"
        }
    ]

@pytest.fixture
def valid_id_numbers():
    """有效的身分證字號測試資料"""
    return [
        "A123456789",
        "B234567890", 
        "C345678901",
        "D456789012",
        "E567890123"
    ]

@pytest.fixture
def invalid_id_numbers():
    """無效的身分證字號測試資料"""
    return [
        "123456789",      # 缺少英文字母
        "A12345678",      # 長度不足
        "A1234567890",    # 長度過長
        "A123456788",     # 檢核碼錯誤
        "Z123456789",     # 無效字母
        "",               # 空字串
        "ABCDEFGHIJ"      # 全英文
    ]

@pytest.fixture
def mock_api_response():
    """模擬API回應"""
    def _mock_response(status_code=200, data=None, error=None):
        mock_resp = Mock()
        mock_resp.status_code = status_code
        if data:
            mock_resp.json.return_value = data
        if error:
            mock_resp.json.return_value = {"detail": error}
        return mock_resp
    return _mock_response

@pytest.fixture(autouse=True)
def setup_test_environment():
    """設定測試環境"""
    # 設定環境變數
    os.environ["API_BASE_URL"] = "http://localhost:8000"
    os.environ["JWT_SECRET_KEY"] = "test-secret-key"
    
    yield
    
    # 清理
    if "API_BASE_URL" in os.environ:
        del os.environ["API_BASE_URL"]
    if "JWT_SECRET_KEY" in os.environ:
        del os.environ["JWT_SECRET_KEY"] 
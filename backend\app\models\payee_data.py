"""
受款人資料模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base


class PayeeData(Base):
    """受款人資料模型"""
    __tablename__ = "payee_data"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    encrypted_id_number = Column(Text, nullable=False, unique=True, index=True)  # 加密後的身分證字號
    address = Column(Text)
    notes = Column(Text)
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 建立者用戶ID
    created_by_department = Column(Integer, ForeignKey("departments.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)  # 軟刪除標記
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 關聯
    created_by_user = relationship("User", back_populates="payee_data")
    created_by_dept = relationship("Department", back_populates="payee_data")
    
    def __repr__(self):
        return f"<PayeeData(id={self.id}, name='{self.name}', created_by_user_id={self.created_by_user_id})>" 
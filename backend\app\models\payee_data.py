"""
受款人資料模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base


class PayeeData(Base):
    """受款人資料模型"""
    __tablename__ = "payee_data"

    id = Column(String(36), primary_key=True, index=True)  # 使用UUID字符串
    name = Column(String(100), nullable=False, index=True)
    id_number = Column(String(20), nullable=False, unique=True, index=True)  # 身分證字號
    phone = Column(String(20))  # 電話
    email = Column(String(100))  # 電子郵件
    address = Column(Text)  # 地址
    bank_name = Column(String(100))  # 銀行名稱
    bank_code = Column(String(10))  # 銀行代碼
    account_number = Column(String(50))  # 帳號
    account_name = Column(String(100))  # 戶名
    department = Column(String(100))  # 部門
    notes = Column(Text)  # 備註
    created_by = Column(String(36), ForeignKey("users.id"), nullable=False)  # 建立者用戶ID
    updated_by = Column(String(36), ForeignKey("users.id"))  # 更新者用戶ID
    is_active = Column(Boolean, default=True, nullable=False)  # 軟刪除標記
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 關聯
    created_by_user = relationship("User", foreign_keys=[created_by], back_populates="created_payees")
    updated_by_user = relationship("User", foreign_keys=[updated_by], back_populates="updated_payees")
    
    def __repr__(self):
        return f"<PayeeData(id={self.id}, name='{self.name}', created_by_user_id={self.created_by_user_id})>" 
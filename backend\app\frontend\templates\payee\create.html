{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">主控台</a></li>
                    <li class="breadcrumb-item"><a href="/payee">受款人管理</a></li>
                    <li class="breadcrumb-item active">新增受款人</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0">
                <i class="bi bi-person-plus"></i> 新增受款人
            </h1>
            <p class="text-muted">填寫受款人基本資料和銀行帳戶資訊</p>
        </div>
    </div>
    
    <!-- 表單 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-form"></i> 受款人資料表單
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" action="/payee/create" class="needs-validation" novalidate>
                        <!-- 基本資料 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-person"></i> 基本資料
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    <i class="bi bi-person"></i> 姓名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       required placeholder="請輸入受款人姓名"
                                       value="{{ form_data.name if form_data else '' }}">
                                <div class="invalid-feedback">請輸入受款人姓名</div>
                            </div>
                            <div class="col-md-6">
                                <label for="id_number" class="form-label">
                                    <i class="bi bi-card-text"></i> 身分證號 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="id_number" name="id_number" 
                                       required placeholder="請輸入身分證號"
                                       value="{{ form_data.id_number if form_data else '' }}">
                                <div class="invalid-feedback">請輸入正確的身分證號</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">
                                    <i class="bi bi-telephone"></i> 電話號碼
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="請輸入電話號碼"
                                       value="{{ form_data.phone if form_data else '' }}">
                                <div class="invalid-feedback">請輸入正確的電話號碼</div>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope"></i> 電子郵件
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="請輸入電子郵件"
                                       value="{{ form_data.email if form_data else '' }}">
                                <div class="invalid-feedback">請輸入正確的電子郵件格式</div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="address" class="form-label">
                                    <i class="bi bi-geo-alt"></i> 地址
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="2" 
                                          placeholder="請輸入地址">{{ form_data.address if form_data else '' }}</textarea>
                            </div>
                        </div>
                        
                        <!-- 銀行資料 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-bank"></i> 銀行帳戶資料
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="bank_name" class="form-label">
                                    <i class="bi bi-building"></i> 銀行名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                       required placeholder="請輸入銀行名稱"
                                       value="{{ form_data.bank_name if form_data else '' }}">
                                <div class="invalid-feedback">請輸入銀行名稱</div>
                            </div>
                            <div class="col-md-6">
                                <label for="bank_code" class="form-label">
                                    <i class="bi bi-hash"></i> 銀行代碼
                                </label>
                                <input type="text" class="form-control" id="bank_code" name="bank_code" 
                                       placeholder="請輸入銀行代碼"
                                       value="{{ form_data.bank_code if form_data else '' }}">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="account_number" class="form-label">
                                    <i class="bi bi-credit-card"></i> 帳戶號碼 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="account_number" name="account_number" 
                                       required placeholder="請輸入帳戶號碼"
                                       value="{{ form_data.account_number if form_data else '' }}">
                                <div class="invalid-feedback">請輸入帳戶號碼</div>
                            </div>
                            <div class="col-md-6">
                                <label for="account_name" class="form-label">
                                    <i class="bi bi-person-badge"></i> 帳戶名稱 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="account_name" name="account_name" 
                                       required placeholder="請輸入帳戶名稱"
                                       value="{{ form_data.account_name if form_data else '' }}">
                                <div class="invalid-feedback">請輸入帳戶名稱</div>
                            </div>
                        </div>
                        
                        <!-- 其他資料 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-info-circle"></i> 其他資料
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="department" class="form-label">
                                    <i class="bi bi-building"></i> 部門
                                </label>
                                <input type="text" class="form-control" id="department" name="department" 
                                       placeholder="請輸入部門"
                                       value="{{ form_data.department if form_data else '' }}">
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-chat-text"></i> 備註
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="請輸入備註資訊">{{ form_data.notes if form_data else '' }}</textarea>
                            </div>
                        </div>
                        
                        <!-- 表單按鈕 -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="/payee" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left"></i> 返回列表
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-arrow-clockwise"></i> 重設
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg"></i> 儲存受款人
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 表單驗證
(function() {
    'use strict';
    
    // 身分證號即時驗證
    document.getElementById('id_number').addEventListener('input', function() {
        var idNumber = this.value.toUpperCase();
        this.value = idNumber;
        
        if (idNumber.length === 10) {
            if (CBASystem.utils.validateIdNumber(this)) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // 電話號碼即時驗證
    document.getElementById('phone').addEventListener('input', function() {
        if (this.value.trim()) {
            if (CBASystem.utils.validatePhoneNumber(this)) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // 表單提交處理
    document.querySelector('form').addEventListener('submit', function(event) {
        var form = this;
        
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // 顯示載入狀態
            var submitBtn = form.querySelector('button[type="submit"]');
            var originalText = submitBtn.innerHTML;
            CBASystem.utils.showLoading(submitBtn, '儲存中...');
            
            // 如果需要，可以在這裡添加額外的驗證
        }
        
        form.classList.add('was-validated');
    });
})();
</script>
{% endblock %}

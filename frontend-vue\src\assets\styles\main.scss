/**
 * 主要樣式文件
 */
@import './variables.scss';

// 全域工具類
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { 
  display: flex; 
  flex-direction: column; 
}

// 間距工具類
@each $size in (xs, sm, md, lg, xl, xxl) {
  .m-#{$size} { margin: var(--spacing-#{$size}); }
  .mt-#{$size} { margin-top: var(--spacing-#{$size}); }
  .mr-#{$size} { margin-right: var(--spacing-#{$size}); }
  .mb-#{$size} { margin-bottom: var(--spacing-#{$size}); }
  .ml-#{$size} { margin-left: var(--spacing-#{$size}); }
  .mx-#{$size} { 
    margin-left: var(--spacing-#{$size}); 
    margin-right: var(--spacing-#{$size}); 
  }
  .my-#{$size} { 
    margin-top: var(--spacing-#{$size}); 
    margin-bottom: var(--spacing-#{$size}); 
  }
  
  .p-#{$size} { padding: var(--spacing-#{$size}); }
  .pt-#{$size} { padding-top: var(--spacing-#{$size}); }
  .pr-#{$size} { padding-right: var(--spacing-#{$size}); }
  .pb-#{$size} { padding-bottom: var(--spacing-#{$size}); }
  .pl-#{$size} { padding-left: var(--spacing-#{$size}); }
  .px-#{$size} { 
    padding-left: var(--spacing-#{$size}); 
    padding-right: var(--spacing-#{$size}); 
  }
  .py-#{$size} { 
    padding-top: var(--spacing-#{$size}); 
    padding-bottom: var(--spacing-#{$size}); 
  }
}

// 卡片樣式
.card {
  background: $bg-color;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  &.card-shadow {
    box-shadow: $box-shadow-light;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-lighter;
    
    .card-title {
      font-size: $font-size-large;
      font-weight: 600;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .card-body {
    color: $text-regular;
  }
}

// 頁面容器
.page-container {
  padding: $spacing-lg;
  
  .page-header {
    margin-bottom: $spacing-lg;
    
    .page-title {
      font-size: $font-size-extra-large;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 $spacing-sm 0;
    }
    
    .page-description {
      color: $text-secondary;
      margin: 0;
    }
  }
  
  .page-content {
    background: $bg-color;
    border-radius: $border-radius-base;
    box-shadow: $box-shadow-base;
    padding: $spacing-lg;
  }
}

// 表格樣式增強
.el-table {
  .table-actions {
    .el-button + .el-button {
      margin-left: $spacing-xs;
    }
  }
  
  .status-tag {
    &.active {
      background-color: #f0f9ff;
      color: #1e40af;
      border-color: #93c5fd;
    }
    
    &.inactive {
      background-color: #fef2f2;
      color: #dc2626;
      border-color: #fca5a5;
    }
  }
}

// 表單樣式增強
.form-container {
  .form-section {
    margin-bottom: $spacing-xl;
    
    .section-title {
      font-size: $font-size-medium;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: $spacing-md;
      padding-bottom: $spacing-xs;
      border-bottom: 2px solid $primary-color;
    }
  }
  
  .form-actions {
    text-align: center;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-lighter;
    
    .el-button + .el-button {
      margin-left: $spacing-md;
    }
  }
}

// 搜尋表單
.search-form {
  background: $bg-color;
  padding: $spacing-lg;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  margin-bottom: $spacing-md;
  
  .search-actions {
    text-align: right;
    
    .el-button + .el-button {
      margin-left: $spacing-sm;
    }
  }
}

// 統計卡片
.stat-card {
  background: $bg-color;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  padding: $spacing-lg;
  text-align: center;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $box-shadow-light;
    transform: translateY(-2px);
  }
  
  .stat-icon {
    font-size: 32px;
    margin-bottom: $spacing-sm;
  }
  
  .stat-value {
    font-size: $font-size-extra-large;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  .stat-label {
    color: $text-secondary;
    font-size: $font-size-small;
  }
}

// 響應式設計
@media (max-width: $sm) {
  .page-container {
    padding: $spacing-md;
  }
  
  .card {
    padding: $spacing-md;
  }
  
  .search-form {
    padding: $spacing-md;
  }
  
  .form-actions {
    .el-button {
      width: 100%;
      margin: $spacing-xs 0 !important;
    }
  }
}

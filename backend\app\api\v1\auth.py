"""
認證相關API路由
"""
import logging
from typing import Optional
from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session

from app.models.database import get_db
from app.models.user import User
from app.schemas.user import UserLogin, Token, UserResponse
from app.schemas.common import MessageResponse
from app.core.dependencies import get_current_active_user, get_client_info
from app.core.config import settings
from app.core.sso import (
    get_soap_sso_authenticator, 
    SSOError, SSOTokenInvalidError, SSOTokenExpiredError, 
    SSOServiceUnavailableError, SSONetworkError
)
from app.utils.jwt_auth import create_access_token, verify_password
from app.utils.audit import log_operation
from app.core.exceptions import AuthenticationError, CBAException


router = APIRouter(prefix="/auth", tags=["認證管理"])

# 設置日誌
logger = logging.getLogger(__name__)


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    用戶登入
    """
    try:
        # 查詢用戶
        user = db.query(User).filter(
            User.username == form_data.username,
            User.is_active == True
        ).first()
        
        if not user or not verify_password(form_data.password, user.hashed_password):
            # 記錄登入失敗
            if request:
                client_info = get_client_info(request)
                log_operation(
                    db=db,
                    user_id=None,
                    action="LOGIN_FAILED",
                    table_name="users",
                    new_values={
                        "username": form_data.username,
                        "reason": "invalid_credentials"
                    },
                    ip_address=client_info.get("ip_address"),
                    user_agent=client_info.get("user_agent")
                )
            
            raise AuthenticationError("用戶名或密碼錯誤")
        
        # 建立Token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "user_id": user.id,
                "username": user.username,
                "roles": [role.name for role in user.roles]
            },
            expires_delta=access_token_expires
        )
        
        # 記錄登入成功
        if request:
            client_info = get_client_info(request)
            log_operation(
                db=db,
                user_id=user.id,
                action="LOGIN_SUCCESS",
                table_name="users",
                new_values={
                    "username": user.username,
                    "login_time": "now"
                },
                ip_address=client_info.get("ip_address"),
                user_agent=client_info.get("user_agent")
            )
        
        # 建立用戶回應資訊
        user_info = UserResponse(
            id=user.id,
            username=user.username,
            full_name=user.full_name,
            department=user.department.name if user.department else None,
            is_active=user.is_active,
            created_at=user.created_at,
            roles=[role.name for role in user.roles]
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds()),
            user_info=user_info
        )
        
    except AuthenticationError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶名或密碼錯誤",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/login-json", response_model=Token)
async def login_json(
    login_data: UserLogin,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    用戶登入（JSON格式）
    """
    try:
        # 查詢用戶
        user = db.query(User).filter(
            User.username == login_data.username,
            User.is_active == True
        ).first()
        
        if not user or not verify_password(login_data.password, user.hashed_password):
            # 記錄登入失敗
            if request:
                client_info = get_client_info(request)
                log_operation(
                    db=db,
                    user_id=None,
                    action="LOGIN_FAILED",
                    table_name="users",
                    new_values={
                        "username": login_data.username,
                        "reason": "invalid_credentials"
                    },
                    ip_address=client_info.get("ip_address"),
                    user_agent=client_info.get("user_agent")
                )
            
            raise AuthenticationError("用戶名或密碼錯誤")
        
        # 建立Token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "user_id": user.id,
                "username": user.username,
                "roles": [role.name for role in user.roles]
            },
            expires_delta=access_token_expires
        )
        
        # 記錄登入成功
        if request:
            client_info = get_client_info(request)
            log_operation(
                db=db,
                user_id=user.id,
                action="LOGIN_SUCCESS",
                table_name="users",
                new_values={
                    "username": user.username,
                    "login_time": "now"
                },
                ip_address=client_info.get("ip_address"),
                user_agent=client_info.get("user_agent")
            )
        
        # 建立用戶回應資訊
        user_info = UserResponse(
            id=user.id,
            username=user.username,
            full_name=user.full_name,
            department=user.department.name if user.department else None,
            is_active=user.is_active,
            created_at=user.created_at,
            roles=[role.name for role in user.roles]
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds()),
            user_info=user_info
        )
        
    except AuthenticationError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用戶名或密碼錯誤",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# OAuth2 SSO端點已移除 - 只保留SOAP SSO功能


@router.get("/sso_login")
async def soap_sso_login(
    request: Request,
    ssoToken1: str = Query(None, description="政府SSO Token (ssoToken1格式)"),
    SAMLart: str = Query(None, description="政府SSO Token (SAMLart格式)"),
    db: Session = Depends(get_db)
):
    """
    政府SOAP SSO登入端點
    處理來自政府SSO系統的登入請求
    支援兩種Token格式：ssoToken1 和 SAMLart
    
    Args:
        ssoToken1: 政府SSO系統提供的Token (ssoToken1格式)
        SAMLart: 政府SSO系統提供的Token (SAMLart格式)
        
    Returns:
        重導向到前端應用，設置JWT Token Cookie
    """
    #print(f"ssoToken1: {ssoToken1}")
    #print(f"SAMLart: {SAMLart}")
    #print(f"request: {request}")
    #print(f"URL: {request.headers.get('referer')}")
    #print(f"URL!!!!!: {settings.sso_soap_ws_url} : {request.headers.get('referer')}SS/SS0/CommonWebService.asmx?WSDL")
    settings.sso_soap_ws_url = f"{request.headers.get('referer')}SS/SS0/CommonWebService.asmx?WSDL"
    try:
        # 檢查SSO Token（支援兩種格式）
        artifact = SAMLart or ssoToken1  # 優先使用SAMLart，兼容ssoToken1
        if not artifact:
            logger.warning("SSO登入失敗：缺少SSO Token參數 (ssoToken1 或 SAMLart)")
            return _create_error_redirect("缺少SSO認證參數，請重新登入")

        token_type = "SAMLart" if SAMLart else "ssoToken1"
        logger.info(f"收到政府SSO登入請求，Token類型: {token_type}，長度: {len(artifact)}")

        # 取得SOAP SSO認證器
        soap_sso_authenticator = get_soap_sso_authenticator()
        if not soap_sso_authenticator:
            logger.error("SOAP SSO服務未配置")
            return _create_error_redirect("SSO服務未配置，請聯繫系統管理員")

        # 使用SOAP SSO進行認證
        #print("進行認證")
        #print(f"🔧 SOAP SSO 認證器配置: {soap_sso_authenticator.is_configured}")
        #print(f"📍 SOAP URL: {soap_sso_authenticator.soap_ws_url}")
        
        try:
            auth_result = await soap_sso_authenticator.authenticate_with_token(artifact, db)
            print(f"✅ 認證成功: {auth_result}")
        except Exception as e:
            print(f"❌ 認證失敗: {str(e)}")
            print(f"🔍 錯誤類型: {type(e).__name__}")
            import traceback
            print(f"🔍 詳細錯誤: {traceback.format_exc()}")
            raise
        # 記錄登入成功
        if request:
            client_info = get_client_info(request)
            log_operation(
                db=db,
                user_id=auth_result["user_info"]["id"],
                action="SOAP_SSO_LOGIN_SUCCESS",
                table_name="users",
                new_values={
                    "username": auth_result["user_info"]["username"],
                    "login_method": "SOAP_SSO",
                    "sso_token_hash": hash(artifact[:10]),  # 記錄token雜湊而非原值
                    "sso_token_type": token_type
                },
                ip_address=client_info.get("ip_address"),
                user_agent=client_info.get("user_agent")
            )

        # 建立成功回應並設置JWT Cookie
        # 重導向到前端應用首頁，並包含 token 參數供前端處理
        frontend_url = f"{settings.FRONTEND_URL}/?token={auth_result['access_token']}"
        print(f"frontend_url: {frontend_url}")
        response = RedirectResponse(url=frontend_url, status_code=status.HTTP_302_FOUND)
        
        # 同時設置 HttpOnly Cookie 作為備用
        response.set_cookie(
            key="access_token",
            value=f"Bearer {auth_result['access_token']}",
            httponly=True,
            max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 轉換為秒
            secure=settings.ENVIRONMENT == "production",  # 生產環境使用HTTPS
            samesite="lax"
        )
        
        logger.info(f"政府SSO登入成功: {auth_result['user_info']['username']}")
        return response

    except SSOTokenInvalidError as e:
        logger.warning(f"SSO Token無效: {str(e)}")
        return _create_error_redirect(e.user_message, "token_invalid")
    
    except SSOTokenExpiredError as e:
        logger.warning(f"SSO Token過期: {str(e)}")
        return _create_error_redirect(e.user_message, "token_expired")
    
    except SSOServiceUnavailableError as e:
        logger.error(f"SSO服務不可用: {str(e)}")
        return _create_error_redirect(e.user_message, "service_unavailable")
    
    except SSONetworkError as e:
        logger.error(f"SSO網路錯誤: {str(e)}")
        return _create_error_redirect(e.user_message, "network_error")
    
    except SSOError as e:
        logger.error(f"SSO錯誤: {str(e)}")
        # 記錄登入失敗
        if request:
            client_info = get_client_info(request)
            log_operation(
                db=db,
                user_id=None,
                action="SOAP_SSO_LOGIN_FAILED",
                table_name="users",
                new_values={
                    "error_type": e.error_code,
                    "error_message": str(e),
                    "sso_token_length": len(artifact) if artifact else 0,
                    "sso_token_type": token_type if artifact else None
                },
                ip_address=client_info.get("ip_address"),
                user_agent=client_info.get("user_agent")
            )
        return _create_error_redirect(e.user_message, e.error_code.lower())
    
    except Exception as e:
        logger.error(f"政府SSO登入過程發生未預期錯誤: {str(e)}", exc_info=True)
        # 記錄系統錯誤
        if request:
            client_info = get_client_info(request)
            log_operation(
                db=db,
                user_id=None,
                action="SOAP_SSO_LOGIN_SYSTEM_ERROR",
                table_name="users",
                new_values={
                    "error_message": str(e),
                    "error_type": "SYSTEM_ERROR"
                },
                ip_address=client_info.get("ip_address"),
                user_agent=client_info.get("user_agent")
            )
        return _create_error_redirect("系統暫時無法處理您的請求，請稍後再試", "system_error")


def _create_error_redirect(error_message: str, error_code: str = "unknown") -> RedirectResponse:
    """
    建立錯誤重導向回應
    
    Args:
        error_message: 錯誤訊息
        error_code: 錯誤代碼
        
    Returns:
        重導向回應
    """
    # 對錯誤訊息進行URL編碼
    from urllib.parse import quote
    encoded_message = quote(error_message)
    
    error_url = f"/login?error={encoded_message}&error_code={error_code}&source=sso"
    return RedirectResponse(url=error_url, status_code=status.HTTP_302_FOUND)


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    取得當前用戶資訊
    """
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        full_name=current_user.full_name,
        department=current_user.department.name if current_user.department else None,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        roles=[role.name for role in current_user.roles]
    )


@router.post("/logout", response_model=MessageResponse)
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    用戶登出
    """
    try:
        # 記錄登出操作
        client_info = get_client_info(request)
        log_operation(
            db=db,
            user_id=current_user.id,
            action="LOGOUT",
            table_name="users",
            new_values={
                "username": current_user.username,
                "logout_time": "now"
            },
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent")
        )
        
        return MessageResponse(
            success=True,
            message="登出成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登出失敗: {str(e)}"
        )


@router.post("/verify", response_model=dict)
async def verify_token(
    current_user: User = Depends(get_current_active_user)
):
    """
    驗證Token有效性
    """
    return {
        "valid": True,
        "user_id": current_user.id,
        "username": current_user.username,
        "roles": [role.name for role in current_user.roles]
    } 
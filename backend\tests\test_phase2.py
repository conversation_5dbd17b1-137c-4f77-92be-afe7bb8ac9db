"""
第二階段測試：核心功能與權限管控
"""
import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
import sys
import json
from datetime import datetime
import time

# 設定測試環境變數
import base64
# 建立32字節的測試金鑰並編碼為base64
test_key = b"test-encryption-key-32-bytes-abc"  # 32字節
os.environ["ENCRYPTION_KEY"] = base64.b64encode(test_key).decode('utf-8')
os.environ["JWT_SECRET_KEY"] = "test-jwt-secret-key-for-testing-purposes"

# 添加專案根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from app.models.database import Base, get_db
from app.models.user import User, Role, UserRole, Permission, RolePermission, Department
from app.models.personal_data import PersonalData
from app.models.audit_log import AuditLog
from app.utils.encryption import encrypt_data, decrypt_data
from app.utils.jwt_auth import create_access_token, hash_password
from app.core.config import settings


# 測試資料庫設定
SQLALCHEMY_DATABASE_URL = "sqlite:///./database/test_phase2_cba.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """覆蓋資料庫依賴"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="function")
def setup_database():
    """設定測試資料庫"""
    # 清理現有資料庫（忽略錯誤）
    try:
        Base.metadata.drop_all(bind=engine)
    except Exception:
        pass
    # 建立測試資料庫表
    Base.metadata.create_all(bind=engine)
    
    # 建立測試資料
    db = TestingSessionLocal()
    try:
        # 建立角色
        general_role = Role(name="general", description="一般用戶")
        global_role = Role(name="global", description="全域用戶")
        admin_role = Role(name="admin", description="管理者")
        
        db.add_all([general_role, global_role, admin_role])
        db.commit()
        
        # 建立權限
        permissions = [
            Permission(name="CREATE_PERSONAL_DATA", description="建立個人資料"),
            Permission(name="READ_PERSONAL_DATA", description="讀取個人資料"),
            Permission(name="READ_ALL_DATA", description="讀取所有資料"),
            Permission(name="MANAGE_ADMIN", description="管理者功能"),
            Permission(name="MANAGE_GLOBAL", description="全域管理")
        ]
        
        db.add_all(permissions)
        db.commit()
        
        # 分配權限給角色
        role_permissions = [
            # 一般用戶權限
            RolePermission(role_id=general_role.id, permission_id=permissions[0].id),
            RolePermission(role_id=general_role.id, permission_id=permissions[1].id),
            # 全域用戶權限
            RolePermission(role_id=global_role.id, permission_id=permissions[0].id),
            RolePermission(role_id=global_role.id, permission_id=permissions[1].id),
            RolePermission(role_id=global_role.id, permission_id=permissions[2].id),
            # 管理者權限
            RolePermission(role_id=admin_role.id, permission_id=permissions[0].id),
            RolePermission(role_id=admin_role.id, permission_id=permissions[1].id),
            RolePermission(role_id=admin_role.id, permission_id=permissions[2].id),
            RolePermission(role_id=admin_role.id, permission_id=permissions[3].id),
            RolePermission(role_id=admin_role.id, permission_id=permissions[4].id),
        ]
        
        db.add_all(role_permissions)
        db.commit()
        
        # 建立部門
        test_dept = Department(name="測試部門", description="測試用部門")
        admin_dept = Department(name="管理部門", description="管理用部門")
        
        db.add_all([test_dept, admin_dept])
        db.commit()
        
        # 建立測試用戶
        test_users = [
            User(
                username="test_general",
                hashed_password=hash_password("password123"),
                full_name="一般測試用戶",
                department_id=test_dept.id,
                is_active=True
            ),
            User(
                username="test_global",
                hashed_password=hash_password("password123"),
                full_name="全域測試用戶",
                department_id=test_dept.id,
                is_active=True
            ),
            User(
                username="test_admin",
                hashed_password=hash_password("password123"),
                full_name="管理者測試用戶",
                department_id=admin_dept.id,
                is_active=True
            )
        ]
        
        db.add_all(test_users)
        db.commit()
        
        # 分配角色給用戶
        user_roles = [
            UserRole(user_id=test_users[0].id, role_id=general_role.id),
            UserRole(user_id=test_users[1].id, role_id=global_role.id),
            UserRole(user_id=test_users[2].id, role_id=admin_role.id)
        ]
        
        db.add_all(user_roles)
        db.commit()
        
        yield db
        
    finally:
        db.close()
        # 清理測試資料庫
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client():
    """測試客戶端"""
    return TestClient(app)


def get_auth_headers(username: str, password: str = "password123", client=None):
    """取得認證標頭"""
    if client is None:
        client = TestClient(app)
    
    response = client.post("/api/v1/auth/login-json", json={
        "username": username,
        "password": password
    })
    assert response.status_code == 200
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


class TestPersonalDataAPI:
    """個人資料管理API測試"""
    
    def test_create_personal_data_success(self, setup_database, client):
        """測試建立個人資料成功"""
        headers = get_auth_headers("test_general", client=client)
        
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        assert response.status_code == 201
        
        result = response.json()
        assert result["name"] == "張測試"
        assert result["id_number"] == "A123456789"
        assert result["address"] == "台北市信義區測試路123號"
        assert "id" in result
    
    def test_create_personal_data_duplicate_id_number(self, setup_database, client):
        """測試重複身分證字號"""
        headers = get_auth_headers("test_general", client=client)
        
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        
        # 第一次建立
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        assert response.status_code == 201
        
        # 第二次建立（重複）
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        assert response.status_code == 409
        assert "身分證字號已存在" in response.json()["detail"]
    
    def test_create_personal_data_invalid_id_number(self, setup_database, client):
        """測試無效身分證字號"""
        headers = get_auth_headers("test_general", client=client)
        
        data = {
            "name": "張測試",
            "id_number": "INVALID123",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        assert response.status_code == 422
    
    def test_get_personal_data_list_general_user(self, setup_database, client):
        """測試一般用戶查詢個人資料列表"""
        headers = get_auth_headers("test_general", client=client)
        
        # 建立測試資料
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        client.post("/api/v1/personal-data/", json=data, headers=headers)
        
        # 查詢列表
        response = client.get("/api/v1/personal-data/", headers=headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["total"] == 1
        assert len(result["items"]) == 1
        assert result["items"][0]["name"] == "張測試"
    
    def test_get_personal_data_list_global_user(self, setup_database, client):
        """測試全域用戶查詢所有資料"""
        # 一般用戶建立資料
        general_headers = get_auth_headers("test_general", client=client)
        data1 = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "一般用戶資料"
        }
        response1 = client.post("/api/v1/personal-data/", json=data1, headers=general_headers)
        assert response1.status_code == 201
        
        # 全域用戶建立資料
        global_headers = get_auth_headers("test_global", client=client)
        data2 = {
            "name": "李測試",
            "id_number": "B123456780",
            "address": "高雄市前鎮區測試街456號",
            "notes": "全域用戶資料"
        }
        response2 = client.post("/api/v1/personal-data/", json=data2, headers=global_headers)
        assert response2.status_code == 201
        
        # 全域用戶查詢所有資料
        response = client.get("/api/v1/personal-data/", headers=global_headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["total"] == 2
        assert len(result["items"]) == 2
    
    def test_get_personal_data_by_id(self, setup_database, client):
        """測試按ID查詢個人資料"""
        headers = get_auth_headers("test_general", client=client)
        
        # 建立資料
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        data_id = response.json()["id"]
        
        # 查詢資料
        response = client.get(f"/api/v1/personal-data/{data_id}", headers=headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["name"] == "張測試"
        assert result["id_number"] == "A123456789"
    
    def test_update_personal_data(self, setup_database, client):
        """測試更新個人資料"""
        headers = get_auth_headers("test_general", client=client)
        
        # 建立資料
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "原始資料"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        data_id = response.json()["id"]
        
        # 更新資料
        update_data = {
            "name": "張測試更新",
            "address": "台北市大安區更新路456號",
            "notes": "更新後資料"
        }
        response = client.put(f"/api/v1/personal-data/{data_id}", json=update_data, headers=headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["name"] == "張測試更新"
        assert result["address"] == "台北市大安區更新路456號"
        assert result["notes"] == "更新後資料"
    
    def test_delete_personal_data(self, setup_database, client):
        """測試刪除個人資料"""
        headers = get_auth_headers("test_general", client=client)
        
        # 建立資料
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試用資料"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        data_id = response.json()["id"]
        
        # 刪除資料
        response = client.delete(f"/api/v1/personal-data/{data_id}", headers=headers)
        assert response.status_code == 200
        assert "成功刪除" in response.json()["message"]
        
        # 驗證資料已刪除
        response = client.get(f"/api/v1/personal-data/{data_id}", headers=headers)
        assert response.status_code == 404


class TestAuthAPI:
    """認證API測試"""
    
    def test_login_success(self, setup_database, client):
        """測試登入成功"""
        response = client.post("/api/v1/auth/login-json", json={
            "username": "test_general",
            "password": "password123"
        })
        assert response.status_code == 200
        
        result = response.json()
        assert "access_token" in result
        assert result["token_type"] == "bearer"
        assert "user_info" in result
        assert result["user_info"]["username"] == "test_general"
    
    def test_login_invalid_credentials(self, setup_database, client):
        """測試登入失敗"""
        response = client.post("/api/v1/auth/login-json", json={
            "username": "test_general",
            "password": "wrong_password"
        })
        assert response.status_code == 401
    
    def test_get_current_user_info(self, setup_database, client):
        """測試取得當前用戶資訊"""
        headers = get_auth_headers("test_general", client=client)
        
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["username"] == "test_general"
        assert result["full_name"] == "一般測試用戶"
        assert result["roles"] == ["general"]
    
    def test_verify_token(self, setup_database, client):
        """測試驗證Token"""
        headers = get_auth_headers("test_general", client=client)
        
        response = client.post("/api/v1/auth/verify", headers=headers)
        assert response.status_code == 200
        
        result = response.json()
        assert result["valid"] is True
        assert result["username"] == "test_general"


class TestPermissionControl:
    """權限控制測試"""
    
    def test_general_user_cannot_access_others_data(self, setup_database, client):
        """測試一般用戶無法存取他人資料"""
        # 一般用戶建立資料
        general_headers = get_auth_headers("test_general", client=client)
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "一般用戶資料"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=general_headers)
        data_id = response.json()["id"]
        
        # 另一個一般用戶嘗試存取
        other_headers = get_auth_headers("test_global", client=client)  # 使用不同用戶
        response = client.get(f"/api/v1/personal-data/{data_id}", headers=other_headers)
        # 全域用戶可以存取，但如果是另一個一般用戶應該會被拒絕
    
    def test_admin_can_access_all_data(self, setup_database, client):
        """測試管理者可以存取所有資料"""
        # 一般用戶建立資料
        general_headers = get_auth_headers("test_general", client=client)
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "一般用戶資料"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=general_headers)
        data_id = response.json()["id"]
        
        # 管理者存取
        admin_headers = get_auth_headers("test_admin", client=client)
        response = client.get(f"/api/v1/personal-data/{data_id}", headers=admin_headers)
        assert response.status_code == 200


class TestEncryptionDecryption:
    """加密解密功能測試"""
    
    def test_id_number_encryption_uniqueness(self):
        """測試身分證字號加密唯一性"""
        id_number = "A123456789"
        
        # 相同明文多次加密應產生不同密文
        encrypted1 = encrypt_data(id_number)
        encrypted2 = encrypt_data(id_number)
        
        assert encrypted1 != encrypted2
        
        # 但解密後應該相同
        decrypted1 = decrypt_data(encrypted1)
        decrypted2 = decrypt_data(encrypted2)
        
        assert decrypted1 == id_number
        assert decrypted2 == id_number
    
    def test_encryption_performance(self):
        """測試加密效能"""
        test_data = ["A123456789", "B987654321", "C555666777"] * 100
        
        start_time = time.time()
        for data in test_data:
            encrypted = encrypt_data(data)
            decrypted = decrypt_data(encrypted)
            assert decrypted == data
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_operation = total_time / (len(test_data) * 2)  # 加密+解密
        
        # 每次操作應該少於10ms
        assert avg_time_per_operation < 0.01


class TestAuditLogging:
    """審計日誌測試"""
    
    def test_audit_log_creation_on_personal_data_operations(self, setup_database, client):
        """測試個人資料操作時審計日誌建立"""
        headers = get_auth_headers("test_admin", client=client)
        
        # 建立個人資料
        data = {
            "name": "張測試",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "測試審計日誌"
        }
        response = client.post("/api/v1/personal-data/", json=data, headers=headers)
        assert response.status_code == 201
        
        # 檢查審計日誌
        response = client.get("/api/v1/audit/logs", headers=headers)
        assert response.status_code == 200
        
        logs = response.json()
        assert logs["total"] > 0
        
        # 找到建立操作的日誌
        create_logs = [log for log in logs["items"] if log["action"] == "CREATE"]
        assert len(create_logs) > 0
        
        create_log = create_logs[0]
        assert create_log["table_name"] == "personal_data"
        assert create_log["new_values"]["name"] == "張測試"
        assert create_log["new_values"]["id_number"] == "[ENCRYPTED]"


def run_all_tests():
    """執行所有第二階段測試"""
    print("=" * 60)
    print("開始執行第二階段測試：核心功能與權限管控")
    print("=" * 60)
    
    # 執行pytest
    pytest_args = [
        __file__,
        "-v",
        "--tb=short",
        "--no-header"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    print("=" * 60)
    if exit_code == 0:
        print("✅ 第二階段所有測試通過")
    else:
        print("❌ 第二階段測試失敗")
    print("=" * 60)
    
    return exit_code == 0


if __name__ == "__main__":
    run_all_tests() 
#!/usr/bin/env python3
"""
前後端連接修正驗證測試
"""

import os
import sys
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional

class ConnectionValidator:
    """連接驗證器"""
    
    def __init__(self):
        self.base_url = os.getenv("API_BASE_URL", "http://localhost:8080")
        self.api_url = f"{self.base_url}/api/v1"
        self.timeout = 30
        self.session = requests.Session()
        self.token = None
        
    def print_step(self, step: str, message: str):
        """格式化輸出步驟"""
        print(f"[{step}] {message}")
    
    def test_health_check(self) -> bool:
        """測試健康檢查 - 驗證修正後的邏輯"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                raw_data = response.json()
                self.print_step("📊", f"原始回傳: {json.dumps(raw_data, indent=2, ensure_ascii=False)}")
                
                # 模擬前端修正後的邏輯
                if raw_data.get("success") and raw_data.get("data"):
                    status = raw_data["data"].get("status", "unknown")
                else:
                    status = raw_data.get("status", "unknown")
                
                self.print_step("✅", f"健康檢查修正後狀態: {status}")
                return status == "healthy"
            else:
                self.print_step("❌", f"健康檢查失敗: {response.status_code}")
                return False
        except Exception as e:
            self.print_step("❌", f"健康檢查異常: {str(e)}")
            return False
    
    def test_login(self, username: str = "009999", password: str = "password") -> bool:
        """測試登入功能"""
        try:
            login_data = {
                "username": username,
                "password": password
            }
            
            response = self.session.post(
                f"{self.api_url}/auth/login-json",
                json=login_data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.print_step("✅", "登入成功，獲得Token")
                return True
            else:
                self.print_step("❌", f"登入失敗: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            self.print_step("❌", f"登入異常: {str(e)}")
            return False
    
    def test_validate_id_number(self, id_number: str) -> Optional[Dict[str, Any]]:
        """測試身分證字號驗證 - 驗證修正後的邏輯"""
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            
            url = f"{self.api_url}/personal-data/validation/id-number/{id_number}"
            
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                raw_data = response.json()
                self.print_step("📊", f"原始回傳: {json.dumps(raw_data, indent=2, ensure_ascii=False)}")
                
                # 模擬前端修正後的邏輯
                processed_data = {
                    "valid": True,  # 如果API呼叫成功，表示格式有效
                    "exists": raw_data.get("exists", False),
                    "message": raw_data.get("message", ""),
                    "id_number": raw_data.get("id_number", id_number)
                }
                
                self.print_step("✅", f"身分證字號驗證修正後格式: {json.dumps(processed_data, indent=2, ensure_ascii=False)}")
                return processed_data
            else:
                self.print_step("❌", f"身分證字號驗證失敗: {response.status_code} - {response.text}")
                
                # 模擬錯誤情況
                error_data = {
                    "valid": False,
                    "exists": False,
                    "message": f"HTTP {response.status_code}",
                    "id_number": id_number
                }
                return error_data
        except Exception as e:
            self.print_step("❌", f"身分證字號驗證異常: {str(e)}")
            
            # 模擬異常情況
            error_data = {
                "valid": False,
                "exists": False,
                "message": str(e),
                "id_number": id_number
            }
            return error_data
    
    def test_create_personal_data(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """測試建立個人資料"""
        try:
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            response = self.session.post(
                f"{self.api_url}/personal-data/",
                json=data,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 201:
                result = response.json()
                self.print_step("✅", f"個人資料建立成功: ID={result.get('id')}")
                return result
            else:
                self.print_step("❌", f"個人資料建立失敗: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.print_step("❌", f"個人資料建立異常: {str(e)}")
            return None
    
    def test_multiple_requests(self) -> bool:
        """測試多次請求（模擬重複請求情況）"""
        self.print_step("🔄", "測試多次認證請求...")
        
        success_count = 0
        total_requests = 5
        
        for i in range(total_requests):
            try:
                headers = {"Authorization": f"Bearer {self.token}"}
                response = self.session.post(
                    f"{self.api_url}/auth/verify",
                    headers=headers,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    success_count += 1
                    self.print_step("✅", f"請求 {i+1}/{total_requests} 成功")
                else:
                    self.print_step("❌", f"請求 {i+1}/{total_requests} 失敗")
                    
            except Exception as e:
                self.print_step("❌", f"請求 {i+1}/{total_requests} 異常: {str(e)}")
        
        self.print_step("📊", f"多次請求測試完成: {success_count}/{total_requests} 成功")
        return success_count == total_requests
    
    def run_validation(self):
        """執行完整驗證"""
        print("🔍 前後端連接修正驗證開始")
        print("=" * 60)
        
        results = {}
        
        # 1. 健康檢查修正驗證
        print("\n1️⃣ 健康檢查修正驗證")
        results["health_check"] = self.test_health_check()
        
        # 2. 登入測試
        print("\n2️⃣ 登入測試")
        results["login"] = self.test_login()
        
        if not results["login"]:
            print("❌ 登入失敗，無法繼續後續測試")
            return False
        
        # 3. 身分證字號驗證修正測試
        print("\n3️⃣ 身分證字號驗證修正測試")
        validation_result = self.test_validate_id_number("A123456789")
        results["id_validation"] = validation_result and validation_result.get("valid", False)
        
        # 4. 多次請求測試
        print("\n4️⃣ 多次請求測試")
        results["multiple_requests"] = self.test_multiple_requests()
        
        # 5. 建立個人資料測試
        print("\n5️⃣ 建立個人資料測試")
        test_data = {
            "name": "修正驗證測試用戶",
            "id_number": "F123456789",
            "address": "台北市修正測試區測試路456號",
            "notes": "前後端連接修正驗證測試建立的資料"
        }
        
        create_result = self.test_create_personal_data(test_data)
        results["create_data"] = create_result is not None
        
        # 6. 總結
        print("\n📊 修正驗證結果總結")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in results.items():
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有測試通過！前後端連接修正成功")
            print("📋 修正項目:")
            print("  ✅ 健康檢查回傳格式適配")
            print("  ✅ 身分證字號驗證格式適配")
            print("  ✅ 重複請求問題解決")
            print("  ✅ 認證機制緩存優化")
        else:
            print("\n❌ 部分測試失敗，請檢查相關問題")
        
        return all_passed

def main():
    """主函數"""
    validator = ConnectionValidator()
    success = validator.run_validation()
    
    if success:
        print("\n🎉 前後端連接修正驗證完成！")
        print("💡 提示：重複請求已優化，前端應該不會再顯示錯誤訊息")
    else:
        print("\n❌ 修正驗證失敗，請檢查問題後重試")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
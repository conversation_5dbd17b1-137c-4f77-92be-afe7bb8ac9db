#!/usr/bin/env python3
"""
測試修復的功能
"""

import sys
import os
import pytz
from datetime import datetime

def test_timezone_fix():
    """測試台北時區修復"""
    print("🕒 測試台北時區功能...")
    
    try:
        # 設定台北時區
        taipei_tz = pytz.timezone('Asia/Taipei')
        
        # 測試時間轉換
        test_timestamp = "2024-12-19T10:30:00Z"
        timestamp = datetime.fromisoformat(test_timestamp.replace('Z', '+00:00')).astimezone(taipei_tz)
        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"✅ 原始時間: {test_timestamp}")
        print(f"✅ 台北時間: {formatted_time}")
        print("✅ 台北時區功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 台北時區測試失敗: {str(e)}")
        return False

def test_api_response_format():
    """測試API回應格式處理"""
    print("\n📊 測試API回應格式處理...")
    
    try:
        # 模擬統計API回應
        mock_overview_response = {
            "success": True,
            "data": {
                "total_records": 100,
                "monthly_new": 25,
                "weekly_new": 8,
                "daily_new": 3,
                "monthly_delta": 5
            }
        }
        
        # 模擬圖表API回應
        mock_charts_response = {
            "success": True,
            "data": {
                "chart_type": "monthly",
                "results": [
                    {"label": "2024-11", "count": 20},
                    {"label": "2024-12", "count": 25}
                ]
            }
        }
        
        # 測試數據提取
        overview = mock_overview_response.get("data", {})
        charts_data = mock_charts_response.get("data", {})
        monthly_data = charts_data.get("results", [])
        
        print(f"✅ 總記錄數: {overview.get('total_records', 0)}")
        print(f"✅ 本月新增: {overview.get('monthly_new', 0)}")
        print(f"✅ 月度數據點: {len(monthly_data)}")
        print("✅ API回應格式處理正常")
        return True
        
    except Exception as e:
        print(f"❌ API回應格式測試失敗: {str(e)}")
        return False

def test_audit_log_format():
    """測試審計日誌格式處理"""
    print("\n📋 測試審計日誌格式處理...")
    
    try:
        # 模擬審計日誌回應
        mock_audit_response = {
            "items": [
                {
                    "id": 1,
                    "username": "測試用戶",
                    "action": "CREATE",
                    "table_name": "payee_data",
                    "timestamp": "2024-12-19T10:30:00+08:00"
                },
                {
                    "id": 2,
                    "username": "管理員",
                    "action": "UPDATE",
                    "table_name": "payee_data",
                    "timestamp": "2024-12-19T11:15:00+08:00"
                }
            ],
            "total": 2
        }
        
        # 測試動作文字轉換
        action_text_map = {
            "CREATE": "新增",
            "UPDATE": "更新", 
            "DELETE": "刪除",
            "create": "新增",
            "update": "更新",
            "delete": "刪除"
        }
        
        taipei_tz = pytz.timezone('Asia/Taipei')
        
        for log in mock_audit_response.get("items", []):
            action = log.get("action", "")
            action_text = action_text_map.get(action, action)
            
            timestamp_str = log.get('timestamp', '')
            if timestamp_str:
                if 'T' in timestamp_str:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                else:
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_time = "時間未知"
            
            print(f"✅ {log.get('username', '未知用戶')} {action_text}了一筆受款人資料 - {formatted_time}")
        
        print("✅ 審計日誌格式處理正常")
        return True
        
    except Exception as e:
        print(f"❌ 審計日誌格式測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始測試修復功能...\n")
    
    results = []
    
    # 執行各項測試
    results.append(test_timezone_fix())
    results.append(test_api_response_format())
    results.append(test_audit_log_format())
    
    # 總結測試結果
    print(f"\n📊 測試結果總結:")
    print(f"✅ 通過測試: {sum(results)}")
    print(f"❌ 失敗測試: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有修復功能測試通過！")
        return 0
    else:
        print("\n⚠️ 部分測試失敗，請檢查修復內容")
        return 1

if __name__ == "__main__":
    sys.exit(main())

[project]
name = "cba-personal-data-system"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "cryptography>=45.0.5",
    "fastapi>=0.115.14",
    "pydantic-settings>=2.10.1",
    "pydantic>=2.11.7",
    "pytest>=8.4.1",
    "python-jwt>=4.1.0",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "streamlit>=1.46.1",
    "uvicorn>=0.35.0",
    "psutil>=7.0.0",
    "python-dotenv>=1.1.1",
    "httpx>=0.28.1",
    "python-jose>=3.5.0",
    "flask>=3.1.1",
    "spyne>=2.14.0",
    "lxml>=6.0.0",
    "zeep>=4.3.1",
    "werkzeug>=3.1.3",
    "bcrypt>=4.3.0",
]

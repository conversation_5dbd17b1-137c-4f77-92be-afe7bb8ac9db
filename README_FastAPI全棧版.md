# CBA受款人資料搜集系統 - FastAPI全棧版

## 📋 系統概述

CBA受款人資料搜集系統FastAPI全棧版，使用FastAPI同時提供後端API和前端Web介面，採用Jinja2模板引擎和Bootstrap 5 UI框架。

## 🏗️ 系統架構

```
用戶瀏覽器
    ↓ http://localhost:80
FastAPI全棧應用 (80埠)
    ├── /api/* → 後端API服務
    ├── /docs → API文檔
    ├── /health → 健康檢查
    ├── /static/* → 靜態檔案 (CSS/JS)
    └── /* → 前端Web頁面 (Jinja2模板)
```

## 🚀 快速開始

### 方法一：一鍵啟動 (推薦)

```bash
# Linux/macOS
chmod +x start_integrated.sh
sudo ./start_integrated.sh

# Windows (以管理員身分執行)
start_integrated.bat
```

### 方法二：手動啟動

```bash
# 1. 安裝依賴
cd backend
uv sync

# 2. 啟動服務
export ENVIRONMENT=production
export HOST=0.0.0.0
export PORT=80
sudo uv run uvicorn main:app --host 0.0.0.0 --port 80
```

## 🌐 存取地址

- **主要入口**: http://localhost
- **登入頁面**: http://localhost/login
- **儀表板**: http://localhost/dashboard
- **受款人管理**: http://localhost/payee
- **API文檔**: http://localhost/docs
- **健康檢查**: http://localhost/health

## 📦 技術棧

### 後端技術
- **FastAPI**: Web框架和API服務
- **Jinja2**: 模板引擎
- **SQLAlchemy**: ORM資料庫操作
- **Pydantic**: 資料驗證
- **JWT**: 用戶認證
- **SQLite**: 資料庫 (可切換到PostgreSQL)

### 前端技術
- **Bootstrap 5**: UI框架
- **Bootstrap Icons**: 圖示庫
- **Vanilla JavaScript**: 前端邏輯
- **HTML5/CSS3**: 標準Web技術

## 🔧 專案結構

```
backend/
├── app/
│   ├── api/              # API路由
│   ├── frontend/         # 前端模組
│   │   ├── routes.py     # 前端路由
│   │   ├── templates/    # Jinja2模板
│   │   │   ├── base.html
│   │   │   ├── auth/
│   │   │   ├── dashboard/
│   │   │   └── payee/
│   │   └── static/       # 靜態檔案
│   │       ├── css/
│   │       └── js/
│   ├── models/           # 資料模型
│   ├── services/         # 業務邏輯
│   └── core/             # 核心功能
├── main.py               # 應用入口
└── pyproject.toml        # 依賴配置
```

## 🎨 功能特色

### 用戶介面
- **響應式設計**: 支援桌面和行動裝置
- **現代化UI**: Bootstrap 5設計風格
- **直觀操作**: 簡潔的用戶體驗
- **即時驗證**: 表單資料即時檢查

### 系統功能
- **用戶認證**: 登入/登出，Session管理
- **權限控制**: 角色型權限管理
- **受款人管理**: CRUD操作，搜尋篩選
- **資料驗證**: 身分證號、電話號碼驗證
- **審計日誌**: 操作記錄追蹤

### 技術優勢
- **單一服務**: 前後端整合部署
- **高效能**: FastAPI異步處理
- **易維護**: 模組化架構設計
- **可擴展**: 支援水平擴展

## 🛠️ 開發指南

### 添加新頁面

1. **創建模板**
   ```bash
   # 在 backend/app/frontend/templates/ 創建新模板
   touch backend/app/frontend/templates/new_page.html
   ```

2. **添加路由**
   ```python
   # 在 backend/app/frontend/routes.py 添加路由
   @frontend_router.get("/new-page", response_class=HTMLResponse)
   async def new_page(request: Request):
       return templates.TemplateResponse("new_page.html", {
           "request": request,
           "title": "新頁面"
       })
   ```

3. **更新導航**
   ```html
   <!-- 在 base.html 導航欄添加連結 -->
   <li class="nav-item">
       <a class="nav-link" href="/new-page">新頁面</a>
   </li>
   ```

### 自定義樣式

```css
/* 在 backend/app/frontend/static/css/style.css 添加樣式 */
.custom-style {
    color: #007bff;
    font-weight: bold;
}
```

### 添加JavaScript功能

```javascript
// 在 backend/app/frontend/static/js/app.js 添加功能
function customFunction() {
    console.log('自定義功能');
}
```

## 🔒 安全性

### 認證機制
- **JWT Token**: 安全的用戶認證
- **Cookie存儲**: HttpOnly Cookie防止XSS
- **Session管理**: 自動過期和刷新

### 資料保護
- **輸入驗證**: 前後端雙重驗證
- **SQL注入防護**: ORM參數化查詢
- **XSS防護**: 模板自動轉義
- **CSRF防護**: 表單Token驗證

## 📊 效能優化

### 前端優化
- **靜態檔案快取**: 適當的Cache-Control標頭
- **CSS/JS壓縮**: 生產環境資源壓縮
- **圖片優化**: WebP格式支援
- **懶載入**: 按需載入內容

### 後端優化
- **異步處理**: FastAPI異步支援
- **資料庫連接池**: SQLAlchemy連接管理
- **查詢優化**: 索引和查詢最佳化
- **快取策略**: Redis快取支援

## 🚨 故障排除

### 常見問題

1. **80埠被佔用**
   ```bash
   # 查看佔用進程
   sudo lsof -i :80
   
   # 停止佔用進程
   sudo lsof -ti:80 | xargs sudo kill -9
   ```

2. **模板找不到**
   ```bash
   # 檢查模板目錄
   ls -la backend/app/frontend/templates/
   
   # 確認模板路徑正確
   ```

3. **靜態檔案404**
   ```bash
   # 檢查靜態檔案目錄
   ls -la backend/app/frontend/static/
   
   # 確認檔案權限
   chmod -R 644 backend/app/frontend/static/
   ```

4. **認證失敗**
   ```bash
   # 檢查JWT設定
   echo $JWT_SECRET_KEY
   
   # 清除瀏覽器Cookie
   ```

### 日誌查看

```bash
# 查看應用日誌
tail -f logs/app.log

# 查看錯誤日誌
tail -f logs/error.log

# 開啟除錯模式
export DEBUG=true
```

## 🔄 從Vue.js版本遷移

如果您之前使用Vue.js版本：

1. **停止Vue.js服務**
   ```bash
   # 停止前端開發服務器
   pkill -f "npm run dev"
   
   # 停止nginx (如果使用)
   sudo systemctl stop nginx
   ```

2. **啟動FastAPI全棧版**
   ```bash
   sudo ./start_integrated.sh
   ```

3. **資料遷移**
   - 資料庫檔案保持不變
   - 用戶帳號和資料完全相容
   - 無需額外遷移步驟

## 📚 API文檔

### 前端路由
- `GET /` - 首頁 (重定向到登入或儀表板)
- `GET /login` - 登入頁面
- `POST /login` - 處理登入
- `GET /logout` - 登出
- `GET /dashboard` - 儀表板
- `GET /payee` - 受款人列表
- `GET /payee/create` - 新增受款人頁面
- `POST /payee/create` - 處理新增受款人
- `GET /payee/{id}` - 受款人詳情
- `GET /payee/{id}/edit` - 編輯受款人頁面
- `POST /payee/{id}/edit` - 處理編輯受款人

### API路由
- `GET /api/v1/auth/health` - 認證服務健康檢查
- `POST /api/v1/auth/login` - API登入
- `GET /api/v1/payee-data/` - 獲取受款人列表
- `POST /api/v1/payee-data/` - 創建受款人
- `GET /api/v1/payee-data/{id}` - 獲取受款人詳情
- `PUT /api/v1/payee-data/{id}` - 更新受款人
- `DELETE /api/v1/payee-data/{id}` - 刪除受款人

## 🎯 生產環境部署

### 使用systemd

```bash
# 創建服務檔案
sudo tee /etc/systemd/system/cba-fastapi.service << EOF
[Unit]
Description=CBA受款人資料搜集系統 - FastAPI全棧版
After=network.target

[Service]
Type=exec
User=root
WorkingDirectory=/opt/cba-system/backend
Environment=ENVIRONMENT=production
Environment=HOST=0.0.0.0
Environment=PORT=80
ExecStart=/opt/cba-system/backend/.venv/bin/uvicorn main:app --host 0.0.0.0 --port 80 --workers 4
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 啟用和啟動服務
sudo systemctl enable cba-fastapi
sudo systemctl start cba-fastapi
```

### 使用Docker

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 複製依賴檔案
COPY backend/pyproject.toml backend/uv.lock ./

# 安裝uv和依賴
RUN pip install uv
RUN uv sync --frozen

# 複製應用程式碼
COPY backend/ ./

# 暴露埠
EXPOSE 80

# 啟動應用
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "80"]
```

## 🎉 總結

FastAPI全棧版提供了：

- ✅ **統一技術棧**: 純Python開發
- ✅ **簡化部署**: 單一服務部署
- ✅ **高效能**: FastAPI異步處理
- ✅ **易維護**: 模組化架構
- ✅ **現代化UI**: Bootstrap 5設計
- ✅ **完整功能**: 所有業務功能齊全

立即開始使用：
```bash
# Linux/macOS
sudo ./start_integrated.sh

# Windows
start_integrated.bat
```

然後存取 http://localhost 開始使用！🚀

"""
前端組件功能測試
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

# 確保可以導入組件
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestPersonalDataForm:
    """個人資料表單組件測試"""
    
    @patch('streamlit.form')
    @patch('streamlit.text_input')
    @patch('streamlit.text_area')
    @patch('streamlit.form_submit_button')
    @patch('streamlit.success')
    @patch('streamlit.error')
    def test_form_submission_success(self, mock_error, mock_success, mock_submit, 
                                   mock_text_area, mock_text_input, mock_form):
        """測試表單提交成功情況"""
        # 模擬表單輸入
        mock_text_input.side_effect = ["張三", "A123456789", "台北市信義區信義路100號"]
        mock_text_area.return_value = "測試備註"
        mock_submit.return_value = True
        mock_form.return_value.__enter__ = Mock()
        mock_form.return_value.__exit__ = Mock()
        
        # 導入組件並測試
        from components.personal_data_form import personal_data_form
        
        # 模擬API成功回應
        with patch('requests.post') as mock_post:
            mock_response = Mock()
            mock_response.status_code = 201
            mock_response.json.return_value = {"message": "新增成功", "id": 1}
            mock_post.return_value = mock_response
            
            # 執行表單
            personal_data_form()
            
            # 驗證成功訊息被調用
            mock_success.assert_called()
    
    @patch('streamlit.form')
    @patch('streamlit.text_input')
    @patch('streamlit.text_area')
    @patch('streamlit.form_submit_button')
    @patch('streamlit.error')
    def test_form_validation_error(self, mock_error, mock_submit, 
                                 mock_text_area, mock_text_input, mock_form):
        """測試表單驗證錯誤情況"""
        # 模擬無效輸入
        mock_text_input.side_effect = ["", "無效身分證", ""]
        mock_text_area.return_value = ""
        mock_submit.return_value = True
        mock_form.return_value.__enter__ = Mock()
        mock_form.return_value.__exit__ = Mock()
        
        from components.personal_data_form import personal_data_form
        
        # 執行表單
        personal_data_form()
        
        # 驗證錯誤訊息被調用
        mock_error.assert_called()
    
    def test_id_number_validation_component(self):
        """測試身分證字號驗證組件"""
        from components.personal_data_form import validate_id_number
        
        # 測試有效身分證字號
        valid_cases = [
            ("A123456789", True),
            ("B234567890", True),
            ("  A123456789  ", True),  # 帶空格
            ("a123456789", True),      # 小寫
        ]
        
        for id_num, expected in valid_cases:
            is_valid, _ = validate_id_number(id_num)
            assert is_valid == expected, f"身分證字號 {id_num} 驗證結果不符預期"
        
        # 測試無效身分證字號
        invalid_cases = [
            ("", False),
            ("123456789", False),
            ("A12345678", False),
            ("A1234567890", False),
            ("Z123456789", False),
        ]
        
        for id_num, expected in invalid_cases:
            is_valid, _ = validate_id_number(id_num)
            assert is_valid == expected, f"身分證字號 {id_num} 驗證結果不符預期"

class TestDataQuery:
    """資料查詢組件測試"""
    
    @patch('streamlit.selectbox')
    @patch('streamlit.text_input')
    @patch('streamlit.date_input')
    @patch('streamlit.button')
    @patch('streamlit.dataframe')
    def test_query_interface(self, mock_dataframe, mock_button, mock_date, 
                           mock_text_input, mock_selectbox):
        """測試查詢介面功能"""
        # 模擬查詢條件輸入
        mock_selectbox.return_value = "姓名"
        mock_text_input.return_value = "張三"
        mock_button.return_value = True
        
        from components.data_query import data_query
        
        # 模擬API回應
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "data": [
                    {
                        "id": 1,
                        "name": "張三",
                        "id_number_masked": "A123****89",
                        "address": "台北市信義區信義路100號",
                        "notes": "測試資料",
                        "created_by": "test_user",
                        "created_at": "2024-12-19 10:30:00"
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10
            }
            mock_get.return_value = mock_response
            
            # 執行查詢
            data_query()
            
            # 驗證dataframe被調用
            mock_dataframe.assert_called()
    
    def test_data_filtering(self, sample_personal_data):
        """測試資料篩選邏輯"""
        data = sample_personal_data
        
        # 測試姓名篩選
        def filter_by_name(data, name):
            if name:
                return [item for item in data if name in item["name"]]
            return data
        
        filtered = filter_by_name(data, "測試用戶一")
        assert len(filtered) == 1
        assert filtered[0]["name"] == "測試用戶一"
        
        # 測試建立者篩選
        def filter_by_creator(data, creator):
            if creator:
                return [item for item in data if creator in item["created_by"]]
            return data
        
        filtered = filter_by_creator(data, "general_user")
        assert len(filtered) == 1
        assert filtered[0]["created_by"] == "general_user"
    
    def test_pagination_logic(self, sample_personal_data):
        """測試分頁邏輯"""
        data = sample_personal_data * 10  # 擴展為20筆資料
        
        def paginate_data(data, page, per_page):
            start = (page - 1) * per_page
            end = start + per_page
            return data[start:end]
        
        # 測試第一頁
        page1 = paginate_data(data, 1, 10)
        assert len(page1) == 10
        
        # 測試第二頁
        page2 = paginate_data(data, 2, 10)
        assert len(page2) == 10
        
        # 測試最後一頁（不足10筆）
        page3 = paginate_data(data, 3, 10)
        assert len(page3) == 0  # 因為只有20筆資料
    
    @patch('streamlit.download_button')
    def test_export_functionality(self, mock_download_button, sample_personal_data):
        """測試資料匯出功能"""
        from components.data_query import export_data_to_csv
        
        # 測試CSV生成
        data = sample_personal_data
        df = pd.DataFrame(data)
        csv_data = df.to_csv(index=False)
        
        # 驗證CSV內容
        assert "測試用戶一" in csv_data
        assert "A123****89" in csv_data
        
        # 測試CSV匯出
        user_info = {"roles": ["admin"]}
        csv_bytes = export_data_to_csv(data, user_info)
        assert len(csv_bytes) > 0

class TestUIComponents:
    """UI組件測試"""
    
    @patch('streamlit.sidebar')
    @patch('streamlit.selectbox')
    def test_sidebar_navigation(self, mock_selectbox, mock_sidebar):
        """測試側邊欄導航"""
        # 模擬側邊欄選擇
        mock_selectbox.return_value = "新增資料"
        
        from main import render_sidebar
        
        # 測試導航渲染
        render_sidebar()
        
        # 驗證selectbox被調用
        mock_selectbox.assert_called()
    
    @patch('streamlit.info')
    @patch('streamlit.warning')
    @patch('streamlit.error')
    @patch('streamlit.success')
    def test_notification_system(self, mock_success, mock_error, mock_warning, mock_info):
        """測試通知系統"""
        # 模擬不同類型的通知
        import streamlit as st
        
        # 測試各種通知類型
        st.success("成功訊息")
        st.error("錯誤訊息")
        st.warning("警告訊息")
        st.info("資訊訊息")
        
        # 驗證各種通知被調用
        mock_success.assert_called_with("成功訊息")
        mock_error.assert_called_with("錯誤訊息")
        mock_warning.assert_called_with("警告訊息")
        mock_info.assert_called_with("資訊訊息")
    
    def test_permission_based_ui_rendering(self):
        """測試基於權限的UI渲染"""
        # 模擬權限檢查函數
        def render_ui_based_on_permission(user_permissions):
            ui_elements = []
            
            if "CREATE_PERSONAL_DATA" in user_permissions:
                ui_elements.append("新增資料表單")
            
            if "READ_ALL_DATA" in user_permissions:
                ui_elements.append("全域查詢功能")
            
            if "EXPORT_DATA" in user_permissions:
                ui_elements.append("資料匯出按鈕")
            
            if "MANAGE_USERS" in user_permissions:
                ui_elements.append("用戶管理選單")
            
            return ui_elements
        
        # 測試一般用戶UI
        general_permissions = ["CREATE_PERSONAL_DATA", "READ_OWN_DATA"]
        general_ui = render_ui_based_on_permission(general_permissions)
        assert "新增資料表單" in general_ui
        assert "全域查詢功能" not in general_ui
        assert "用戶管理選單" not in general_ui
        
        # 測試管理者UI
        admin_permissions = ["CREATE_PERSONAL_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_USERS"]
        admin_ui = render_ui_based_on_permission(admin_permissions)
        assert "新增資料表單" in admin_ui
        assert "全域查詢功能" in admin_ui
        assert "資料匯出按鈕" in admin_ui
        assert "用戶管理選單" in admin_ui

class TestFormInteraction:
    """表單互動測試"""
    
    def test_form_field_validation_interaction(self):
        """測試表單欄位驗證互動"""
        # 模擬即時驗證
        def validate_form_field(field_name, value):
            validation_rules = {
                "name": lambda x: len(x.strip()) >= 2 and len(x.strip()) <= 20,
                "id_number": lambda x: len(x.strip()) == 10 and x[0].isalpha(),
                "address": lambda x: len(x.strip()) <= 200,
                "notes": lambda x: len(x.strip()) <= 500
            }
            
            if field_name in validation_rules:
                return validation_rules[field_name](value)
            return True
        
        # 測試各欄位驗證
        assert validate_form_field("name", "張三") == True
        assert validate_form_field("name", "A") == False
        assert validate_form_field("id_number", "A123456789") == True
        assert validate_form_field("id_number", "123456789") == False
    
    def test_conditional_field_display(self):
        """測試條件式欄位顯示"""
        # 模擬基於角色的欄位顯示
        def get_visible_fields(user_role):
            base_fields = ["name", "id_number", "address", "notes"]
            
            if user_role == "admin":
                return base_fields + ["created_by", "created_at", "updated_at"]
            elif user_role == "global_user":
                return base_fields + ["created_by"]
            else:
                return base_fields
        
        # 測試不同角色的欄位顯示
        general_fields = get_visible_fields("general_user")
        assert "created_by" not in general_fields
        assert len(general_fields) == 4
        
        global_fields = get_visible_fields("global_user")
        assert "created_by" in global_fields
        assert len(global_fields) == 5
        
        admin_fields = get_visible_fields("admin")
        assert "created_by" in admin_fields
        assert "created_at" in admin_fields
        assert len(admin_fields) == 7

class TestErrorHandling:
    """錯誤處理測試"""
    
    @patch('streamlit.error')
    @patch('requests.post')
    def test_api_error_handling(self, mock_post, mock_error):
        """測試API錯誤處理"""
        # 模擬API錯誤回應
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {"detail": "身分證字號已存在"}
        mock_post.return_value = mock_response
        
        # 模擬錯誤處理函數
        def handle_api_error(response):
            if response.status_code != 200 and response.status_code != 201:
                error_msg = response.json().get("detail", "未知錯誤")
                return False, error_msg
            return True, None
        
        # 測試錯誤處理
        success, error_msg = handle_api_error(mock_response)
        assert success == False
        assert error_msg == "身分證字號已存在"
    
    def test_network_error_handling(self):
        """測試網路錯誤處理"""
        import requests
        
        def handle_network_error():
            try:
                # 模擬網路請求
                response = requests.get("http://invalid-url")
                return True, None
            except requests.ConnectionError:
                return False, "網路連線錯誤，請檢查網路設定"
            except requests.Timeout:
                return False, "請求逾時，請稍後再試"
            except Exception as e:
                return False, f"發生未預期的錯誤: {str(e)}"
        
        # 這裡只測試函數邏輯，實際測試需要mock
        # 主要確保錯誤處理邏輯正確
        assert handle_network_error  # 函數存在且可調用 
# 🔐 SOAP SSO 部署與使用指南

## 📋 概述

本系統整合了**政府SOAP-based單一登入**功能，支援屏東縣政府SSO系統。

## 🏗️ 架構說明

### 整合元件
- **SOAPSSOAuthenticator**: 處理SOAP Web Service認證
- **API端點**: `/api/v1/auth/sso_login` 處理政府SSO登入
- **用戶管理**: 自動建立/更新用戶和部門資訊
- **JWT認證**: 整合現有的JWT認證流程

### SSO類型
- **政府SOAP SSO**
  - 使用SOAP Web Service
  - 支援XML格式用戶資料解析
  - 自動部門映射
  - 支援SAMLart和ssoToken1兩種Token格式

## ⚙️ 環境配置

### 1. 安裝依賴

```bash
# 安裝SOAP客戶端庫
uv add zeep

# 同步虛擬環境
uv sync
```

### 2. 環境變數設定

建立 `.env` 檔案：

```bash
# 複製配置範例
cp config.example .env
```

編輯 `.env` 檔案：

```bash
# ===========================================
# SOAP SSO 設定 (政府SSO)
# ===========================================
# 政府單一登入系統設定 - 已預設為屏東縣政府SSO
SSO_SOAP_WS_URL=https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL

# 如果是其他政府機關，請修改為對應的SOAP Web Service URL
# SSO_SOAP_WS_URL=https://your-gov-sso.gov.tw/path/to/service.asmx?WSDL
```

### 3. 其他重要配置

```bash
# JWT設定
JWT_SECRET_KEY=your-super-secret-jwt-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=480

# 資料庫設定
DATABASE_URL=sqlite:///./database/cba_personal_data.db

# 應用程式設定
ENVIRONMENT=development
FRONTEND_URL=http://localhost:8501
```

## 🚀 部署步驟

### 1. 準備環境

```bash
# 切換到後端目錄
cd backend

# 安裝依賴
uv sync

# 檢查整合狀態
uv run python test_soap_sso.py
```

### 2. 啟動服務

```bash
# 開發環境
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 生產環境
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 驗證部署

訪問以下URL確認功能：

- API文檔: http://localhost:8000/docs
- 健康檢查: http://localhost:8000/health
- SSO端點: http://localhost:8000/api/v1/auth/sso_login?ssoToken1=test

## 🔄 SSO流程說明

### 政府SOAP SSO流程

1. **用戶重導向**: 政府SSO系統將用戶重導向至您的系統
   ```
   GET /api/v1/auth/sso_login?ssoToken1={token}
   或
   GET /api/v1/auth/sso_login?SAMLart={token}
   ```

2. **Token驗證**: 系統呼叫SOAP Web Service驗證Token
   ```python
   # 接收參數（支援兩種格式）
   artifact = request.args.get('SAMLart') or request.args.get('ssoToken1')
   
   # 呼叫SOAP服務
   client = Client("https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL")
   result = client.service.getUserProfile(artifact)
   ```

3. **用戶資料解析**: 解析XML回應中的用戶資訊
   ```python
   from xml.etree import ElementTree as ET
   root = ET.fromstring(result)
   
   if root.tag == 'Error':
       # 處理錯誤狀況
       user_info = {'帳號': '', '姓名': '', 'source_org_no': ''}
   else:
       # 解析用戶資訊
       account = root.find('帳號').text
       name = root.find('姓名').text
       org_info = f"{root.find('機關名稱').text}:{root.find('機關代碼').text}:{root.find('單位代碼').text}"
       
       user_info = {
           '帳號': account,
           '姓名': name,
           'source_org_no': org_info,
           'ssoToken1': artifact   
       }
   ```

4. **用戶建立/更新**: 自動在本地資料庫建立或更新用戶
   - 建立部門（使用處層級代碼）
   - 建立/更新用戶資訊
   - 分配預設角色（一般員工）

5. **JWT Token生成**: 產生本地JWT Token

6. **Cookie設定**: 設定HTTP-only Cookie並重導向

## 🧪 測試指南

### 1. 功能測試

```bash
# 執行整合測試
uv run python test_soap_sso.py
```

### 2. 手動測試

#### 測試SOAP SSO登入
```bash
# 使用curl測試（需要有效的ssoToken1）
curl -X GET "http://localhost:8000/api/v1/auth/sso_login?ssoToken1=your-test-token" \
     -H "accept: application/json" \
     -L  # 跟隨重導向
```

#### 測試JWT Token驗證
```bash
# 使用取得的JWT Token測試API
curl -X GET "http://localhost:8000/api/v1/auth/me" \
     -H "Authorization: Bearer your-jwt-token"
```

### 3. 錯誤測試

```bash
# 測試無效Token
curl -X GET "http://localhost:8000/api/v1/auth/sso_login?ssoToken1=invalid" -L

# 測試缺少Token
curl -X GET "http://localhost:8000/api/v1/auth/sso_login" -L
```

## 🔧 故障排除

### 常見問題

#### 1. "No module named 'zeep'" 錯誤
```bash
# 解決方案：安裝zeep庫
uv add zeep
uv sync
```

#### 2. SOAP服務連線失敗
- 檢查網路連線
- 確認WSDL URL正確
- 檢查防火牆設定

#### 3. 用戶建立失敗
- 檢查資料庫權限
- 確認用戶模型完整
- 檢查部門建立邏輯

#### 4. JWT Token無效
- 檢查JWT_SECRET_KEY設定
- 確認Token過期時間
- 檢查Token格式

### 日誌檢查

```bash
# 檢查應用程式日誌
tail -f logs/app.log

# 檢查SSO相關日誌
grep "SOAP_SSO" logs/app.log
grep "SSOError" logs/app.log
```

## 🔗 外部系統整合

### 相關系統API端點

根據範例程式，系統還可整合以下外部API：

#### 結報系統
- **OAuth令牌端點**: `https://efs.pthg.gov.tw/GTHI/oauth2/token`
- **待辦API**: `https://efs.pthg.gov.tw/GTHI/api/to-do/find`
- **認證方式**: Basic Authentication + Bearer Token

#### 差勤系統
- **SOAP端點**: `https://otvmtest.pthg.gov.tw/PLM_AP/ws/syncWS.asmx?WSDL`
- **服務方法**: `GetCntWaitJson(account_map)`

### 整合範例
```python
# 結報系統OAuth認證
def get_access_token():
    token_url = "https://efs.pthg.gov.tw/GTHI/oauth2/token"
    client_id = "your-client-id"
    client_secret = "your-client-secret"
    
    credentials = f"{client_id}:{client_secret}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    
    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    payload = {'grant_type': 'client_credentials'}
    response = requests.post(token_url, headers=headers, data=payload, verify=False)
    return response.json().get('access_token')

# 差勤系統SOAP呼叫
def get_attendance_todo(account_map):
    client = Client('https://otvmtest.pthg.gov.tw/PLM_AP/ws/syncWS.asmx?WSDL')
    result = client.service.GetCntWaitJson(account_map)
    return result
```

## 📊 監控與維護

### 健康檢查端點

```python
# 檢查SOAP SSO服務狀態
GET /api/v1/health/sso
```

### 重要監控指標

1. **SSO登入成功率**
2. **SOAP服務回應時間**
3. **用戶建立/更新頻率**
4. **JWT Token簽發數量**
5. **外部API整合狀態**

### 定期維護

1. **清理過期Token**
2. **檢查SOAP服務可用性**
3. **監控用戶權限變更**
4. **備份用戶和部門資料**

## 🔒 安全考量

### 1. Token安全
- ssoToken1不應記錄完整內容
- 設定適當的JWT過期時間
- 使用HTTPS（生產環境）

### 2. SOAP安全
- 驗證SOAP回應格式
- 防範XML注入攻擊
- 設定連線超時

### 3. 用戶資料保護
- 遵循GDPR/個資法
- 加密敏感資料
- 審計用戶存取

## 📞 技術支援

### 系統配置
- **FastAPI版本**: 0.104.1+
- **Python版本**: 3.8+
- **zeep版本**: 4.2.1+

### 聯絡資訊
- 系統管理員: <EMAIL>
- 技術支援: <EMAIL>
- 緊急聯絡: +886-xxx-xxxxxx

---

## 📝 更新記錄

| 版本 | 日期 | 更新內容 |
|------|------|----------|
| 1.0.0 | 2024-12-19 | 初始SOAP SSO整合 |
| 1.0.1 | 2024-12-19 | 添加錯誤處理和日誌 |

---

*此指南適用於CBA人員資料調查系統的SOAP SSO整合功能。* 
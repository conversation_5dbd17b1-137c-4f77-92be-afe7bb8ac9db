#!/usr/bin/env python3
"""
CBA系統整合版測試腳本
測試前後端整合服務的各項功能
"""

import requests
import time
import sys
import json
from pathlib import Path

class IntegratedSystemTester:
    def __init__(self, base_url="http://localhost"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """記錄測試結果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def test_service_health(self):
        """測試服務健康狀態"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            success = response.status_code == 200
            
            if success:
                data = response.json()
                message = f"服務正常 - {data.get('message', '')}"
            else:
                message = f"HTTP {response.status_code}"
            
            self.log_test("服務健康檢查", success, message)
            return success
        except Exception as e:
            self.log_test("服務健康檢查", False, str(e))
            return False
    
    def test_frontend_loading(self):
        """測試前端頁面載入"""
        try:
            response = self.session.get(f"{self.base_url}/login", timeout=10)
            success = response.status_code == 200 and ("CBA" in response.text or "登入" in response.text)

            if success:
                message = "前端登入頁面載入正常"
            else:
                message = f"HTTP {response.status_code} 或內容異常"

            self.log_test("前端頁面載入", success, message)
            return success
        except Exception as e:
            self.log_test("前端頁面載入", False, str(e))
            return False
    
    def test_api_docs(self):
        """測試API文檔"""
        try:
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            success = response.status_code == 200
            
            if success:
                message = "API文檔可存取"
            else:
                message = f"HTTP {response.status_code}"
            
            self.log_test("API文檔存取", success, message)
            return success
        except Exception as e:
            self.log_test("API文檔存取", False, str(e))
            return False
    
    def test_api_endpoints(self):
        """測試主要API端點"""
        endpoints = [
            ("/api/v1/auth/health", "認證服務健康檢查"),
            ("/api/v1/payee-data/", "受款人資料API"),
        ]
        
        all_success = True
        for endpoint, description in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                # 401是正常的，因為沒有認證
                success = response.status_code in [200, 401, 422]
                
                if success:
                    message = f"端點可存取 (HTTP {response.status_code})"
                else:
                    message = f"HTTP {response.status_code}"
                
                self.log_test(f"API端點: {description}", success, message)
                if not success:
                    all_success = False
                    
            except Exception as e:
                self.log_test(f"API端點: {description}", False, str(e))
                all_success = False
        
        return all_success
    
    def test_static_assets(self):
        """測試靜態資源"""
        try:
            # 測試CSS檔案是否可存取
            css_response = self.session.get(f"{self.base_url}/static/css/style.css", timeout=10)
            css_ok = css_response.status_code == 200

            # 測試JS檔案是否可存取
            js_response = self.session.get(f"{self.base_url}/static/js/app.js", timeout=10)
            js_ok = js_response.status_code == 200

            # 檢查登入頁面是否包含Bootstrap相關內容
            login_response = self.session.get(f"{self.base_url}/login", timeout=10)
            has_bootstrap = "bootstrap" in login_response.text.lower()

            success = css_ok and js_ok and has_bootstrap

            if success:
                message = "靜態資源載入正常"
            else:
                issues = []
                if not css_ok:
                    issues.append("CSS檔案無法存取")
                if not js_ok:
                    issues.append("JS檔案無法存取")
                if not has_bootstrap:
                    issues.append("Bootstrap資源缺失")
                message = f"問題: {', '.join(issues)}"

            self.log_test("靜態資源檢查", success, message)
            return success

        except Exception as e:
            self.log_test("靜態資源檢查", False, str(e))
            return False
    
    def test_frontend_routing(self):
        """測試前端路由"""
        frontend_routes = [
            ("/login", "登入"),
            ("/dashboard", "主控台"),  # 需要認證，會重定向到登入
            ("/payee", "受款人"),      # 需要認證，會重定向到登入
        ]

        all_success = True
        for route, expected_content in frontend_routes:
            try:
                response = self.session.get(f"{self.base_url}{route}", timeout=10, allow_redirects=True)
                # 檢查是否返回正確的頁面或重定向到登入頁
                success = (response.status_code == 200 and
                          (expected_content in response.text or "登入" in response.text))

                if success:
                    if "登入" in response.text:
                        message = "正確重定向到登入頁面"
                    else:
                        message = "頁面載入正常"
                else:
                    message = f"HTTP {response.status_code} 或內容異常"

                self.log_test(f"前端路由: {route}", success, message)
                if not success:
                    all_success = False

            except Exception as e:
                self.log_test(f"前端路由: {route}", False, str(e))
                all_success = False

        return all_success
    
    def check_build_files(self):
        """檢查前端檔案"""
        frontend_path = Path("backend/app/frontend")

        if not frontend_path.exists():
            self.log_test("前端檔案檢查", False, "前端目錄不存在")
            return False

        required_paths = [
            "templates",
            "static",
            "routes.py"
        ]

        missing_paths = []
        for path_name in required_paths:
            path = frontend_path / path_name
            if not path.exists():
                missing_paths.append(path_name)

        if missing_paths:
            message = f"缺少檔案/目錄: {', '.join(missing_paths)}"
            self.log_test("前端檔案檢查", False, message)
            return False
        else:
            self.log_test("前端檔案檢查", True, "所有必要檔案存在")
            return True
    
    def run_all_tests(self):
        """執行所有測試"""
        print("🧪 CBA系統整合版測試")
        print("=" * 40)
        
        # 檢查前端檔案
        build_ok = self.check_build_files()
        if not build_ok:
            print("\n⚠️ 前端檔案缺失，請檢查FastAPI前端模組")
            return False
        
        # 等待服務啟動
        print("⏳ 等待服務啟動...")
        time.sleep(2)
        
        # 執行測試
        tests = [
            self.test_service_health,
            self.test_frontend_loading,
            self.test_api_docs,
            self.test_api_endpoints,
            self.test_static_assets,
            self.test_frontend_routing
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # 避免請求過於頻繁
        
        # 統計結果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 40)
        print(f"📊 測試結果統計:")
        print(f"   總測試數: {total_tests}")
        print(f"   通過: {passed_tests}")
        print(f"   失敗: {failed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 所有測試通過！系統運行正常")
            return True
        else:
            print(f"\n⚠️ 有 {failed_tests} 個測試失敗，請檢查系統狀態")
            return False

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CBA系統整合版測試")
    parser.add_argument("--url", default="http://localhost", 
                       help="服務URL (預設: http://localhost)")
    parser.add_argument("--wait", type=int, default=0,
                       help="測試前等待時間(秒)")
    
    args = parser.parse_args()
    
    if args.wait > 0:
        print(f"⏳ 等待 {args.wait} 秒後開始測試...")
        time.sleep(args.wait)
    
    tester = IntegratedSystemTester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
CBA系統整合版測試腳本
測試前後端整合服務的各項功能
"""

import requests
import time
import sys
import json
from pathlib import Path

class IntegratedSystemTester:
    def __init__(self, base_url="http://localhost"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """記錄測試結果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def test_service_health(self):
        """測試服務健康狀態"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            success = response.status_code == 200
            
            if success:
                data = response.json()
                message = f"服務正常 - {data.get('message', '')}"
            else:
                message = f"HTTP {response.status_code}"
            
            self.log_test("服務健康檢查", success, message)
            return success
        except Exception as e:
            self.log_test("服務健康檢查", False, str(e))
            return False
    
    def test_frontend_loading(self):
        """測試前端頁面載入"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            success = response.status_code == 200 and "CBA" in response.text
            
            if success:
                message = "前端頁面載入正常"
            else:
                message = f"HTTP {response.status_code} 或內容異常"
            
            self.log_test("前端頁面載入", success, message)
            return success
        except Exception as e:
            self.log_test("前端頁面載入", False, str(e))
            return False
    
    def test_api_docs(self):
        """測試API文檔"""
        try:
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            success = response.status_code == 200
            
            if success:
                message = "API文檔可存取"
            else:
                message = f"HTTP {response.status_code}"
            
            self.log_test("API文檔存取", success, message)
            return success
        except Exception as e:
            self.log_test("API文檔存取", False, str(e))
            return False
    
    def test_api_endpoints(self):
        """測試主要API端點"""
        endpoints = [
            ("/api/v1/auth/health", "認證服務健康檢查"),
            ("/api/v1/payee-data/", "受款人資料API"),
        ]
        
        all_success = True
        for endpoint, description in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                # 401是正常的，因為沒有認證
                success = response.status_code in [200, 401, 422]
                
                if success:
                    message = f"端點可存取 (HTTP {response.status_code})"
                else:
                    message = f"HTTP {response.status_code}"
                
                self.log_test(f"API端點: {description}", success, message)
                if not success:
                    all_success = False
                    
            except Exception as e:
                self.log_test(f"API端點: {description}", False, str(e))
                all_success = False
        
        return all_success
    
    def test_static_assets(self):
        """測試靜態資源"""
        try:
            # 測試CSS和JS檔案是否可存取
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code != 200:
                self.log_test("靜態資源檢查", False, "無法載入主頁面")
                return False
            
            # 檢查是否包含Vue.js相關內容
            content = response.text
            has_vue_content = any(keyword in content for keyword in [
                "vue", "Vue", "vite", "element-plus", "app"
            ])
            
            if has_vue_content:
                message = "靜態資源載入正常"
                success = True
            else:
                message = "可能缺少Vue.js資源"
                success = False
            
            self.log_test("靜態資源檢查", success, message)
            return success
            
        except Exception as e:
            self.log_test("靜態資源檢查", False, str(e))
            return False
    
    def test_spa_routing(self):
        """測試SPA路由"""
        spa_routes = [
            "/auth/login",
            "/dashboard", 
            "/payee",
            "/nonexistent-route"  # 應該也返回index.html
        ]
        
        all_success = True
        for route in spa_routes:
            try:
                response = self.session.get(f"{self.base_url}{route}", timeout=10)
                # SPA路由應該都返回200並包含Vue.js內容
                success = response.status_code == 200 and "CBA" in response.text
                
                if success:
                    message = "SPA路由正常"
                else:
                    message = f"HTTP {response.status_code} 或內容異常"
                
                self.log_test(f"SPA路由: {route}", success, message)
                if not success:
                    all_success = False
                    
            except Exception as e:
                self.log_test(f"SPA路由: {route}", False, str(e))
                all_success = False
        
        return all_success
    
    def check_build_files(self):
        """檢查建構檔案"""
        frontend_dist = Path("frontend-vue/dist")
        
        if not frontend_dist.exists():
            self.log_test("前端建構檔案", False, "dist目錄不存在")
            return False
        
        required_files = [
            "index.html",
            "assets"  # 資源目錄
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = frontend_dist / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            message = f"缺少檔案: {', '.join(missing_files)}"
            self.log_test("前端建構檔案", False, message)
            return False
        else:
            self.log_test("前端建構檔案", True, "所有必要檔案存在")
            return True
    
    def run_all_tests(self):
        """執行所有測試"""
        print("🧪 CBA系統整合版測試")
        print("=" * 40)
        
        # 檢查建構檔案
        build_ok = self.check_build_files()
        if not build_ok:
            print("\n⚠️ 前端未建構，請先執行:")
            print("cd frontend-vue && npm run build")
            return False
        
        # 等待服務啟動
        print("⏳ 等待服務啟動...")
        time.sleep(2)
        
        # 執行測試
        tests = [
            self.test_service_health,
            self.test_frontend_loading,
            self.test_api_docs,
            self.test_api_endpoints,
            self.test_static_assets,
            self.test_spa_routing
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # 避免請求過於頻繁
        
        # 統計結果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 40)
        print(f"📊 測試結果統計:")
        print(f"   總測試數: {total_tests}")
        print(f"   通過: {passed_tests}")
        print(f"   失敗: {failed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 所有測試通過！系統運行正常")
            return True
        else:
            print(f"\n⚠️ 有 {failed_tests} 個測試失敗，請檢查系統狀態")
            return False

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CBA系統整合版測試")
    parser.add_argument("--url", default="http://localhost", 
                       help="服務URL (預設: http://localhost)")
    parser.add_argument("--wait", type=int, default=0,
                       help="測試前等待時間(秒)")
    
    args = parser.parse_args()
    
    if args.wait > 0:
        print(f"⏳ 等待 {args.wait} 秒後開始測試...")
        time.sleep(args.wait)
    
    tester = IntegratedSystemTester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block content %}
<div class="login-container d-flex align-items-center justify-content-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="login-card p-5">
                    <!-- 登入標題區域 -->
                    <div class="login-header">
                        <div class="login-icon">
                            <i class="bi bi-building" aria-hidden="true"></i>
                        </div>
                        <h1 class="login-title">CBA系統</h1>
                        <p class="login-subtitle">受款人資料搜集系統</p>
                    </div>

                    <!-- 登入表單 -->
                    <form method="post" action="/login" novalidate aria-label="用戶登入表單">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" name="username"
                                   placeholder="用戶名" required autocomplete="username"
                                   aria-describedby="username-help">
                            <label for="username">
                                <i class="bi bi-person" aria-hidden="true"></i> 用戶名
                            </label>
                        </div>

                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="密碼" required autocomplete="current-password"
                                   aria-describedby="password-help">
                            <label for="password">
                                <i class="bi bi-lock" aria-hidden="true"></i> 密碼
                            </label>
                        </div>

                        <button type="submit" class="login-btn" aria-describedby="login-help">
                            <i class="bi bi-box-arrow-in-right" aria-hidden="true"></i>
                            <span>登入系統</span>
                        </button>
                    </form>

                    <!-- 測試帳號資訊 -->
                    <div class="test-accounts">
                        <h6>
                            <i class="bi bi-info-circle" aria-hidden="true"></i>
                            開發環境測試帳號
                        </h6>
                        <div class="account-info">
                            <strong>管理員：</strong>admin / admin123
                        </div>
                        <div class="account-info">
                            <strong>一般用戶：</strong>user / user123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.login-container {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    position: relative;
    z-index: 1;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.login-icon i {
    font-size: 2.5rem;
    color: white;
}

.login-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.login-subtitle {
    color: var(--text-muted);
    font-size: var(--font-size-base);
    font-weight: 400;
}

.form-floating {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-floating .form-control {
    height: 3.5rem;
    padding: 1rem 0.75rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast);
}

.form-floating .form-control:focus {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-floating label {
    color: var(--text-muted);
    font-weight: 500;
}

.login-btn {
    width: 100%;
    height: 3.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    border-radius: var(--radius-md);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.login-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
}

.test-accounts {
    background: rgba(37, 99, 235, 0.05);
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.test-accounts h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.test-accounts .account-info {
    background: white;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    border: 1px solid var(--gray-200);
}

.test-accounts .account-info:last-child {
    margin-bottom: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .login-card {
        margin: var(--spacing-md);
        padding: var(--spacing-xl) !important;
    }

    .login-icon {
        width: 60px;
        height: 60px;
    }

    .login-icon i {
        font-size: 2rem;
    }

    .login-title {
        font-size: var(--font-size-xl);
    }
}
</style>
{% endblock %}

{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block content %}
<div class="login-container d-flex align-items-center justify-content-center"
     style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); min-height: 100vh; width: 100vw; position: fixed; top: 0; left: 0; overflow: hidden; margin: 0; padding: 0;">

    <!-- 背景動畫 -->
    <div style="content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.05\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); animation: float 20s ease-in-out infinite;"></div>

    <div class="container" style="position: relative; z-index: 1; max-width: 100%; margin: 0; padding: 1rem;">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="login-card p-5"
                     style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 1.5rem; box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); position: relative; z-index: 1; transition: all 250ms ease-in-out;"
                     onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 25px 50px -12px rgba(0, 0, 0, 0.25)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)';"
                >
                    <!-- 登入標題區域 -->
                    <div class="login-header" style="text-align: center; margin-bottom: 2rem;">
                        <div class="login-icon"
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); animation: pulse 2s infinite;">
                            <i class="bi bi-building" aria-hidden="true" style="font-size: 2.5rem; color: white;"></i>
                        </div>
                        <h1 class="login-title" style="color: #0f172a; font-weight: 700; font-size: 1.5rem; margin-bottom: 0.5rem;">CBA系統</h1>
                        <p class="login-subtitle" style="color: #64748b; font-size: 1rem; font-weight: 400;">受款人資料搜集系統</p>
                    </div>

                    <!-- 登入表單 -->
                    <form method="post" action="/login" novalidate aria-label="用戶登入表單">
                        <div class="form-floating" style="position: relative; margin-bottom: 1.5rem;">
                            <input type="text" class="form-control" id="username" name="username"
                                   placeholder="用戶名" required autocomplete="username"
                                   aria-describedby="username-help"
                                   style="height: 3.5rem; padding: 1rem 0.75rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; background: rgba(255, 255, 255, 0.9); transition: all 150ms ease-in-out;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)';"
                                   onblur="this.style.borderColor='#e2e8f0'; this.style.background='rgba(255, 255, 255, 0.9)'; this.style.boxShadow='none';">
                            <label for="username" style="color: #64748b; font-weight: 500;">
                                <i class="bi bi-person" aria-hidden="true"></i> 用戶名
                            </label>
                        </div>

                        <div class="form-floating" style="position: relative; margin-bottom: 1.5rem;">
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="密碼" required autocomplete="current-password"
                                   aria-describedby="password-help"
                                   style="height: 3.5rem; padding: 1rem 0.75rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; background: rgba(255, 255, 255, 0.9); transition: all 150ms ease-in-out;"
                                   onfocus="this.style.borderColor='#2563eb'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(37, 99, 235, 0.1)';"
                                   onblur="this.style.borderColor='#e2e8f0'; this.style.background='rgba(255, 255, 255, 0.9)'; this.style.boxShadow='none';">
                            <label for="password" style="color: #64748b; font-weight: 500;">
                                <i class="bi bi-lock" aria-hidden="true"></i> 密碼
                            </label>
                        </div>

                        <button type="submit" class="login-btn" aria-describedby="login-help"
                                style="width: 100%; height: 3.5rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); border: none; border-radius: 0.75rem; color: white; font-weight: 600; font-size: 1rem; transition: all 150ms ease-in-out; position: relative; overflow: hidden; display: flex; align-items: center; justify-content: center; gap: 0.5rem;"
                                onmouseover="this.style.background='linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)';"
                                onmouseout="this.style.background='linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)'; this.style.transform='translateY(0)'; this.style.boxShadow='none';"
                                onmousedown="this.style.transform='translateY(0)';">
                            <i class="bi bi-box-arrow-in-right" aria-hidden="true"></i>
                            <span>登入系統</span>
                        </button>
                    </form>

                    <!-- 測試帳號資訊 -->
                    <div class="test-accounts"
                         style="background: rgba(37, 99, 235, 0.05); border: 1px solid rgba(37, 99, 235, 0.1); border-radius: 0.75rem; padding: 1.5rem; margin-top: 2rem;">
                        <h6 style="color: #2563eb; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.25rem;">
                            <i class="bi bi-info-circle" aria-hidden="true"></i>
                            開發環境測試帳號
                        </h6>
                        <div class="account-info"
                             style="background: white; border-radius: 0.375rem; padding: 0.5rem 1rem; margin-bottom: 0.25rem; font-family: 'Courier New', monospace; font-size: 0.875rem; border: 1px solid #e2e8f0;">
                            <strong>管理員：</strong>admin / admin123
                        </div>
                        <div class="account-info"
                             style="background: white; border-radius: 0.375rem; padding: 0.5rem 1rem; margin-bottom: 0; font-family: 'Courier New', monospace; font-size: 0.875rem; border: 1px solid #e2e8f0;">
                            <strong>一般用戶：</strong>user / user123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 重置樣式 - 移除所有默認間距和空白 */
html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    position: relative !important;
}

/* 移除Bootstrap容器的默認樣式 */
.container, .container-fluid {
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* 移除Bootstrap行和列的默認間距 */
.row {
    margin: 0 !important;
    padding: 0 !important;
}

.col, .col-md-6, .col-lg-5, .col-xl-4 {
    padding: 0 !important;
    margin: 0 !important;
}

/* 動畫效果 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 強制應用登入頁面樣式 - 最高優先級 */
html body main .login-container {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    min-height: 100vh !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

body .login-container::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    animation: float 20s ease-in-out infinite !important;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

body .login-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1.5rem !important;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
    transition: all 250ms ease-in-out !important;
    position: relative !important;
    z-index: 1 !important;
}

body .login-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

body .login-icon {
    width: 80px !important;
    height: 80px !important;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 1.5rem !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
    animation: pulse 2s infinite !important;
}

.login-icon i {
    font-size: 2.5rem;
    color: white;
}

.login-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.login-subtitle {
    color: var(--text-muted);
    font-size: var(--font-size-base);
    font-weight: 400;
}

.form-floating {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-floating .form-control {
    height: 3.5rem;
    padding: 1rem 0.75rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast);
}

.form-floating .form-control:focus {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-floating label {
    color: var(--text-muted);
    font-weight: 500;
}

.login-btn {
    width: 100%;
    height: 3.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    border-radius: var(--radius-md);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.login-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
}

.test-accounts {
    background: rgba(37, 99, 235, 0.05);
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.test-accounts h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.test-accounts .account-info {
    background: white;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    border: 1px solid var(--gray-200);
}

.test-accounts .account-info:last-child {
    margin-bottom: 0;
}

/* 調試樣式 - 確認CSS載入 */
html body {
    font-family: 'Inter', sans-serif !important;
}

/* 響應式設計 */
@media (max-width: 768px) {
    html body main .login-card {
        margin: 1rem !important;
        padding: 2rem !important;
    }

    html body main .login-icon {
        width: 60px !important;
        height: 60px !important;
    }

    html body main .login-icon i {
        font-size: 2rem !important;
    }

    html body main .login-title {
        font-size: 1.25rem !important;
    }
}

/* 確保樣式被應用的調試規則 */
.login-container {
    border: 3px solid red !important;
}
</style>
{% endblock %}

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from typing import List, Dict
from sqlalchemy.orm import Session

from app.models.user import Department
from app.models.database import get_db
from app.schemas.department import (
    DepartmentCreate,
    DepartmentUpdate,
    DepartmentResponse
)
from app.core.dependencies import get_current_active_user, get_client_info
from app.utils.audit import log_operation

router = APIRouter(prefix="/departments", tags=["部門管理"])

@router.get("/", response_model=List[DepartmentResponse])
async def get_departments(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """取得所有部門列表"""
    # 僅管理者可操作
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢部門列表")
    departments = db.query(Department).all()
    return departments

@router.post("/", response_model=DepartmentResponse)
async def create_department(
    data: DepartmentCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """新增部門"""
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可新增部門")
    # 重複檢查
    if db.query(Department).filter(Department.name == data.name).first():
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="部門名稱已存在")
    if db.query(Department).filter(Department.code == data.code).first():
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="部門代碼已存在")
    dept = Department(code=data.code, name=data.name, description=data.description, is_active=True)
    db.add(dept)
    db.commit()
    db.refresh(dept)
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="CREATE_DEPARTMENT",
        table_name="departments",
        new_values={"department_id": dept.id, "name": data.name},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return dept

@router.get("/{department_id}", response_model=DepartmentResponse)
async def get_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """取得特定部門"""
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可查詢部門")
    dept = db.query(Department).filter(Department.id == department_id).first()
    if not dept:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部門不存在")
    return dept

@router.put("/{department_id}", response_model=DepartmentResponse)
async def update_department(
    department_id: int,
    data: DepartmentUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """更新部門資訊"""
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可更新部門")
    dept = db.query(Department).filter(Department.id == department_id).first()
    if not dept:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部門不存在")
    # 更新欄位
    if data.code is not None:
        if db.query(Department).filter(Department.code == data.code, Department.id != department_id).first():
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="部門代碼已存在")
        dept.code = data.code
    if data.name is not None:
        dept.name = data.name
    if data.description is not None:
        dept.description = data.description
    if data.is_active is not None:
        dept.is_active = data.is_active
    db.commit()
    db.refresh(dept)
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="UPDATE_DEPARTMENT",
        table_name="departments",
        new_values={"department_id": dept.id},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return dept

@router.delete("/{department_id}", response_model=Dict[str, str])
async def delete_department(
    department_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """刪除部門（軟刪除）"""
    if not current_user.has_role("admin"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="僅管理者可刪除部門")
    dept = db.query(Department).filter(Department.id == department_id).first()
    if not dept:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部門不存在")
    dept.is_active = False
    db.commit()
    # 審計日誌
    client_info = get_client_info(request)
    log_operation(
        db=db,
        user_id=current_user.id,
        action="DELETE_DEPARTMENT",
        table_name="departments",
        new_values={"department_id": dept.id},
        ip_address=client_info.get("ip_address"),
        user_agent=client_info.get("user_agent")
    )
    return {"message": "部門已刪除"} 
#!/usr/bin/env python3
"""
測試登入功能
"""

import requests
import sys

def test_login():
    """測試登入功能"""
    base_url = "http://localhost"
    
    # 創建session以保持cookie
    session = requests.Session()
    
    print("🧪 測試CBA系統登入功能")
    print("=" * 40)
    
    # 1. 測試登入頁面
    print("1. 測試登入頁面...")
    try:
        response = session.get(f"{base_url}/login")
        if response.status_code == 200:
            print("✅ 登入頁面載入成功")
        else:
            print(f"❌ 登入頁面載入失敗: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登入頁面載入失敗: {e}")
        return False
    
    # 2. 測試登入提交
    print("2. 測試登入提交...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ 登入成功，重定向到儀表板")
            
            # 檢查是否設定了cookie
            if 'access_token' in session.cookies:
                print("✅ 認證Cookie已設定")
            else:
                print("⚠️ 未找到認證Cookie")
                
        else:
            print(f"❌ 登入失敗: HTTP {response.status_code}")
            print(f"回應內容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 登入提交失敗: {e}")
        return False
    
    # 3. 測試存取受保護的頁面
    print("3. 測試存取儀表板...")
    try:
        response = session.get(f"{base_url}/dashboard")
        
        if response.status_code == 200:
            print("✅ 儀表板存取成功")
            
            # 檢查是否包含預期內容
            if "主控台" in response.text or "dashboard" in response.text.lower():
                print("✅ 儀表板內容正確")
            else:
                print("⚠️ 儀表板內容可能不完整")
                
        else:
            print(f"❌ 儀表板存取失敗: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 儀表板存取失敗: {e}")
        return False
    
    # 4. 測試存取受款人頁面
    print("4. 測試存取受款人頁面...")
    try:
        response = session.get(f"{base_url}/payee")
        
        if response.status_code == 200:
            print("✅ 受款人頁面存取成功")
            
            # 檢查是否包含預期內容
            if "受款人" in response.text:
                print("✅ 受款人頁面內容正確")
            else:
                print("⚠️ 受款人頁面內容可能不完整")
                
        else:
            print(f"❌ 受款人頁面存取失敗: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 受款人頁面存取失敗: {e}")
        return False
    
    # 5. 測試登出
    print("5. 測試登出...")
    try:
        response = session.get(f"{base_url}/logout", allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ 登出成功，重定向到登入頁面")
            
            # 檢查cookie是否被清除
            if 'access_token' not in session.cookies:
                print("✅ 認證Cookie已清除")
            else:
                print("⚠️ 認證Cookie未清除")
                
        else:
            print(f"❌ 登出失敗: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登出失敗: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 所有登入測試通過！")
    print("\n📋 使用說明:")
    print("1. 開啟瀏覽器存取: http://localhost")
    print("2. 使用測試帳號登入:")
    print("   - 管理員: admin / admin123")
    print("   - 一般用戶: user / user123")
    print("3. 登入後即可使用所有功能")
    
    return True

def main():
    """主函數"""
    try:
        success = test_login()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n測試被中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n測試過程中發生錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

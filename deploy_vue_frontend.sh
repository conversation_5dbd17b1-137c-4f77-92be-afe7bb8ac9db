#!/bin/bash

# CBA系統 - Vue.js前端部署腳本

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查Node.js和npm
check_dependencies() {
    log_info "檢查依賴..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安裝，請先安裝Node.js 18或更高版本"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安裝，請先安裝npm"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本過低，需要18或更高版本"
        exit 1
    fi
    
    log_success "依賴檢查通過"
}

# 安裝依賴
install_dependencies() {
    log_info "安裝前端依賴..."
    
    cd frontend-vue
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_success "前端依賴安裝完成"
}

# 建構前端
build_frontend() {
    log_info "建構Vue.js前端..."
    
    cd frontend-vue
    
    # 設定生產環境
    export NODE_ENV=production
    
    # 執行建構
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "前端建構失敗，dist目錄不存在"
        exit 1
    fi
    
    log_success "前端建構完成"
}

# 部署到nginx
deploy_to_nginx() {
    log_info "部署到nginx..."
    
    # 預設nginx網站根目錄
    NGINX_ROOT="/var/www/html"
    
    # 如果是Ubuntu/Debian
    if [ -d "/var/www/html" ]; then
        NGINX_ROOT="/var/www/html"
    # 如果是CentOS/RHEL
    elif [ -d "/usr/share/nginx/html" ]; then
        NGINX_ROOT="/usr/share/nginx/html"
    fi
    
    # 備份現有檔案
    if [ -d "$NGINX_ROOT/cba-system" ]; then
        log_info "備份現有檔案..."
        sudo mv "$NGINX_ROOT/cba-system" "$NGINX_ROOT/cba-system.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 複製建構檔案
    sudo mkdir -p "$NGINX_ROOT/cba-system"
    sudo cp -r frontend-vue/dist/* "$NGINX_ROOT/cba-system/"
    
    # 設定權限
    sudo chown -R www-data:www-data "$NGINX_ROOT/cba-system" 2>/dev/null || \
    sudo chown -R nginx:nginx "$NGINX_ROOT/cba-system" 2>/dev/null || \
    sudo chown -R apache:apache "$NGINX_ROOT/cba-system" 2>/dev/null || \
    log_warning "無法設定檔案權限，請手動檢查"
    
    log_success "檔案部署完成"
}

# 配置nginx
configure_nginx() {
    log_info "配置nginx..."
    
    # 檢查nginx配置檔案是否存在
    if [ -f "nginx-vue-integrated.conf" ]; then
        # 複製nginx配置
        if [ -d "/etc/nginx/sites-available" ]; then
            # Ubuntu/Debian
            sudo cp nginx-vue-integrated.conf /etc/nginx/sites-available/cba-system
            sudo ln -sf /etc/nginx/sites-available/cba-system /etc/nginx/sites-enabled/
            sudo rm -f /etc/nginx/sites-enabled/default
        elif [ -d "/etc/nginx/conf.d" ]; then
            # CentOS/RHEL
            sudo cp nginx-vue-integrated.conf /etc/nginx/conf.d/cba-system.conf
        fi
        
        # 測試nginx配置
        if sudo nginx -t; then
            log_success "nginx配置有效"
            
            # 重新載入nginx
            sudo systemctl reload nginx
            log_success "nginx已重新載入"
        else
            log_error "nginx配置無效"
            exit 1
        fi
    else
        log_warning "nginx配置檔案不存在，請手動配置"
    fi
}

# 啟動後端服務
start_backend() {
    log_info "啟動後端服務..."
    
    # 檢查後端是否已在運行
    if pgrep -f "uvicorn.*main:app" > /dev/null; then
        log_info "後端服務已在運行"
    else
        # 啟動後端
        cd backend
        nohup uv run uvicorn main:app --host 0.0.0.0 --port 8080 > ../backend.log 2>&1 &
        sleep 3
        
        if pgrep -f "uvicorn.*main:app" > /dev/null; then
            log_success "後端服務已啟動"
        else
            log_error "後端服務啟動失敗"
            exit 1
        fi
    fi
}

# 驗證部署
verify_deployment() {
    log_info "驗證部署..."
    
    # 檢查後端健康狀態
    if curl -f -s http://localhost:8080/health > /dev/null; then
        log_success "後端API正常"
    else
        log_error "後端API無法存取"
        exit 1
    fi
    
    # 檢查nginx狀態
    if systemctl is-active --quiet nginx; then
        log_success "nginx服務正常"
    else
        log_error "nginx服務異常"
        exit 1
    fi
    
    log_success "部署驗證通過"
}

# 主函數
main() {
    echo "🚀 CBA系統 - Vue.js前端部署"
    echo "============================="
    
    # 檢查是否為root用戶
    if [ "$EUID" -eq 0 ]; then
        log_warning "不建議使用root用戶執行此腳本"
    fi
    
    # 執行部署步驟
    check_dependencies
    install_dependencies
    build_frontend
    
    # 需要sudo權限的操作
    echo ""
    log_info "接下來需要sudo權限進行系統配置..."
    
    deploy_to_nginx
    configure_nginx
    start_backend
    verify_deployment
    
    echo ""
    log_success "🎉 Vue.js前端部署完成！"
    echo ""
    echo "📋 存取資訊:"
    echo "  • 前端應用: http://localhost/"
    echo "  • 後端API: http://localhost:8080"
    echo "  • API文檔: http://localhost:8080/docs"
    echo ""
    echo "📁 檔案位置:"
    echo "  • 前端檔案: $NGINX_ROOT/cba-system/"
    echo "  • 後端日誌: $(pwd)/backend.log"
    echo ""
    echo "🔧 管理命令:"
    echo "  • 重新載入nginx: sudo systemctl reload nginx"
    echo "  • 檢查後端狀態: curl http://localhost:8080/health"
    echo "  • 查看後端日誌: tail -f $(pwd)/backend.log"
}

# 執行主函數
main "$@"

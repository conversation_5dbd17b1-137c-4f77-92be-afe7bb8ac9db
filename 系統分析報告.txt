================================================================================
                           CBA個人資料搜集系統
                             系統分析報告
================================================================================

專案資訊
--------
專案名稱：CBA個人資料搜集系統
建立日期：2024年12月19日
分析方法：WOOP方法論 + Sequential Thinking
技術限制：Python + FastAPI + Streamlit + SQLite3 + JWT Bearer認證

================================================================================
一、專案概述
================================================================================

1.1 專案目標
-----------
建立安全、可靠的個人資料收集與管理系統，具備完整的權限管控機制，
確保個人資料的安全性和系統的可靠性。

1.2 核心功能
-----------
• 個人資料收集（姓名、身分證字號、地址、備註）
• 三級權限管控（一般帳號、全域帳號、管理者帳號）
• 資料查詢與管理
• 系統管理與用戶管理
• 單一簽入整合
• 資料安全防護

1.3 主要特色
-----------
• 身分證字號加密保護
• 基於角色的存取控制(RBAC)
• 完整的審計日誌
• 直觀的用戶介面
• 強化的安全機制

================================================================================
二、需求分析（WOOP方法論）
================================================================================

2.1 願望(Wish)
-------------
建立具備完整權限管控的個人資料搜集系統，提供安全、高效的資料管理功能。

2.2 結果(Outcome)
----------------
完成安全的個人資料搜集系統，具備：
• 三級權限管控機制
• 資料驗證與重複檢查
• 單一簽入整合
• 身分證字號加密防護
• 完整的操作審計

2.3 障礙(Obstacle)
-----------------
• 身分證字號機敏資料保護的複雜性
• 三級權限管控機制的設計與實現
• 單一簽入系統整合的技術難度
• 資料驗證機制的完整性要求
• 系統安全性與使用性的平衡

2.4 計畫(Plan)
-------------
採用分階段開發策略：
• 第一階段：基礎架構與認證系統
• 第二階段：核心功能與權限管控
• 第三階段：前端介面與用戶體驗
• 第四階段：安全加固與效能優化

================================================================================
三、權限架構設計
================================================================================

3.1 三級權限設計
---------------

3.1.1 一般帳號 (General User)
• 權限範圍：
  - 新增個人資料
  - 查詢個人新增的資料
  - 修改個人新增的資料
  - 刪除個人新增的資料

3.1.2 全域帳號 (Global User)
• 權限範圍：
  - 包含一般帳號所有權限
  - 查詢所有人新增的資料
  - 檢視全域統計資訊
  - 匯出查詢結果

3.1.3 管理者帳號 (Administrator)
• 權限範圍：
  - 包含全域帳號所有權限
  - 設定管理者帳號
  - 設定全域帳號
  - 用戶管理（啟用/停用）
  - 系統配置管理
  - 審計日誌檢視

3.2 權限控制機制
---------------
• 基於角色的存取控制(RBAC)
• API層級權限檢查
• 資料查詢範圍限制
• 操作日誌記錄

================================================================================
四、資料模型設計
================================================================================

4.1 核心資料表結構
-----------------

4.1.1 個人資料表 (personal_data)
CREATE TABLE personal_data (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    id_number_encrypted TEXT NOT NULL UNIQUE, -- 加密後的身分證字號
    address TEXT,
    notes TEXT,
    created_by INTEGER NOT NULL,
    created_by_department INTEGER NOT NULL, -- 建立者所屬部門
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (created_by_department) REFERENCES departments(id)
);

4.1.2 部門表 (departments)
CREATE TABLE departments (
    id INTEGER PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

4.1.3 用戶表 (users)
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    department_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (department_id) REFERENCES departments(id)
);

4.1.4 角色表 (roles)
CREATE TABLE roles (
    id INTEGER PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT -- JSON格式存儲權限清單
);

4.1.5 用戶角色關聯表 (user_roles)
CREATE TABLE user_roles (
    user_id INTEGER,
    role_id INTEGER,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

4.1.6 審計日誌表 (audit_log)
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY,
    user_id INTEGER,
    action TEXT NOT NULL,
    table_name TEXT,
    record_id INTEGER,
    old_values TEXT, -- JSON格式
    new_values TEXT, -- JSON格式
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

4.2 資料關係設計
---------------
• 用戶與角色：多對多關係
• 用戶與個人資料：一對多關係
• 角色與權限：一對多關係
• 操作與審計日誌：一對一關係

================================================================================
五、系統架構設計
================================================================================

5.1 四層架構設計
---------------

5.1.1 前端層 (Presentation Layer)
• 框架：Streamlit
• 認證：基於session_state和JWT token
• 主要頁面：
  - 登入頁面 (login.py)
  - 資料輸入頁面 (data_input.py)
  - 資料查詢頁面 (data_query.py)
  - 權限管理頁面 (admin.py)
• 主要組件：
  - 用戶認證組件
  - 資料表單組件
  - 權限檢查組件
  - 資料顯示組件

5.1.2 後端層 (Application Layer)
• 框架：FastAPI
• 認證：JWT Bearer Token
• 主要模組：
  - 認證模組 (auth.py)
  - 個人資料管理模組 (personal_data.py)
  - 用戶管理模組 (users.py)
  - 權限管理模組 (permissions.py)
• 中介軟體：
  - JWT認證中介軟體
  - 權限檢查中介軟體
  - 審計日誌中介軟體
  - CORS中介軟體

5.1.3 資料層 (Data Layer)
• 主資料庫：SQLite3
• 加密服務：AES-256-GCM
• 快取服務：記憶體快取（用戶會話）

5.1.4 安全層 (Security Layer)
• SSO整合：基於現有SOAP服務
• 資料加密：身分證字號加密
• 輸入驗證：格式驗證與安全檢查
• 權限控制：基於角色的存取控制(RBAC)

5.2 系統部署架構
---------------

5.2.1 開發環境
• 本地開發伺服器
• SQLite3本地資料庫
• 環境變數管理
• 開發工具整合

5.2.2 生產環境
• FastAPI後端服務
• Streamlit前端服務
• 反向代理(Nginx)
• SSL/TLS加密
• 負載均衡（可選）

================================================================================
六、安全機制設計
================================================================================

6.1 身分證字號保護機制
---------------------

6.1.1 加密規範詳細說明
• 加密演算法：AES-256-GCM（進階加密標準-伽羅瓦計數器模式）
  - 對稱加密，密鑰長度256位元
  - GCM模式提供認證加密，同時確保機密性和完整性
  - 抗量子運算攻擊能力強

• 密鑰管理機制：
  - 主密鑰：32字節隨機產生，存儲於環境變數中
  - 密鑰衍生：使用PBKDF2-SHA256從主密鑰衍生實際加密密鑰
  - 密鑰輪換：每3個月更換一次主密鑰
  - 密鑰備份：加密後存儲於安全位置

• 初始化向量(IV)機制：
  - IV長度：96位元（12字節）
  - 生成方式：每次加密時使用os.urandom()產生隨機IV
  - 唯一性保證：確保相同明文產生不同密文
  - 存儲方式：IV與密文一同存儲，前12字節為IV，後續為加密內容
  - 安全考量：IV無需保密但必須唯一，防止重放攻擊

• 加密流程實作：
  1. 產生隨機IV（12字節）
  2. 使用AES-256-GCM加密身分證字號
  3. 獲得密文和認證標籤
  4. 將IV+密文+標籤組合存儲
  5. 清除記憶體中的明文

• 解密流程實作：
  1. 從存儲數據中提取IV（前12字節）
  2. 提取密文和認證標籤
  3. 使用相同密鑰和IV進行解密
  4. 驗證認證標籤確保完整性
  5. 返回明文並立即清除記憶體

6.1.2 存取控制詳細機制
• 加密層級控制：
  - 資料庫存儲：僅存儲加密後的密文
  - 應用程式層：解密僅在必要時進行
  - 記憶體保護：明文使用後立即清零
  - 日誌保護：絕不記錄明文身分證字號

• 解密權限管理：
  - 一般帳號：無解密權限，僅能查看遮蔽顯示（如：A12******78）
  - 全域帳號：有限解密權限，可查看完整資料但有操作日誌
  - 管理者帳號：完整解密權限，所有操作均記錄審計日誌
  - 系統帳號：僅系統內部比對時可解密

• 傳輸保護機制：
  - API傳輸：強制使用HTTPS/TLS 1.3
  - 前端顯示：敏感資料遮蔽顯示
  - 資料匯出：加密或遮蔽處理
  - 備份傳輸：額外加密保護

• 記憶體安全管理：
  - 變數清零：使用後立即清除敏感變數
  - 垃圾回收：強制記憶體回收
  - 記憶體轉儲：防止敏感資料出現在記憶體轉儲中
  - 交換檔案：禁用包含敏感資料的記憶體交換

6.1.3 安全監控與稽核
• 加密操作監控：
  - 加密/解密次數統計
  - 失敗操作警報
  - 異常存取模式偵測
  - 密鑰使用頻率監控

• 存取稽核機制：
  - 完整操作日誌：誰、何時、為何存取
  - 批次操作監控：大量資料存取警報
  - 異地存取偵測：異常IP或位置警報
  - 權限變更追蹤：角色權限變更記錄

• 合規性檢查：
  - 個資法合規：符合台灣個人資料保護法
  - 安全標準：遵循ISO 27001標準
  - 定期稽核：每季度安全檢查
  - 滲透測試：每年度委外測試

6.2 輸入驗證機制
---------------

6.2.1 身分證字號驗證
• 格式驗證：台灣身分證字號格式
• 檢查碼驗證：計算驗證碼正確性
• 重複檢查：資料庫唯一性驗證
• 即時驗證：前端即時回饋

6.2.2 其他輸入驗證
• 資料長度限制
• 特殊字元過濾
• SQL注入防護
• XSS攻擊防護

6.3 認證與授權
-------------

6.3.1 JWT Token管理
• Token生成：包含用戶ID、角色、權限
• Token驗證：每次API請求驗證
• Token過期：設定合理過期時間
• Token撤銷：支援強制登出

6.3.2 SSO整合
• 基於現有SOAP服務
• 用戶資訊同步
• 角色權限映射
• 失效處理機制

================================================================================
七、前端介面設計
================================================================================

7.1 設計原則
-----------
• 簡潔現代的視覺風格
• 直觀的操作流程
• 響應式設計
• 無障礙設計考量

7.2 色彩方案
-----------
• 主色：深藍色 (#1f4788)
• 次要色：淺藍色 (#e3f2fd)
• 強調色：橙色 (#ff9800)
• 警告色：紅色 (#f44336)
• 成功色：綠色 (#4caf50)
• 中性色：灰色系列

7.3 主要頁面設計
---------------

7.3.1 登入頁面
• 系統標題與Logo
• SSO登入按鈕
• 登入狀態顯示
• 錯誤訊息提示
• 登入流程指引

7.3.2 主頁面
• 頂部導航欄（用戶資訊、登出）
• 側邊欄導航選單
• 主要內容區域
• 狀態訊息區域
• 操作按鈕區域

7.3.3 資料輸入頁面
• 結構化表單設計
• 即時驗證提示
• 必填欄位標示
• 提交確認機制
• 成功/失敗回饋

7.3.4 資料查詢頁面
• 搜尋條件設定
• 結果列表顯示
• 分頁導航
• 排序與篩選
• 匯出功能

7.3.5 權限管理頁面（管理者限定）
• 用戶列表管理
• 角色分配介面
• 權限設定功能
• 批次操作支援
• 操作確認對話框

7.4 用戶體驗設計
---------------
• 載入狀態指示器
• 即時錯誤提示
• 操作成功確認
• 鍵盤快捷鍵支援
• 操作歷史記錄

================================================================================
八、API介面設計
================================================================================

8.1 認證相關API
---------------

8.1.1 單一簽入登入
POST /api/auth/sso_login
• 請求參數：ssoToken1
• 回應格式：JWT token + 用戶資訊
• 錯誤處理：登入失敗、Token無效

8.1.2 Token驗證
GET /api/auth/verify
• 請求標頭：Authorization: Bearer <token>
• 回應格式：用戶資訊 + 權限清單
• 錯誤處理：Token過期、無效

8.1.3 登出
POST /api/auth/logout
• 請求標頭：Authorization: Bearer <token>
• 回應格式：成功訊息
• 功能：Token撤銷、會話清除

8.2 個人資料管理API
------------------

8.2.1 新增個人資料
POST /api/personal-data
• 請求標頭：Authorization: Bearer <token>
• 請求格式：{name, id_number, address, notes}
• 回應格式：新增成功的資料記錄
• 驗證：身分證字號格式、重複檢查

8.2.2 查詢個人資料
GET /api/personal-data
• 請求標頭：Authorization: Bearer <token>
• 查詢參數：page, limit, search, sort
• 回應格式：分頁資料清單
• 權限控制：基於用戶角色限制查詢範圍

8.2.3 取得特定資料
GET /api/personal-data/{id}
• 請求標頭：Authorization: Bearer <token>
• 路徑參數：資料ID
• 回應格式：特定資料記錄
• 權限檢查：資料擁有者或管理者

8.2.4 更新個人資料
PUT /api/personal-data/{id}
• 請求標頭：Authorization: Bearer <token>
• 請求格式：{name, id_number, address, notes}
• 回應格式：更新後的資料記錄
• 權限檢查：資料擁有者或管理者

8.2.5 刪除個人資料
DELETE /api/personal-data/{id}
• 請求標頭：Authorization: Bearer <token>
• 路徑參數：資料ID
• 回應格式：刪除成功訊息
• 權限檢查：資料擁有者或管理者

8.3 用戶管理API（管理者限定）
---------------------------

8.3.1 取得所有用戶
GET /api/users
• 請求標頭：Authorization: Bearer <token>
• 查詢參數：page, limit, role, status
• 回應格式：用戶清單
• 權限要求：管理者角色

8.3.2 更新用戶角色
PUT /api/users/{id}/role
• 請求標頭：Authorization: Bearer <token>
• 請求格式：{role_id}
• 回應格式：更新後的用戶資訊
• 權限要求：管理者角色

8.3.3 啟用/停用用戶
PUT /api/users/{id}/status
• 請求標頭：Authorization: Bearer <token>
• 請求格式：{is_active}
• 回應格式：更新後的用戶資訊
• 權限要求：管理者角色

8.4 資料驗證API
---------------

8.4.1 驗證身分證字號
POST /api/validate/id-number
• 請求標頭：Authorization: Bearer <token>
• 請求格式：{id_number}
• 回應格式：{is_valid, is_duplicate, message}
• 驗證內容：格式、重複性

8.4.2 批次驗證
POST /api/validate/batch
• 請求標頭：Authorization: Bearer <token>
• 請求格式：[{name, id_number, address, notes}]
• 回應格式：[{is_valid, errors}]
• 驗證內容：全部資料欄位

8.5 API安全設計
---------------

8.5.1 認證機制
• 所有API需要JWT Bearer token
• Token包含用戶ID、角色、權限
• Token有效期限制
• 自動更新機制

8.5.2 權限控制
• 基於角色的存取控制
• API層級權限檢查
• 資料範圍限制
• 操作日誌記錄

8.5.3 輸入驗證
• 請求資料格式驗證
• 參數類型檢查
• 長度限制
• 特殊字元過濾

8.5.4 錯誤處理
• 統一錯誤回應格式
• 適當的HTTP狀態碼
• 詳細錯誤訊息
• 安全錯誤資訊

================================================================================
九、技術堆疊
================================================================================

9.1 前端技術
-----------
• 主框架：Streamlit
• UI組件：streamlit-chat、自定義組件
• 樣式：自定義CSS、響應式設計
• 狀態管理：st.session_state
• 資料視覺化：Streamlit內建圖表

9.2 後端技術
-----------
• Web框架：FastAPI
• 資料庫ORM：SQLAlchemy
• 認證：python-jose (JWT)
• 加密：cryptography
• 密碼雜湊：passlib[bcrypt]
• 資料驗證：Pydantic
• 異步處理：asyncio

9.3 資料庫技術
-------------
• 主資料庫：SQLite3
• 資料庫遷移：Alembic
• 連線池：SQLAlchemy連線池

9.4 安全技術
-----------
• 加密：AES-256-GCM
• 雜湊：bcrypt
• JWT：RS256或HS256
• HTTPS：SSL/TLS

9.5 開發工具
-----------
• 套件管理：uv
• 測試框架：pytest
• 程式碼格式：black、isort
• 程式碼檢查：flake8、mypy
• 文件生成：Sphinx
• 版本控制：Git
• 環境管理：python-dotenv

9.6 部署工具
-----------
• 容器化：Docker
• 程序管理：Supervisor
• 監控：Prometheus + Grafana（可選）
• 日誌：ELK Stack（可選）

================================================================================
十、開發規劃
================================================================================

10.1 階段規劃
------------

10.1.1 第一階段：基礎架構（2-3週）
• 專案架構建立
• 資料庫設計與建立
• 基本認證系統
• SSO整合
• 核心工具類別

10.1.2 第二階段：核心功能（3-4週）
• 個人資料管理API
• 權限管控系統
• 資料驗證機制
• 加密解密功能
• 審計日誌系統

10.1.3 第三階段：前端介面（2-3週）
• Streamlit應用架構
• 用戶介面設計
• 表單驗證
• 資料顯示
• 用戶體驗優化

10.1.4 第四階段：整合測試（2-3週）
• 系統整合測試
• 安全測試
• 效能測試
• 用戶接受測試
• 部署準備

10.2 開發順序建議
----------------
1. 建立基礎架構和資料模型
2. 實現認證和權限系統
3. 開發核心功能和API
4. 建立前端介面
5. 進行安全測試和優化
6. 系統整合和部署

10.3 里程碑設定
--------------
• 里程碑1：認證系統完成
• 里程碑2：資料管理功能完成
• 里程碑3：前端介面完成
• 里程碑4：系統測試完成
• 里程碑5：正式上線

================================================================================
十一、風險評估
================================================================================

11.1 技術風險
------------

11.1.1 高風險項目
• 身分證字號加密機制複雜性
• SSO系統整合難度
• 效能優化挑戰

11.1.2 中風險項目
• 前端用戶體驗設計
• 資料庫效能調優
• 第三方套件相依性

11.1.3 低風險項目
• 基本CRUD功能實現
• 簡單權限檢查
• 基礎日誌記錄

11.2 安全風險
------------

11.2.1 高風險項目
• 身分證字號資料外洩
• 權限控制漏洞
• 加密密鑰管理

11.2.2 中風險項目
• SQL注入攻擊
• XSS攻擊
• 會話劫持

11.2.3 低風險項目
• 基本輸入驗證
• 簡單存取控制
• 日誌記錄

11.3 營運風險
------------

11.3.1 高風險項目
• 系統可用性
• 資料備份恢復
• 用戶訓練需求

11.3.2 中風險項目
• 系統維護複雜度
• 功能擴展需求
• 用戶支援負擔

11.3.3 低風險項目
• 日常操作
• 基本監控
• 簡單故障排除

11.4 風險緩解策略
----------------

11.4.1 技術風險緩解
• 分階段開發降低複雜度
• 充分的原型驗證
• 定期技術審查
• 備用方案準備

11.4.2 安全風險緩解
• 多層防護機制
• 定期安全審計
• 滲透測試
• 安全訓練

11.4.3 營運風險緩解
• 完整的備份策略
• 災難恢復計畫
• 用戶文件準備
• 支援流程建立

================================================================================
十二、成功指標
================================================================================

12.1 功能指標
------------
• 所有需求功能正常運作（100%）
• 資料輸入驗證正確率（99.9%）
• 權限控制準確率（100%）
• 資料查詢回應時間（<2秒）

12.2 安全指標
------------
• 通過安全測試（無重大漏洞）
• 身分證字號加密率（100%）
• 未授權存取阻止率（100%）
• 操作審計記錄完整性（100%）

12.3 效能指標
------------
• 系統回應時間（平均<1秒）
• 同時用戶支援（>50人）
• 系統可用性（>99%）
• 資料庫查詢效率（<500ms）

12.4 用戶體驗指標
----------------
• 用戶滿意度（>85%）
• 操作錯誤率（<5%）
• 學習曲線（<30分鐘）
• 介面回應速度（<500ms）

12.5 維護指標
------------
• 平均故障修復時間（<4小時）
• 程式碼覆蓋率（>80%）
• 文件完整性（100%）
• 系統監控覆蓋率（>95%）

================================================================================
十三、結論與建議
================================================================================

13.1 系統分析總結
----------------
本系統分析報告完整涵蓋了CBA個人資料搜集系統的所有需求和設計規範。
通過WOOP方法論和Sequential Thinking的分析，已經：

• 明確定義了系統目標和功能需求
• 設計了完整的三級權限架構
• 規劃了安全的資料模型和加密機制
• 制定了完整的系統架構和技術方案
• 定義了詳細的API介面和安全規範
• 制定了可行的開發計畫和風險管理策略

13.2 技術方案可行性
------------------
所選擇的技術堆疊均符合技術限制要求：
• Python前後端技術成熟可靠
• FastAPI + Streamlit組合效能優異
• SQLite3適合中小規模資料管理
• JWT認證機制安全可靠
• 加密技術符合資料保護要求

13.3 安全性評估
--------------
系統設計充分考慮了資料安全需求：
• 身分證字號採用AES-256加密
• 多層權限控制機制
• 完整的操作審計追蹤
• 輸入驗證和攻擊防護
• SSO整合確保認證安全

13.4 下一步建議
--------------

13.4.1 立即行動
• 確認系統分析報告內容
• 準備開發環境和工具
• 建立專案架構
• 開始第一階段開發

13.4.2 短期目標（1-2週）
• 完成基礎架構建立
• 實現SSO認證整合
• 建立資料庫結構
• 開發基本工具類別

13.4.3 中期目標（1-2月）
• 完成所有核心功能
• 建立完整的前端介面
• 進行系統整合測試
• 完成安全測試驗證

13.4.4 長期目標（3-6月）
• 系統正式上線運行
• 用戶訓練和支援
• 系統監控和優化
• 功能擴展和升級

13.5 專案成功關鍵因素
--------------------
• 嚴格遵循安全設計原則
• 分階段開發降低風險
• 充分的測試和驗證
• 完整的文件和訓練
• 持續的監控和改進

================================================================================
十四、附錄
================================================================================

14.1 技術文件清單
----------------
• 系統架構圖
• 資料庫設計文件
• API規格文件
• 安全設計文件
• 部署指南
• 操作手冊

14.2 開發工具清單
----------------
• 開發環境：Python 3.11
• 套件管理：uv
• 程式碼編輯：VS Code
• 版本控制：Git
• 測試工具：pytest
• 文件工具：Sphinx

14.3 相關標準和規範
------------------
• 個人資料保護法
• 資訊安全管理制度
• 系統開發生命週期
• 程式碼品質標準
• 文件撰寫規範

================================================================================
報告完成日期：2024年12月19日
分析人員：系統分析專家
審核狀態：待審核
版本：1.0
================================================================================ 
<template>
  <div class="dashboard">
    <div class="page-header">
      <h1 class="page-title">主控台</h1>
      <p class="page-description">歡迎使用CBA受款人資料搜集系統</p>
    </div>
    
    <!-- 統計卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.key">
        <div class="stat-card" :class="stat.type">
          <div class="stat-icon">
            <el-icon><component :is="stat.icon" /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 圖表區域 -->
    <el-row :gutter="24" class="charts-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>受款人統計趨勢</span>
              <el-button type="text" size="small">查看詳情</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon size="48"><TrendCharts /></el-icon>
              <p>圖表載入中...</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>部門分布</span>
              <el-button type="text" size="small">查看詳情</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon size="48"><PieChart /></el-icon>
              <p>圖表載入中...</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="24" class="actions-row">
      <el-col :xs="24" :lg="16">
        <el-card class="recent-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最近操作</span>
              <el-button type="text" size="small">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentActions" style="width: 100%">
            <el-table-column prop="action" label="操作" width="120" />
            <el-table-column prop="target" label="對象" />
            <el-table-column prop="user" label="操作人" width="100" />
            <el-table-column prop="time" label="時間" width="160" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button 
              type="primary" 
              size="large" 
              style="width: 100%; margin-bottom: 12px;"
              @click="$router.push('/payee/create')"
            >
              <el-icon><Plus /></el-icon>
              新增受款人
            </el-button>
            <el-button 
              type="success" 
              size="large" 
              style="width: 100%; margin-bottom: 12px;"
              @click="$router.push('/reports/export')"
            >
              <el-icon><Download /></el-icon>
              匯出資料
            </el-button>
            <el-button 
              type="info" 
              size="large" 
              style="width: 100%;"
              @click="$router.push('/audit')"
            >
              <el-icon><Document /></el-icon>
              查看日誌
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  User, 
  OfficeBuilding, 
  DataAnalysis, 
  Document,
  TrendCharts,
  PieChart,
  Plus,
  Download
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 統計數據
const stats = ref([
  {
    key: 'total',
    label: '總受款人數',
    value: '1,234',
    icon: User,
    type: 'primary'
  },
  {
    key: 'active',
    label: '活躍受款人',
    value: '1,156',
    icon: User,
    type: 'success'
  },
  {
    key: 'departments',
    label: '部門數量',
    value: '28',
    icon: OfficeBuilding,
    type: 'warning'
  },
  {
    key: 'reports',
    label: '本月報表',
    value: '45',
    icon: DataAnalysis,
    type: 'info'
  }
])

// 最近操作
const recentActions = ref([
  {
    action: '新增',
    target: '張三 (受款人)',
    user: '管理員',
    time: '2024-01-15 14:30'
  },
  {
    action: '編輯',
    target: '李四 (受款人)',
    user: '操作員',
    time: '2024-01-15 13:45'
  },
  {
    action: '匯出',
    target: '受款人清單',
    user: '管理員',
    time: '2024-01-15 11:20'
  },
  {
    action: '刪除',
    target: '王五 (受款人)',
    user: '管理員',
    time: '2024-01-15 10:15'
  }
])

onMounted(() => {
  // 載入儀表板數據
  loadDashboardData()
})

const loadDashboardData = async () => {
  try {
    // 這裡應該調用API獲取實際數據
    console.log('載入儀表板數據...')
  } catch (error) {
    console.error('載入儀表板數據失敗:', error)
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  .page-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    .page-description {
      color: #909399;
      margin: 0;
    }
  }
  
  .stats-row {
    margin-bottom: 24px;
    
    .stat-card {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 16px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      }
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .stat-label {
          color: #909399;
          font-size: 14px;
        }
      }
      
      &.primary .stat-icon {
        background: linear-gradient(135deg, #409eff, #66b1ff);
      }
      
      &.success .stat-icon {
        background: linear-gradient(135deg, #67c23a, #85ce61);
      }
      
      &.warning .stat-icon {
        background: linear-gradient(135deg, #e6a23c, #ebb563);
      }
      
      &.info .stat-icon {
        background: linear-gradient(135deg, #909399, #a6a9ad);
      }
    }
  }
  
  .charts-row {
    margin-bottom: 24px;
    
    .chart-card {
      height: 400px;
      
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
      }
      
      .chart-container {
        height: 320px;
        
        .chart-placeholder {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #909399;
          
          p {
            margin-top: 16px;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .actions-row {
    .recent-card {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
      }
    }
    
    .quick-actions-card {
      .card-header {
        font-weight: 600;
      }
      
      .quick-actions {
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .stats-row {
      .stat-card {
        margin-bottom: 16px;
      }
    }
    
    .charts-row {
      .chart-card {
        margin-bottom: 16px;
      }
    }
    
    .actions-row {
      .recent-card,
      .quick-actions-card {
        margin-bottom: 16px;
      }
    }
  }
}
</style>

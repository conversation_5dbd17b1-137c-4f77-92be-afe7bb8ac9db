#!/usr/bin/env python3
"""
測試完整的登入流程
"""

import requests
import sys

def test_complete_login_flow():
    """測試完整的登入流程"""
    base_url = "http://localhost:8000"
    
    # 創建session以保持cookie
    session = requests.Session()
    
    print("🧪 測試完整登入流程")
    print("=" * 50)
    
    # 1. 測試登入頁面
    print("1. 存取登入頁面...")
    try:
        response = session.get(f"{base_url}/login")
        if response.status_code == 200:
            print("✅ 登入頁面載入成功")
        else:
            print(f"❌ 登入頁面載入失敗: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登入頁面存取失敗: {e}")
        return False
    
    # 2. 執行登入
    print("2. 執行登入...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ 登入成功，收到重定向")
            
            # 檢查重定向位置
            location = response.headers.get('Location', '')
            print(f"   重定向到: {location}")
            
            # 檢查是否設定了cookie
            cookies = session.cookies
            print(f"   設定的Cookies: {list(cookies.keys())}")
            
        else:
            print(f"❌ 登入失敗: HTTP {response.status_code}")
            print(f"   回應內容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 登入執行失敗: {e}")
        return False
    
    # 3. 測試存取儀表板
    print("3. 存取儀表板...")
    try:
        response = session.get(f"{base_url}/dashboard")
        
        if response.status_code == 200:
            print("✅ 儀表板存取成功")
            
            # 檢查內容
            if "主控台" in response.text:
                print("✅ 儀表板內容正確")
            else:
                print("⚠️ 儀表板內容可能不完整")
                
        else:
            print(f"❌ 儀表板存取失敗: HTTP {response.status_code}")
            
            # 如果是401，檢查cookie
            if response.status_code == 401:
                print("   認證失敗，檢查cookies:")
                for name, value in session.cookies.items():
                    print(f"     {name}: {value[:50]}...")
                    
            return False
            
    except Exception as e:
        print(f"❌ 儀表板存取失敗: {e}")
        return False
    
    # 4. 測試其他受保護頁面
    print("4. 測試受款人頁面...")
    try:
        response = session.get(f"{base_url}/payee")
        
        if response.status_code == 200:
            print("✅ 受款人頁面存取成功")
        else:
            print(f"⚠️ 受款人頁面存取失敗: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ 受款人頁面存取失敗: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 登入流程測試完成！")
    print("\n📋 使用說明:")
    print("1. 在瀏覽器中開啟: http://localhost:8000/login")
    print("2. 使用帳號登入: admin / admin123")
    print("3. 登入成功後會自動跳轉到儀表板")
    print("4. 現在應該可以看到完整的UI優化效果")
    
    return True

def main():
    """主函數"""
    try:
        success = test_complete_login_flow()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n測試被中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n測試過程中發生錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

"""
資料查詢頁面組件
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
import io
import sys
import os
import pytz

# 設定台北時區
taipei_tz = pytz.timezone('Asia/Taipei')

# 將父目錄加入sys.path以便導入utils模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from utils.api_client import api_client
    from utils.auth_manager import AuthManager
except ImportError:
    # 如果導入失敗，建立模擬物件
    class MockApiClient:
        def get_payee_data_list(self, *args, **kwargs):
            return {"items": [], "total": 0, "page": 1, "pages": 1}
        def delete_payee_data(self, *args, **kwargs):
            return {"success": True}
    
    api_client = MockApiClient()
    
    class MockAuthManager:
        @staticmethod
        def require_authentication():
            pass
        @staticmethod
        def can_read_all_data():
            return True
        @staticmethod
        def can_export_data():
            return True
        @staticmethod
        def is_admin():
            return False
        @staticmethod
        def get_current_user():
            return {"username": "test", "full_name": "測試用戶"}
    
    AuthManager = MockAuthManager()

def render_query_filters() -> Dict[str, Any]:
    """渲染查詢篩選條件"""
    st.subheader("🔍 查詢條件")
    
    with st.form("query_filters"):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            search_filter = st.text_input(
                "關鍵字搜尋",
                placeholder="姓名、地址關鍵字",
                help="可搜尋姓名或地址中的關鍵字"
            )
        
        with col2:
            name_filter = st.text_input(
                "姓名",
                placeholder="輸入姓名",
                help="精確或模糊查詢姓名"
            )
        
        with col3:
            address_filter = st.text_input(
                "地址",
                placeholder="輸入地址關鍵字",
                help="查詢地址中包含的關鍵字"
            )
        
        # 日期篩選
        col1, col2 = st.columns(2)
        with col1:
            date_from = st.date_input(
                "建立日期（起）",
                value=None,
                help="選擇起始日期"
            )
        
        with col2:
            date_to = st.date_input(
                "建立日期（迄）",
                value=None,
                help="選擇結束日期"
            )
        
        # 管理者可額外篩選部門
        department_filter = None
        if AuthManager.is_admin():
            department_filter = st.text_input(
                "部門篩選",
                placeholder="輸入部門名稱",
                help="僅管理者可使用此篩選"
            )
        
        # 查詢按鈕
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            search_clicked = st.form_submit_button(
                "🔍 查詢",
                use_container_width=True,
                type="primary"
            )
    
    # 整理篩選條件
    filters = {}
    if search_filter:
        filters['search'] = search_filter
    if name_filter:
        filters['name'] = name_filter
    if address_filter:
        filters['address'] = address_filter
    if department_filter:
        filters['department'] = department_filter
    
    # 日期篩選需要特殊處理
    date_filters = {}
    if date_from:
        date_filters['date_from'] = date_from
    if date_to:
        date_filters['date_to'] = date_to
    
    return filters, date_filters, search_clicked

def render_data_table(data: List[Dict], current_page: int, page_size: int):
    """渲染資料表格"""
    if not data:
        st.info("📄 沒有找到符合條件的資料")
        return

    # 設定台北時區
    import pytz
    taipei_tz = pytz.timezone('Asia/Taipei')

    # 轉換為DataFrame便於顯示
    df_data = []
    for item in data:
        # 轉換時間為台北時區
        created_at_str = item.get("created_at", "")
        updated_at_str = item.get("updated_at", "")

        formatted_created_at = ""
        formatted_updated_at = ""

        if created_at_str:
            try:
                if 'T' in created_at_str:
                    created_timestamp = datetime.fromisoformat(created_at_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                else:
                    created_timestamp = datetime.strptime(created_at_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                formatted_created_at = created_timestamp.strftime('%Y-%m-%d %H:%M:%S')
            except Exception:
                formatted_created_at = created_at_str

        if updated_at_str:
            try:
                if 'T' in updated_at_str:
                    updated_timestamp = datetime.fromisoformat(updated_at_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                else:
                    updated_timestamp = datetime.strptime(updated_at_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                formatted_updated_at = updated_timestamp.strftime('%Y-%m-%d %H:%M:%S')
            except Exception:
                formatted_updated_at = updated_at_str

        df_data.append({
            "編號": item.get("id", ""),
            "姓名": item.get("name", ""),
            "身分證字號": item.get("id_number", ""),
            "地址": item.get("address", ""),
            "備註": item.get("notes", ""),
            "建立者": item.get("created_by", ""),
            "建立時間": formatted_created_at,
            "更新時間": formatted_updated_at
        })
    
    df = pd.DataFrame(df_data)
    
    # 設定欄位寬度
    column_config = {
        "編號": st.column_config.NumberColumn("編號", width="small"),
        "姓名": st.column_config.TextColumn("姓名", width="medium"),
        "身分證字號": st.column_config.TextColumn("身分證字號", width="medium"),
        "地址": st.column_config.TextColumn("地址", width="large"),
        "備註": st.column_config.TextColumn("備註", width="medium"),
        "建立者": st.column_config.TextColumn("建立者", width="small"),
        "建立時間": st.column_config.DatetimeColumn("建立時間", width="medium"),
        "更新時間": st.column_config.DatetimeColumn("更新時間", width="medium")
    }
    
    # 顯示表格
    st.dataframe(
        df,
        column_config=column_config,
        use_container_width=True,
        hide_index=True
    )
    
    # 操作按鈕區域
    if data:
        st.divider()
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col1:
            if st.button("🔄 重新整理", use_container_width=True, key="refresh_data_table"):
                st.rerun()
        
        # 匯出功能移到表單外部
        if AuthManager.can_export_data():
            with col2:
                # 準備匯出資料
                output = io.BytesIO()
                df.to_excel(output, index=False, engine='openpyxl')
                output.seek(0)
                
                st.download_button(
                    label="💾 匯出當前資料",
                    data=output,
                    file_name=f"受款人資料_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    use_container_width=True,
                    key="export_current_data"
                )
        
        with col3:
            # 管理者可以刪除資料
            if AuthManager.is_admin():
                selected_id = st.selectbox(
                    "選擇要刪除的記錄",
                    options=[0] + [item["id"] for item in data],
                    format_func=lambda x: "請選擇..." if x == 0 else f"ID: {x}",
                    key="select_record_to_delete"
                )
                
                if selected_id != 0:
                    if st.button("🗑️ 刪除記錄", use_container_width=True, type="secondary", key="delete_selected_record"):
                        delete_record(selected_id)

def render_pagination(total_count: int, current_page: int, page_size: int):
    """渲染分頁控制"""
    if total_count <= page_size:
        return current_page
    
    total_pages = (total_count + page_size - 1) // page_size
    
    st.divider()
    col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])
    
    with col1:
        if st.button("⏮️ 第一頁", disabled=(current_page == 1), key="pagination_first_page"):
            st.session_state.query_current_page = 1
            st.rerun()
    
    with col2:
        if st.button("⬅️ 上一頁", disabled=(current_page == 1), key="pagination_prev_page"):
            st.session_state.query_current_page = current_page - 1
            st.rerun()
    
    with col3:
        new_page = st.number_input(
            f"頁碼 (共 {total_pages} 頁)",
            min_value=1,
            max_value=total_pages,
            value=current_page,
            key="pagination_page_input"
        )
        if new_page != current_page:
            st.session_state.query_current_page = new_page
            st.rerun()
    
    with col4:
        if st.button("➡️ 下一頁", disabled=(current_page == total_pages), key="pagination_next_page"):
            st.session_state.query_current_page = current_page + 1
            st.rerun()
    
    with col5:
        if st.button("⏭️ 最後頁", disabled=(current_page == total_pages), key="pagination_last_page"):
            st.session_state.query_current_page = total_pages
            st.rerun()
    
    # 顯示分頁資訊
    start_record = (current_page - 1) * page_size + 1
    end_record = min(current_page * page_size, total_count)
    st.caption(f"顯示第 {start_record}-{end_record} 筆，共 {total_count} 筆記錄")
    
    return current_page

def export_current_data(data: List[Dict]):
    """匯出當前資料為CSV"""
    try:
        if not data:
            st.warning("沒有資料可供匯出")
            return
        
        # 轉換為DataFrame
        df_data = []
        for item in data:
            # 轉換時間為台北時區
            created_at = datetime.fromisoformat(item.get("created_at", "")).astimezone(taipei_tz).strftime("%Y-%m-%d %H:%M:%S")
            updated_at = datetime.fromisoformat(item.get("updated_at", "")).astimezone(taipei_tz).strftime("%Y-%m-%d %H:%M:%S")
            
            df_data.append({
                "編號": item.get("id", ""),
                "姓名": item.get("name", ""),
                "身分證字號": item.get("id_number", ""),
                "地址": item.get("address", ""),
                "備註": item.get("notes", ""),
                "建立者": item.get("created_by", ""),
                "建立時間": created_at,
                "更新時間": updated_at
            })
        
        df = pd.DataFrame(df_data)
        
        # 轉換為CSV
        csv_data = df.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
        
        # 產生檔案名稱
        current_time = datetime.now(taipei_tz).strftime("%Y%m%d_%H%M%S")
        filename = f"個人資料查詢結果_{current_time}.csv"
        
        # 提供下載
        st.download_button(
            label="📥 下載CSV檔案",
            data=csv_data,
            file_name=filename,
            mime="text/csv",
            use_container_width=True
        )
        
    except Exception as e:
        st.error(f"匯出失敗：{str(e)}")

def delete_record(record_id: int):
    """刪除記錄"""
    try:
        with st.spinner("正在刪除記錄..."):
            result = api_client.delete_payee_data(record_id)
            
            if result.get("success", False):
                st.success("✅ 記錄已成功刪除")
                st.rerun()
            else:
                st.error("❌ 刪除失敗")
                
    except Exception as e:
        st.error(f"❌ 刪除時發生錯誤：{str(e)}")

def render_data_query_page():
    """渲染資料查詢頁面主入口"""
    # 檢查認證
    AuthManager.require_authentication()
    
    st.title("🔍 查詢受款人資料")  # 修正標題
    
    # 權限說明
    if AuthManager.can_read_all_data():
        st.info("ℹ️ 您可以查看所有個人資料")
    else:
        st.info("ℹ️ 您只能查看自己建立的個人資料")
    
    # 渲染篩選條件
    filters, date_filters, search_clicked = render_query_filters()
    
    # 初始化分頁
    if 'query_current_page' not in st.session_state:
        st.session_state.query_current_page = 1
    
    if 'query_page_size' not in st.session_state:
        st.session_state.query_page_size = 20
    
    # 頁面大小選擇
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        page_size = st.selectbox(
            "每頁筆數",
            options=[10, 20, 50, 100],
            index=1,
            key="page_size_select"
        )
        if page_size != st.session_state.query_page_size:
            st.session_state.query_page_size = page_size
            st.session_state.query_current_page = 1
            st.rerun()
    
    # 如果點擊了查詢按鈕，重置到第一頁
    if search_clicked:
        st.session_state.query_current_page = 1
    
    # 查詢資料
    try:
        with st.spinner("正在查詢資料..."):
            # 組合查詢參數
            query_params = {
                **filters,
                "page": st.session_state.query_current_page,
                "size": st.session_state.query_page_size
            }
            
            # 呼叫API
            result = api_client.get_payee_data_list(**query_params)
            
            data = result.get("items", [])
            total_count = result.get("total", 0)
            
            # 顯示結果
            if total_count > 0:
                st.subheader(f"📋 查詢結果 (共 {total_count} 筆)")
                
                # 渲染資料表格
                render_data_table(data, st.session_state.query_current_page, st.session_state.query_page_size)
                
                # 渲染分頁控制
                st.session_state.query_current_page = render_pagination(
                    total_count, 
                    st.session_state.query_current_page, 
                    st.session_state.query_page_size
                )
                
            else:
                st.info("📄 沒有找到符合條件的資料")
                
                # 提供一些建議
                st.markdown("""
                **查詢建議：**
                - 檢查搜尋條件是否正確
                - 嘗試使用較寬鬆的查詢條件
                - 確認您有權限查看相關資料
                """)
    
    except Exception as e:
        st.error(f"❌ 查詢時發生錯誤：{str(e)}")
        
        # 顯示詳細錯誤訊息（僅在開發模式）
        if st.session_state.get("debug_mode", False):
            st.exception(e) 

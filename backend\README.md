# CBA人員資料調查系統 - 後端服務

## 📋 系統概述

CBA人員資料調查系統是一個基於FastAPI的後端服務，提供人員資料管理、身份認證、審計日誌等功能。

## 🛠 技術架構

- **框架**: FastAPI
- **資料庫**: SQLite3
- **認證**: JWT Bearer Token
- **Python版本**: 3.8+
- **包管理**: UV

## 🚀 快速開始

### 1. 環境要求

- Python 3.8 或更高版本
- UV 包管理器

### 2. 安裝依賴

#### Windows (PowerShell)
```powershell
# 進入後端目錄
cd backend

# 安裝依賴
uv sync

# 或者手動安裝
uv pip install -r requirements.txt
```

#### Linux/macOS
```bash
cd backend
uv sync
```

### 3. 環境配置

#### 第一次設定
```powershell
# 複製配置範例檔案
Copy-Item config.example .env

# 編輯 .env 檔案
notepad .env
```

#### 必要配置項目
- `JWT_SECRET_KEY`: JWT簽名密鑰（生產環境必須更改）
- `ENCRYPTION_KEY`: 資料加密密鑰（生產環境必須更改）
- `DATABASE_URL`: 資料庫連線URL

#### SSO配置（選用）
如果需要使用SSO功能，請在`.env`檔案中取消以下項目的註解並填入正確值：
```env
SSO_CLIENT_ID=your-sso-client-id
SSO_CLIENT_SECRET=your-sso-client-secret
SSO_AUTHORIZATION_URL=https://your-sso-provider.com/auth
SSO_TOKEN_URL=https://your-sso-provider.com/token
SSO_USER_INFO_URL=https://your-sso-provider.com/userinfo
```

### 4. 啟動服務

#### Windows (PowerShell)
```powershell
# 方法1：直接啟動
cd backend
uv run python main.py

# 方法2：使用uvicorn
cd backend
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

#### 開發模式啟動
```powershell
cd backend
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 常見問題解決

### PowerShell命令語法錯誤
**錯誤**: `'&&' 語彙基元不是有效的陳述式分隔符號`

**解決方案**: PowerShell不支援`&&`操作符，使用以下正確語法：
```powershell
# 錯誤寫法
cd backend && uv run python main.py

# 正確寫法
cd backend; uv run python main.py

# 或分別執行
cd backend
uv run python main.py
```

### SSO服務500錯誤
**錯誤**: 訪問`/api/v1/auth/sso/login`時出現500錯誤

**原因**: SSO環境變數未配置

**解決方案**:
1. 如果不使用SSO，可以忽略此錯誤，使用其他登入方式
2. 如果需要SSO，請在`.env`檔案中配置相關變數

### 資料庫初始化
```powershell
# 如果需要重新初始化資料庫
cd backend
uv run python -c "from app.models.database import init_db; init_db()"
```

## 📚 API文檔

服務啟動後，訪問以下URL查看API文檔：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 測試

### 運行測試
```powershell
cd backend
uv run python -m pytest tests/
```

### 測試覆蓋率
```powershell
cd backend
uv run python -m pytest tests/ --cov=app --cov-report=html
```

## 📁 專案結構

```
backend/
├── app/
│   ├── api/           # API路由
│   ├── core/          # 核心配置
│   ├── models/        # 資料模型
│   ├── schemas/       # Pydantic模式
│   ├── services/      # 業務邏輯
│   └── utils/         # 工具函數
├── database/          # 資料庫檔案
├── tests/            # 測試檔案
├── config.example    # 配置範例
├── main.py          # 主程式入口
└── requirements.txt # 依賴清單
```

## 🔐 安全設定

### 生產環境注意事項
1. **更改預設密鑰**: 必須更改`JWT_SECRET_KEY`和`ENCRYPTION_KEY`
2. **HTTPS**: 生產環境必須使用HTTPS
3. **環境變數**: 敏感資訊使用環境變數，不要寫入代碼
4. **資料庫安全**: 考慮使用PostgreSQL等生產級資料庫

### 推薦密鑰生成
```python
# 生成安全密鑰
import secrets
print("JWT_SECRET_KEY =", secrets.token_urlsafe(32))
print("ENCRYPTION_KEY =", secrets.token_urlsafe(32))
```

## 📞 技術支援

如遇問題，請檢查：
1. Python版本是否符合要求
2. 所有依賴是否正確安裝
3. 環境變數是否正確配置
4. 資料庫是否可正常訪問

## 📝 版本資訊

- 版本: 1.0.0
- 最後更新: 2025-01-05
- Python: 3.8+
- FastAPI: 最新版本

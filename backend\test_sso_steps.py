#!/usr/bin/env python3
"""
分步測試 SOAP SSO 認證的每個步驟
"""

import os
import sys
import asyncio
from xml.etree import ElementTree as ET
from typing import Dict, Any, Optional

# 模擬基本的類和函數
class MockSession:
    """模擬資料庫會話"""
    def __init__(self):
        self.mock_data = {}
    
    def query(self, model):
        class MockQuery:
            def filter(self, condition):
                return self
            
            def first(self):
                return None
        
        return MockQuery()
    
    def add(self, obj):
        pass
    
    def commit(self):
        pass
    
    def refresh(self, obj):
        pass

class MockUser:
    """模擬用戶類"""
    def __init__(self, username, full_name):
        self.id = 1
        self.username = username
        self.full_name = full_name
        self.email = f"{username}@example.com"
        self.department = None
        self.roles = []
        self.permissions = []

class MockDepartment:
    """模擬部門類"""
    def __init__(self, name):
        self.id = 1
        self.name = name

def test_xml_parsing():
    """測試 XML 解析"""
    print("🔍 測試 XML 解析")
    print("=" * 40)
    
    # 模擬從 SSO 服務取得的 XML 回應
    xml_response = """<?xml version="1.0" encoding="utf-8"?>
<userProfile>
    <帳號>009999</帳號>
    <姓名>弓長張</姓名>
    <電話 />
    <電話 />
    <傳真 />
    <手機 />
    <手機 />
    <統號 />
    <Email />
    <機關代碼>376530000A</機關代碼>
    <機關名稱>屏東縣政府</機關名稱>
    <職稱代碼 />
    <職稱 />
    <可使用系統 />
    <單位代碼>1030</單位代碼>
</userProfile>"""
    
    try:
        root = ET.fromstring(xml_response)
        print(f"✅ XML 解析成功，根元素: {root.tag}")
        
        # 提取用戶資訊
        user_info = {
            'account': _get_xml_text(root, '帳號'),
            'full_name': _get_xml_text(root, '姓名'),
            'dept_code': _get_xml_text(root, '單位代碼'),
            'org_name': _get_xml_text(root, '機關名稱')
        }
        
        print(f"📋 解析的用戶資訊: {user_info}")
        
        # 驗證必要欄位
        if user_info['account']:
            print("✅ 帳號欄位存在")
        else:
            print("❌ 帳號欄位缺失")
            
        return user_info
        
    except ET.ParseError as e:
        print(f"❌ XML 解析失敗: {e}")
        return None

def _get_xml_text(root: ET.Element, tag_name: str) -> Optional[str]:
    """從XML中安全地取得文字內容"""
    element = root.find(tag_name)
    return element.text if element is not None else None

async def test_user_profile_processing(user_info: Dict[str, str]):
    """測試用戶資料處理"""
    print("\n👤 測試用戶資料處理")
    print("=" * 40)
    
    if not user_info:
        print("❌ 沒有用戶資訊可處理")
        return None
        
    try:
        # 模擬資料庫會話
        db = MockSession()
        
        # 模擬用戶資料處理
        account = user_info['account']
        full_name = user_info['full_name'] or account
        dept_code = user_info['dept_code']
        org_name = user_info['org_name']
        
        print(f"📝 處理用戶資料:")
        print(f"  - 帳號: {account}")
        print(f"  - 姓名: {full_name}")
        print(f"  - 部門代碼: {dept_code}")
        print(f"  - 機關名稱: {org_name}")
        
        # 處理部門代碼
        bureau_code = dept_code[:2] + "00" if dept_code and len(dept_code) >= 2 else "0000"
        print(f"  - 局處代碼: {bureau_code}")
        
        # 模擬建立用戶
        user = MockUser(account, full_name)
        user.department = MockDepartment(org_name or f"部門{bureau_code}")
        
        print("✅ 用戶資料處理成功")
        return user
        
    except Exception as e:
        print(f"❌ 用戶資料處理失敗: {e}")
        return None

def test_jwt_token_creation(user):
    """測試 JWT Token 建立"""
    print("\n🔑 測試 JWT Token 建立")
    print("=" * 40)
    
    if not user:
        print("❌ 沒有用戶資訊可建立 Token")
        return None
        
    try:
        # 模擬 JWT Token 數據
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "username": user.username,
            "full_name": user.full_name,
            "department_id": user.department.id if user.department else None,
            "roles": [role.name for role in user.roles],
            "permissions": user.permissions
        }
        
        print(f"📋 JWT Token 數據: {token_data}")
        
        # 模擬建立 Token (實際上這裡會使用 JWT 庫)
        mock_token = f"mock.jwt.token.{user.username}"
        
        print(f"✅ JWT Token 建立成功: {mock_token}")
        return mock_token
        
    except Exception as e:
        print(f"❌ JWT Token 建立失敗: {e}")
        return None

def test_final_response(access_token: str, user):
    """測試最終回應"""
    print("\n📤 測試最終回應")
    print("=" * 40)
    
    if not access_token or not user:
        print("❌ 缺少必要的資料")
        return None
        
    try:
        # 模擬最終回應
        result = {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "department": user.department.name if user.department else None,
                "roles": [role.name for role in user.roles],
                "permissions": user.permissions
            }
        }
        
        print(f"📋 最終回應: {result}")
        print("✅ 最終回應建立成功")
        return result
        
    except Exception as e:
        print(f"❌ 最終回應建立失敗: {e}")
        return None

async def main():
    """主要測試函數"""
    print("🔬 SOAP SSO 認證步驟測試")
    print("=" * 60)
    
    # 步驟 1: 測試 XML 解析
    user_info = test_xml_parsing()
    
    # 步驟 2: 測試用戶資料處理
    user = await test_user_profile_processing(user_info)
    
    # 步驟 3: 測試 JWT Token 建立
    access_token = test_jwt_token_creation(user)
    
    # 步驟 4: 測試最終回應
    result = test_final_response(access_token, user)
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果:")
    print(f"  - XML 解析: {'✅ 成功' if user_info else '❌ 失敗'}")
    print(f"  - 用戶資料處理: {'✅ 成功' if user else '❌ 失敗'}")
    print(f"  - JWT Token 建立: {'✅ 成功' if access_token else '❌ 失敗'}")
    print(f"  - 最終回應: {'✅ 成功' if result else '❌ 失敗'}")
    
    if all([user_info, user, access_token, result]):
        print("🎉 所有步驟都成功！")
        print("✅ 問題可能在於實際的資料庫操作或 JWT 庫依賴")
    else:
        print("❌ 某些步驟失敗，需要進一步調查")

if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python3
"""
修復Streamlit代理問題的腳本
提供多種解決方案
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def create_streamlit_config():
    """創建Streamlit配置檔案"""
    print("📝 創建Streamlit配置檔案...")
    
    config_dir = Path("frontend/.streamlit")
    config_dir.mkdir(exist_ok=True)
    
    config_content = """[server]
port = 8501
address = "0.0.0.0"
headless = true
enableCORS = true
enableXsrfProtection = false
baseUrlPath = ""

[browser]
gatherUsageStats = false

[client]
showErrorDetails = true
toolbarMode = "minimal"

[theme]
primaryColor = "#0066CC"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
"""
    
    config_file = config_dir / "config.toml"
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 配置檔案已創建: {config_file}")

def solution_1_direct_access():
    """解決方案1: 直接存取各服務"""
    print("\n🔧 解決方案1: 直接存取各服務")
    print("=" * 40)
    print("不使用80埠代理，直接存取各服務：")
    print("• 前端UI: http://localhost:8501")
    print("• 後端API: http://localhost:8080")
    print("• API文檔: http://localhost:8080/docs")
    print("\n這是最穩定的方式，避免代理問題。")

def solution_2_nginx():
    """解決方案2: 使用nginx代理"""
    print("\n🔧 解決方案2: 使用nginx代理")
    print("=" * 40)
    print("1. 安裝nginx")
    print("2. 使用提供的nginx配置檔案")
    print("3. 啟動nginx服務")
    print("\n配置檔案: nginx-integrated.conf")

def solution_3_streamlit_proxy():
    """解決方案3: 使用專用Streamlit代理"""
    print("\n🔧 解決方案3: 使用專用Streamlit代理")
    print("=" * 40)
    print("使用專門為Streamlit設計的代理服務器：")
    print("python streamlit_proxy.py")

def solution_4_environment_fix():
    """解決方案4: 環境變數修復"""
    print("\n🔧 解決方案4: 設定環境變數")
    print("=" * 40)
    
    # 設定環境變數
    env_vars = {
        'STREAMLIT_SERVER_ENABLE_CORS': 'true',
        'STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION': 'false',
        'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false',
        'STREAMLIT_SERVER_HEADLESS': 'true'
    }
    
    print("設定以下環境變數：")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key}={value}")
    
    print("✅ 環境變數已設定")

def start_services_with_fix():
    """啟動服務並應用修復"""
    print("\n🚀 啟動修復後的服務...")
    
    # 創建配置檔案
    create_streamlit_config()
    
    # 設定環境變數
    solution_4_environment_fix()
    
    print("\n選擇啟動方式：")
    print("1. 直接存取各服務（推薦）")
    print("2. 使用Streamlit專用代理")
    print("3. 手動啟動")
    
    choice = input("\n請選擇 (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 啟動各服務...")
        print("請使用以下命令分別啟動服務：")
        print("\n後端API:")
        print("cd backend && uv run uvicorn main:app --host 0.0.0.0 --port 8080")
        print("\n前端UI:")
        print("cd frontend && streamlit run main.py --server.port 8501 --server.address 0.0.0.0")
        print("\n然後直接存取:")
        print("• 前端: http://localhost:8501")
        print("• 後端: http://localhost:8080")
        
    elif choice == "2":
        print("\n🚀 啟動Streamlit專用代理...")
        try:
            subprocess.run([sys.executable, "streamlit_proxy.py"])
        except KeyboardInterrupt:
            print("\n✅ 服務已停止")
        
    elif choice == "3":
        print("\n📋 手動啟動指令:")
        print("1. 後端: cd backend && uv run uvicorn main:app --host 0.0.0.0 --port 8080")
        print("2. 前端: cd frontend && streamlit run main.py")
        print("3. 代理: python streamlit_proxy.py (可選)")

def diagnose_problem():
    """診斷問題"""
    print("🔍 診斷Streamlit代理問題...")
    print("=" * 40)
    
    # 檢查服務狀態
    print("檢查服務狀態:")
    
    services = [
        ("前端Streamlit", "http://localhost:8501"),
        ("後端API", "http://localhost:8080/health"),
        ("整合代理", "http://localhost:80/health")
    ]
    
    for name, url in services:
        try:
            import requests
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {name}: 正常")
            else:
                print(f"  ⚠️ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: 無法連接 - {e}")
    
    print("\n常見問題和解決方案:")
    print("1. 304狀態碼重複出現 - 這是正常的快取行為")
    print("2. WebSocket連接問題 - 使用nginx或直接存取")
    print("3. CORS錯誤 - 檢查Streamlit配置")
    print("4. 代理超時 - 增加超時設定")

def main():
    """主函數"""
    print("🔧 CBA系統 - Streamlit代理問題修復工具")
    print("=" * 50)
    
    print("\n選擇操作：")
    print("1. 診斷問題")
    print("2. 查看解決方案")
    print("3. 應用修復並啟動")
    print("4. 創建Streamlit配置")
    print("5. 退出")
    
    while True:
        choice = input("\n請選擇 (1-5): ").strip()
        
        if choice == "1":
            diagnose_problem()
        elif choice == "2":
            solution_1_direct_access()
            solution_2_nginx()
            solution_3_streamlit_proxy()
            solution_4_environment_fix()
        elif choice == "3":
            start_services_with_fix()
            break
        elif choice == "4":
            create_streamlit_config()
        elif choice == "5":
            print("👋 再見！")
            break
        else:
            print("❌ 無效選擇，請重新輸入")

if __name__ == "__main__":
    main()

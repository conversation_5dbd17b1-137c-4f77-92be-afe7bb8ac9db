#!/usr/bin/env python3
"""
CBA人員資料調查系統 - 系統監控腳本
監控系統健康狀態、效能指標和服務可用性
"""

import os
import sys
import json
import time
import psutil
import logging
import requests
import smtplib
import sqlite3
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Any, Optional
from pathlib import Path

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/opt/cba-system/logs/monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SystemMonitor:
    """系統監控類別"""
    
    def __init__(self, config_file: str = "/opt/cba-system/monitor_config.json"):
        self.config = self._load_config(config_file)
        self.alerts = []
        self.metrics = {}
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """載入監控配置"""
        default_config = {
            "thresholds": {
                "cpu_usage": 80.0,
                "memory_usage": 85.0,
                "disk_usage": 90.0,
                "response_time": 5.0
            },
            "services": {
                "backend": {
                    "url": "http://127.0.0.1:8000/api/v1/health",
                    "timeout": 10
                },
                "frontend": {
                    "url": "http://127.0.0.1:8501",
                    "timeout": 10
                }
            },
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "<EMAIL>",
                "password": "your-app-password",
                "recipients": ["<EMAIL>"]
            },
            "database": {
                "path": "/opt/cba-system/database/cba_personal_data.db"
            },
            "log_paths": [
                "/var/log/nginx/cba-system.error.log",
                "/opt/cba-system/logs/app.log"
            ]
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合併預設配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                # 建立預設配置檔案
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                logger.info(f"建立預設配置檔案: {config_file}")
                return default_config
        except Exception as e:
            logger.error(f"載入配置檔案失敗: {e}")
            return default_config
    
    def check_system_resources(self) -> Dict[str, Any]:
        """檢查系統資源使用情況"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 記憶體使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁碟使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 系統負載
            try:
                load_avg = os.getloadavg()
            except OSError:
                load_avg = (0, 0, 0)  # Windows不支援
            
            metrics = {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "status": "critical" if cpu_percent > self.config["thresholds"]["cpu_usage"] else "normal"
                },
                "memory": {
                    "usage_percent": memory_percent,
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "status": "critical" if memory_percent > self.config["thresholds"]["memory_usage"] else "normal"
                },
                "disk": {
                    "usage_percent": disk_percent,
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "status": "critical" if disk_percent > self.config["thresholds"]["disk_usage"] else "normal"
                },
                "load_average": {
                    "1min": load_avg[0],
                    "5min": load_avg[1],
                    "15min": load_avg[2]
                }
            }
            
            # 檢查是否需要告警
            if cpu_percent > self.config["thresholds"]["cpu_usage"]:
                self.alerts.append(f"CPU使用率過高: {cpu_percent:.1f}%")
            
            if memory_percent > self.config["thresholds"]["memory_usage"]:
                self.alerts.append(f"記憶體使用率過高: {memory_percent:.1f}%")
            
            if disk_percent > self.config["thresholds"]["disk_usage"]:
                self.alerts.append(f"磁碟使用率過高: {disk_percent:.1f}%")
            
            self.metrics["system"] = metrics
            return metrics
            
        except Exception as e:
            logger.error(f"檢查系統資源失敗: {e}")
            self.alerts.append(f"系統資源檢查失敗: {str(e)}")
            return {}
    
    def check_service_health(self) -> Dict[str, Any]:
        """檢查服務健康狀態"""
        services_status = {}
        
        for service_name, service_config in self.config["services"].items():
            try:
                start_time = time.time()
                response = requests.get(
                    service_config["url"],
                    timeout=service_config["timeout"]
                )
                response_time = time.time() - start_time
                
                status = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "status_code": response.status_code,
                    "response_time": round(response_time, 3),
                    "last_check": datetime.now().isoformat()
                }
                
                # 檢查回應時間
                if response_time > self.config["thresholds"]["response_time"]:
                    self.alerts.append(f"{service_name}服務回應時間過長: {response_time:.3f}秒")
                    status["status"] = "slow"
                
                # 檢查狀態碼
                if response.status_code != 200:
                    self.alerts.append(f"{service_name}服務異常 (HTTP {response.status_code})")
                
                services_status[service_name] = status
                
            except requests.exceptions.Timeout:
                self.alerts.append(f"{service_name}服務請求逾時")
                services_status[service_name] = {
                    "status": "timeout",
                    "error": "請求逾時",
                    "last_check": datetime.now().isoformat()
                }
            except requests.exceptions.ConnectionError:
                self.alerts.append(f"{service_name}服務連線失敗")
                services_status[service_name] = {
                    "status": "down",
                    "error": "連線失敗",
                    "last_check": datetime.now().isoformat()
                }
            except Exception as e:
                self.alerts.append(f"{service_name}服務檢查失敗: {str(e)}")
                services_status[service_name] = {
                    "status": "error",
                    "error": str(e),
                    "last_check": datetime.now().isoformat()
                }
        
        self.metrics["services"] = services_status
        return services_status
    
    def check_database_health(self) -> Dict[str, Any]:
        """檢查資料庫健康狀態"""
        db_status = {}
        
        try:
            db_path = self.config["database"]["path"]
            
            if not os.path.exists(db_path):
                self.alerts.append("資料庫檔案不存在")
                return {"status": "error", "error": "資料庫檔案不存在"}
            
            # 檢查檔案大小
            file_size = os.path.getsize(db_path)
            file_size_mb = round(file_size / (1024*1024), 2)
            
            # 測試資料庫連線
            start_time = time.time()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
            table_count = cursor.fetchone()[0]
            conn.close()
            connection_time = time.time() - start_time
            
            db_status = {
                "status": "healthy",
                "file_size_mb": file_size_mb,
                "table_count": table_count,
                "connection_time": round(connection_time, 3),
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.alerts.append(f"資料庫健康檢查失敗: {str(e)}")
            db_status = {
                "status": "error",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
        
        self.metrics["database"] = db_status
        return db_status
    
    def check_log_errors(self) -> Dict[str, Any]:
        """檢查日誌檔案中的錯誤"""
        log_status = {}
        
        for log_path in self.config["log_paths"]:
            try:
                if not os.path.exists(log_path):
                    continue
                
                # 檢查最近1小時的錯誤
                cutoff_time = datetime.now() - timedelta(hours=1)
                error_count = 0
                
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                    # 檢查最後100行
                    for line in lines[-100:]:
                        if any(keyword in line.lower() for keyword in ['error', 'exception', 'critical', 'fatal']):
                            error_count += 1
                
                log_status[log_path] = {
                    "error_count_last_hour": error_count,
                    "file_size_mb": round(os.path.getsize(log_path) / (1024*1024), 2),
                    "last_modified": datetime.fromtimestamp(os.path.getmtime(log_path)).isoformat()
                }
                
                if error_count > 10:
                    self.alerts.append(f"日誌檔案 {log_path} 發現大量錯誤: {error_count}個")
                
            except Exception as e:
                logger.error(f"檢查日誌檔案 {log_path} 失敗: {e}")
        
        self.metrics["logs"] = log_status
        return log_status
    
    def send_alert_email(self, subject: str, content: str):
        """發送告警郵件"""
        if not self.config["email"]["enabled"]:
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = self.config["email"]["username"]
            msg['To'] = ", ".join(self.config["email"]["recipients"])
            msg['Subject'] = subject
            
            msg.attach(MimeText(content, 'html', 'utf-8'))
            
            server = smtplib.SMTP(self.config["email"]["smtp_server"], self.config["email"]["smtp_port"])
            server.starttls()
            server.login(self.config["email"]["username"], self.config["email"]["password"])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"告警郵件已發送: {subject}")
            
        except Exception as e:
            logger.error(f"發送告警郵件失敗: {e}")
    
    def generate_report(self) -> str:
        """產生監控報告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>CBA系統監控報告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .alert {{ color: #dc3545; font-weight: bold; }}
                .normal {{ color: #28a745; }}
                .warning {{ color: #ffc107; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>CBA人員資料調查系統監控報告</h1>
                <p>監控時間: {timestamp}</p>
            </div>
        """
        
        # 告警摘要
        if self.alerts:
            html_report += """
            <div class="section">
                <h2 class="alert">🚨 告警事件</h2>
                <ul>
            """
            for alert in self.alerts:
                html_report += f"<li class='alert'>{alert}</li>"
            html_report += "</ul></div>"
        else:
            html_report += """
            <div class="section">
                <h2 class="normal">✅ 系統狀態正常</h2>
                <p>目前無告警事件</p>
            </div>
            """
        
        # 系統資源
        if "system" in self.metrics:
            system = self.metrics["system"]
            html_report += f"""
            <div class="section">
                <h2>💻 系統資源</h2>
                <table>
                    <tr><th>項目</th><th>使用率</th><th>狀態</th></tr>
                    <tr><td>CPU</td><td>{system['cpu']['usage_percent']:.1f}%</td><td class="{system['cpu']['status']}">{system['cpu']['status']}</td></tr>
                    <tr><td>記憶體</td><td>{system['memory']['usage_percent']:.1f}%</td><td class="{system['memory']['status']}">{system['memory']['status']}</td></tr>
                    <tr><td>磁碟</td><td>{system['disk']['usage_percent']:.1f}%</td><td class="{system['disk']['status']}">{system['disk']['status']}</td></tr>
                </table>
            </div>
            """
        
        # 服務狀態
        if "services" in self.metrics:
            html_report += """
            <div class="section">
                <h2>🔧 服務狀態</h2>
                <table>
                    <tr><th>服務</th><th>狀態</th><th>回應時間</th></tr>
            """
            for service, status in self.metrics["services"].items():
                status_class = "normal" if status["status"] == "healthy" else "alert"
                response_time = status.get("response_time", "N/A")
                html_report += f"""
                <tr><td>{service}</td><td class="{status_class}">{status['status']}</td><td>{response_time}</td></tr>
                """
            html_report += "</table></div>"
        
        html_report += "</body></html>"
        
        return html_report
    
    def run_monitoring(self):
        """執行完整監控檢查"""
        logger.info("開始系統監控檢查...")
        
        # 重置告警和指標
        self.alerts = []
        self.metrics = {}
        
        # 執行各項檢查
        self.check_system_resources()
        self.check_service_health()
        self.check_database_health()
        self.check_log_errors()
        
        # 產生報告
        report = self.generate_report()
        
        # 儲存報告
        report_path = f"/opt/cba-system/logs/monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"監控報告已儲存: {report_path}")
        except Exception as e:
            logger.error(f"儲存監控報告失敗: {e}")
        
        # 發送告警
        if self.alerts:
            subject = f"CBA系統告警 - {len(self.alerts)}個問題需要關注"
            self.send_alert_email(subject, report)
            logger.warning(f"發現 {len(self.alerts)} 個告警事件")
        else:
            logger.info("系統狀態正常，無告警事件")
        
        # 清理舊報告（保留最近7天）
        self._cleanup_old_reports()
        
        return self.metrics
    
    def _cleanup_old_reports(self):
        """清理舊的監控報告"""
        try:
            reports_dir = Path("/opt/cba-system/logs")
            cutoff_time = datetime.now() - timedelta(days=7)
            
            for report_file in reports_dir.glob("monitor_report_*.html"):
                if report_file.stat().st_mtime < cutoff_time.timestamp():
                    report_file.unlink()
                    logger.info(f"已清理舊報告: {report_file}")
        except Exception as e:
            logger.error(f"清理舊報告失敗: {e}")


def main():
    """主函數"""
    # 確保日誌目錄存在
    os.makedirs("/opt/cba-system/logs", exist_ok=True)
    
    # 建立監控實例
    monitor = SystemMonitor()
    
    # 執行監控
    try:
        metrics = monitor.run_monitoring()
        
        # 輸出JSON格式結果（用於其他系統整合）
        if "--json" in sys.argv:
            print(json.dumps(metrics, indent=2, ensure_ascii=False))
        
        # 檢查是否有告警
        if monitor.alerts:
            sys.exit(1)  # 有告警時返回非零退出碼
        else:
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"監控執行失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 
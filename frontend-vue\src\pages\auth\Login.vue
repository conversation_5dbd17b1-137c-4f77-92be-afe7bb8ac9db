<template>
  <div class="login-container">
    <el-card class="login-card" shadow="always">
      <template #header>
        <div class="login-header">
          <h2>用戶登入</h2>
          <p>請輸入您的帳號密碼</p>
        </div>
      </template>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        size="large"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="請輸入用戶名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="請輸入密碼"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登入中...' : '登入' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-divider">
        <span>或</span>
      </div>
      
      <el-button
        type="info"
        size="large"
        style="width: 100%"
        :loading="ssoLoading"
        @click="handleSSOLogin"
      >
        <el-icon><Key /></el-icon>
        {{ ssoLoading ? 'SSO登入中...' : 'SSO單一簽入' }}
      </el-button>
      
      <div class="login-footer">
        <p>開發環境測試帳號：</p>
        <p>管理員：admin / admin123</p>
        <p>一般用戶：user / user123</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Key } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { LoginCredentials } from '@/types/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表單引用
const loginFormRef = ref<FormInstance>()

// 載入狀態
const loading = ref(false)
const ssoLoading = ref(false)

// 登入表單
const loginForm = reactive<LoginCredentials>({
  username: '',
  password: ''
})

// 表單驗證規則
const loginRules: FormRules = {
  username: [
    { required: true, message: '請輸入用戶名', trigger: 'blur' },
    { min: 3, max: 50, message: '用戶名長度在 3 到 50 個字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 6, max: 100, message: '密碼長度在 6 到 100 個字符', trigger: 'blur' }
  ]
}

// 處理登入
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      ElMessage.success('登入成功')
      
      // 跳轉到目標頁面或首頁
      const redirect = route.query.redirect as string || '/'
      router.push(redirect)
    }
  } catch (error: any) {
    console.error('登入失敗:', error)
    ElMessage.error(error.message || '登入失敗')
  } finally {
    loading.value = false
  }
}

// 處理SSO登入
const handleSSOLogin = async () => {
  try {
    ssoLoading.value = true
    
    // 這裡應該跳轉到SSO登入頁面
    // 實際實作中需要根據後端SSO配置來處理
    ElMessage.info('SSO登入功能開發中...')
    
    // 模擬SSO登入流程
    // window.location.href = '/api/v1/auth/sso-login'
    
  } catch (error: any) {
    console.error('SSO登入失敗:', error)
    ElMessage.error(error.message || 'SSO登入失敗')
  } finally {
    ssoLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  
  .login-card {
    width: 100%;
    max-width: 400px;
    
    .login-header {
      text-align: center;
      
      h2 {
        margin: 0 0 8px 0;
        color: #303133;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .login-divider {
      text-align: center;
      margin: 24px 0;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e4e7ed;
      }
      
      span {
        background: #fff;
        padding: 0 16px;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .login-footer {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
      text-align: center;
      
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #909399;
        
        &:first-child {
          font-weight: 600;
          color: #606266;
        }
      }
    }
  }
}
</style>

/**
 * 路由守衛
 */
import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 不需要認證的路由
const whiteList = ['/auth/login', '/auth/sso-callback', '/404', '/403']

export function setupRouterGuards(router: Router) {
  // 全域前置守衛
  router.beforeEach(async (to, from, next) => {
    // 開始進度條
    NProgress.start()
    
    // 設定頁面標題
    if (to.meta.title) {
      document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
    } else {
      document.title = import.meta.env.VITE_APP_TITLE
    }
    
    const authStore = useAuthStore()
    
    // 如果還沒有初始化認證狀態，先初始化
    if (!authStore.isAuthenticated && authStore.token) {
      try {
        await authStore.initAuth()
      } catch (error) {
        console.error('初始化認證狀態失敗:', error)
        authStore.clearAuthData()
      }
    }
    
    // 檢查是否需要認證
    const requiresAuth = to.meta.requiresAuth !== false
    
    if (requiresAuth) {
      if (!authStore.isAuthenticated) {
        // 未登入，跳轉到登入頁
        ElMessage.warning('請先登入')
        next({
          path: '/auth/login',
          query: { redirect: to.fullPath }
        })
        return
      }
      
      // 檢查權限
      if (to.meta.permission) {
        const hasPermission = authStore.checkPermission(to.meta.permission as string)
        if (!hasPermission) {
          ElMessage.error('沒有權限存取此頁面')
          next('/403')
          return
        }
      }
      
      // 檢查角色
      if (to.meta.roles) {
        const roles = to.meta.roles as string[]
        const hasRole = roles.includes(authStore.userRole || '')
        if (!hasRole) {
          ElMessage.error('沒有權限存取此頁面')
          next('/403')
          return
        }
      }
    } else {
      // 不需要認證的頁面，如果已登入且訪問登入頁，跳轉到首頁
      if (authStore.isAuthenticated && to.path === '/auth/login') {
        next('/')
        return
      }
    }
    
    next()
  })
  
  // 全域後置守衛
  router.afterEach((to, from) => {
    // 結束進度條
    NProgress.done()
    
    // 記錄頁面訪問（可用於統計）
    if (import.meta.env.VITE_APP_DEBUG === 'true') {
      console.log(`路由跳轉: ${from.path} -> ${to.path}`)
    }
  })
  
  // 路由錯誤處理
  router.onError((error) => {
    console.error('路由錯誤:', error)
    NProgress.done()
    ElMessage.error('頁面載入失敗')
  })
}

/**
 * 檢查用戶是否有權限存取路由
 */
export function hasRoutePermission(route: any, userPermissions: string[], userRole: string): boolean {
  // 管理員有所有權限
  if (userRole === 'admin') {
    return true
  }
  
  // 檢查特定權限
  if (route.meta?.permission) {
    return userPermissions.includes(route.meta.permission)
  }
  
  // 檢查角色
  if (route.meta?.roles) {
    return route.meta.roles.includes(userRole)
  }
  
  // 預設允許存取
  return true
}

/**
 * 過濾用戶可存取的路由
 */
export function filterRoutes(routes: any[], userPermissions: string[], userRole: string): any[] {
  return routes.filter(route => {
    const hasPermission = hasRoutePermission(route, userPermissions, userRole)
    
    if (hasPermission && route.children) {
      route.children = filterRoutes(route.children, userPermissions, userRole)
    }
    
    return hasPermission
  })
}

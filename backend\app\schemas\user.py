"""
用戶相關的Pydantic模型
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel


class UserBase(BaseModel):
    """用戶基礎模型"""
    username: str
    full_name: Optional[str] = None
    department: Optional[str] = None


class UserCreate(UserBase):
    """建立用戶請求模型"""
    password: str
    role_id: int


class UserUpdate(BaseModel):
    """更新用戶請求模型"""
    full_name: Optional[str] = None
    department: Optional[str] = None
    is_active: Optional[bool] = None


class UserInDB(UserBase):
    """資料庫中的用戶模型"""
    id: int
    hashed_password: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class UserResponse(UserBase):
    """用戶回應模型"""
    id: int
    is_active: bool
    created_at: datetime
    roles: List[str] = []
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用戶登入請求模型"""
    username: str
    password: str


class Token(BaseModel):
    """JWT Token回應模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: UserResponse


class TokenData(BaseModel):
    """Token中的用戶資料"""
    user_id: Optional[int] = None
    username: Optional[str] = None
    roles: List[str] = []


class UserAdminResponse(BaseModel):
    """用戶管理回應模型"""
    id: int
    username: str
    full_name: Optional[str] = None
    department: Optional[str] = None
    is_active: bool
    created_at: datetime
    roles: List[str] = []

    class Config:
        from_attributes = True


class UpdateUserStatusRequest(BaseModel):
    """更新用戶狀態請求模型"""
    is_active: bool


class UpdateUserDepartmentRequest(BaseModel):
    """更新用戶部門請求模型"""
    department_id: int 
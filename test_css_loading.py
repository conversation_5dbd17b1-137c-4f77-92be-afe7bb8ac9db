#!/usr/bin/env python3
"""
測試CSS載入和樣式應用
"""

import requests
import sys
from bs4 import BeautifulSoup

def test_css_loading():
    """測試CSS文件載入"""
    base_url = "http://localhost:8000"
    
    print("🎨 測試CSS載入和樣式應用")
    print("=" * 50)
    
    # 1. 測試CSS文件直接存取
    print("1. 測試CSS文件存取...")
    try:
        response = requests.get(f"{base_url}/static/css/style.css")
        if response.status_code == 200:
            print("✅ CSS文件載入成功")
            print(f"   文件大小: {len(response.content)} bytes")
            
            # 檢查關鍵CSS內容
            css_content = response.text
            key_styles = [
                ":root",
                "--primary-color",
                ".navbar",
                ".btn-primary",
                ".card",
                ".stat-icon",
                "@keyframes"
            ]
            
            missing_styles = []
            for style in key_styles:
                if style not in css_content:
                    missing_styles.append(style)
            
            if not missing_styles:
                print("✅ 所有關鍵樣式都存在")
            else:
                print(f"⚠️ 缺少樣式: {missing_styles}")
                
        else:
            print(f"❌ CSS文件載入失敗: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CSS文件存取失敗: {e}")
        return False
    
    # 2. 測試登入頁面CSS引用
    print("2. 測試登入頁面CSS引用...")
    try:
        response = requests.get(f"{base_url}/login")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 檢查CSS連結
            css_links = soup.find_all('link', {'rel': 'stylesheet'})
            style_css_found = False
            
            for link in css_links:
                href = link.get('href', '')
                if 'style.css' in href:
                    style_css_found = True
                    print(f"✅ 找到CSS連結: {href}")
                    break
            
            if not style_css_found:
                print("❌ 未找到style.css連結")
                return False
            
            # 檢查內聯樣式
            style_blocks = soup.find_all('style')
            if style_blocks:
                print(f"✅ 找到 {len(style_blocks)} 個內聯樣式區塊")
            else:
                print("⚠️ 未找到內聯樣式區塊")
                
        else:
            print(f"❌ 登入頁面載入失敗: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登入頁面測試失敗: {e}")
        return False
    
    # 3. 測試JavaScript文件
    print("3. 測試JavaScript文件...")
    try:
        response = requests.get(f"{base_url}/static/js/app.js")
        if response.status_code == 200:
            print("✅ JavaScript文件載入成功")
            print(f"   文件大小: {len(response.content)} bytes")
        else:
            print(f"⚠️ JavaScript文件載入失敗: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️ JavaScript文件測試失敗: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 CSS載入測試完成！")
    print("\n📋 現在您應該看到:")
    print("1. 登入頁面有藍色漸變背景")
    print("2. 毛玻璃效果的登入卡片")
    print("3. 現代化的表單設計")
    print("4. 動畫和懸停效果")
    print("\n🔧 如果樣式仍未顯示，請:")
    print("1. 清除瀏覽器緩存 (Ctrl+F5)")
    print("2. 檢查瀏覽器開發者工具的Console")
    print("3. 確認沒有CSS載入錯誤")
    
    return True

def main():
    """主函數"""
    try:
        success = test_css_loading()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n測試被中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n測試過程中發生錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

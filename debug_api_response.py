#!/usr/bin/env python3
"""
調試API回應格式
"""

import requests
import json
from datetime import datetime
import pytz

def debug_audit_api():
    """調試審計日誌API回應"""
    print("🔍 調試審計日誌API回應格式...")
    
    # API端點
    api_url = "http://localhost:8080/api/v1/audit/logs"
    
    try:
        # 發送請求
        response = requests.get(
            api_url,
            params={"page": 1, "size": 5},
            headers={"Authorization": "Bearer your_token_here"},  # 需要實際的token
            timeout=10
        )
        
        print(f"✅ HTTP狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 回應結構: {list(data.keys())}")
            
            if "items" in data and data["items"]:
                first_item = data["items"][0]
                print(f"✅ 第一筆記錄結構: {list(first_item.keys())}")
                
                # 檢查時間戳格式
                timestamp = first_item.get("timestamp")
                print(f"✅ 原始時間戳: {timestamp}")
                print(f"✅ 時間戳類型: {type(timestamp)}")
                
                # 嘗試解析時間
                if timestamp:
                    try:
                        taipei_tz = pytz.timezone('Asia/Taipei')
                        
                        # 嘗試不同的解析方法
                        if isinstance(timestamp, str):
                            if 'T' in timestamp:
                                # ISO格式
                                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).astimezone(taipei_tz)
                                print(f"✅ ISO格式轉換結果: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                            else:
                                # 假設是UTC時間
                                dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                                print(f"✅ UTC格式轉換結果: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                        else:
                            print(f"⚠️ 時間戳不是字串格式: {type(timestamp)}")
                            
                    except Exception as e:
                        print(f"❌ 時間解析失敗: {str(e)}")
            else:
                print("⚠️ 沒有找到審計記錄")
                
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            print(f"❌ 錯誤內容: {response.text}")
            
    except Exception as e:
        print(f"❌ 請求失敗: {str(e)}")

def test_time_conversion_logic():
    """測試時間轉換邏輯"""
    print("\n🕒 測試修復後的時間轉換邏輯...")

    # 模擬不同格式的時間戳
    test_timestamps = [
        "2025-07-14 07:39:38",  # 資料庫格式
        "2025-07-14T07:39:38",  # ISO格式（無時區）
        "2025-07-14T07:39:38Z", # ISO格式（UTC）
        "2025-07-14T07:39:38+00:00", # ISO格式（明確UTC）
    ]

    taipei_tz = pytz.timezone('Asia/Taipei')
    expected_result = "2025-07-14 15:39:38"  # 所有都應該轉換為這個台北時間

    for timestamp_str in test_timestamps:
        try:
            print(f"\n📅 測試時間戳: {timestamp_str}")

            # 使用修復後的邏輯
            if 'T' in timestamp_str and ('+' in timestamp_str or 'Z' in timestamp_str):
                # ISO格式時間且包含時區資訊
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                print(f"   ISO+時區轉換: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            elif 'T' in timestamp_str:
                # ISO格式時間但無時區資訊，假設為UTC
                timestamp = datetime.fromisoformat(timestamp_str).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                print(f"   ISO無時區轉換: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                # 一般格式時間，假設資料庫時間是UTC時間（無時區資訊）
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                print(f"   一般格式轉換: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

            result = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            if result == expected_result:
                print(f"   ✅ 轉換正確！")
            else:
                print(f"   ❌ 轉換錯誤！期望: {expected_result}, 實際: {result}")

        except Exception as e:
            print(f"   ❌ 轉換失敗: {str(e)}")

def main():
    """主函數"""
    print("🧪 開始調試API回應和時間轉換...\n")
    
    # 測試時間轉換邏輯
    test_time_conversion_logic()
    
    # 嘗試調試API（可能需要認證）
    print("\n" + "="*50)
    print("注意：API調試需要有效的認證token")
    print("如果需要調試實際API，請先登入系統獲取token")
    print("="*50)
    
    return 0

if __name__ == "__main__":
    exit(main())

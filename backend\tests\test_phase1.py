"""
第一階段測試 - 基礎架構與認證系統
"""

import os
import sys
import sqlite3
import pytest
from datetime import datetime
import time

# 添加專案路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 測試用環境變數設定
os.environ.update({
    "DATABASE_URL": "sqlite:///./database/test_cba_personal_data.db",
    "ENCRYPTION_KEY": "dGVzdGVuY3J5cHRpb25rZXkzMmJ5dGVzbGVuZ3Rob2s=",  # 32字節的base64編碼: testencryptionkey32byteslengthok
    "JWT_SECRET_KEY": "test-jwt-secret-key-for-testing-only",
    "SSO_CLIENT_ID": "",  # 測試時SSO配置為空
    "SSO_CLIENT_SECRET": "",
    "SSO_AUTHORIZATION_URL": "",
    "SSO_TOKEN_URL": "",
    "SSO_USER_INFO_URL": "",
})

from app.models.database import engine, Base, init_database, drop_tables, SessionLocal
from app.utils.encryption import encrypt_id_number, decrypt_id_number, verify_encryption_integrity
from app.utils.validation import validate_id_number, validate_name
from app.utils.jwt_auth import create_access_token, decode_token, verify_token
from app.utils.audit import create_audit_log, AuditAction
from app.models.user import User, Role, Department
from app.models.personal_data import PersonalData
from app.models.audit_log import AuditLog


class TestPhase1Infrastructure:
    """第一階段基礎架構測試"""
    
    @classmethod
    def setup_class(cls):
        """測試類別設定"""
        # 確保資料庫目錄存在
        os.makedirs("./database", exist_ok=True)
        
        # 清理測試資料庫，重試機制處理檔案鎖定問題
        test_db_path = "./database/test_cba_personal_data.db"
        if os.path.exists(test_db_path):
            try:
                os.remove(test_db_path)
            except PermissionError:
                # 如果檔案被鎖定，稍等再試
                time.sleep(1)
                try:
                    os.remove(test_db_path)
                except PermissionError:
                    # 如果仍然無法刪除，先不管，繼續測試
                    pass
        
        # 初始化測試資料庫
        init_database()
    
    @classmethod
    def teardown_class(cls):
        """測試類別清理"""
        # 嘗試清理測試資料庫，但不強制
        test_db_path = "./database/test_cba_personal_data.db"
        if os.path.exists(test_db_path):
            try:
                os.remove(test_db_path)
            except PermissionError:
                # 如果檔案被鎖定，忽略清理錯誤
                pass
    
    def test_1_1_project_structure(self):
        """1.1 專案目錄結構驗證"""
        # 從backend目錄的角度檢查相對路徑
        expected_dirs = [
            "app/api",
            "app/core", 
            "app/models",
            "app/services",
            "app/utils",
            "tests",
            "../frontend/pages",
            "../frontend/components",
            "../frontend/utils",
            "../database",
            "../docs"
        ]
        
        for dir_path in expected_dirs:
            assert os.path.exists(dir_path), f"目錄 {dir_path} 不存在"
    
    def test_1_2_dependencies(self):
        """1.2 依賴套件安裝測試"""
        # 檢查重要模組是否可以導入
        try:
            import fastapi
            import sqlalchemy
            import cryptography
            import jose
            import httpx
            assert True
        except ImportError as e:
            pytest.fail(f"依賴套件導入失敗: {e}")
    
    def test_1_3_environment_config(self):
        """1.3 環境設定檔測試"""
        from app.core.config import settings
        
        # 檢查重要設定是否載入
        assert settings.APP_NAME is not None
        assert settings.DATABASE_URL is not None
        assert settings.JWT_SECRET_KEY is not None
        assert settings.ENCRYPTION_KEY is not None
    
    def test_2_1_database_connection(self):
        """2.1 資料庫連接測試"""
        conn = sqlite3.connect('./database/test_cba_personal_data.db')
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        assert result[0] == 1
        conn.close()
    
    def test_2_2_database_tables(self):
        """2.2 資料表結構建立測試"""
        db = SessionLocal()
        try:
            # 檢查所有表是否存在
            tables = [
                "departments", "users", "roles", "user_roles", 
                "personal_data", "audit_logs"
            ]
            
            from sqlalchemy import text
            for table in tables:
                # 使用參數化查詢以避免SQL注入風險
                result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table_name"), {"table_name": table})
                assert result.fetchone() is not None, f"表 {table} 不存在"
        finally:
            db.close()
    
    def test_2_3_foreign_key_constraints(self):
        """2.3 資料表關聯測試"""
        db = SessionLocal()
        try:
            # 檢查預設資料是否正確插入
            roles = db.query(Role).all()
            assert len(roles) >= 3, "預設角色未正確建立"
            
            departments = db.query(Department).all()
            assert len(departments) >= 3, "預設部門未正確建立"
            
        finally:
            db.close()
    
    def test_3_1_jwt_token_generation(self):
        """3.1 JWT Token生成測試"""
        user_data = {"user_id": 1, "username": "test_user"}
        token = create_access_token(user_data)
        
        # 驗證Token格式
        assert len(token.split('.')) == 3, "JWT Token格式錯誤"
        
        # 驗證Token內容
        payload = decode_token(token)
        assert payload["user_id"] == 1
        assert payload["username"] == "test_user"
        assert payload["type"] == "access_token"
    
    def test_3_2_jwt_token_verification(self):
        """3.2 JWT Token驗證測試"""
        user_data = {"user_id": 1, "username": "test_user"}
        token = create_access_token(user_data)
        
        # 有效Token驗證
        assert verify_token(token) == True
        
        # 無效Token驗證
        assert verify_token("invalid.token.here") == False
        assert verify_token("") == False
    
    def test_3_3_permission_check(self):
        """3.3 權限檢查測試"""
        db = SessionLocal()
        try:
            # 建立測試用戶和角色
            general_role = db.query(Role).filter(Role.name == "general").first()
            admin_role = db.query(Role).filter(Role.name == "admin").first()
            
            assert general_role is not None, "一般角色不存在"
            assert admin_role is not None, "管理員角色不存在"
            
            # 檢查角色權限
            import json
            general_permissions = json.loads(general_role.permissions)
            admin_permissions = json.loads(admin_role.permissions)
            
            assert "CREATE_PERSONAL_DATA" in general_permissions
            assert "READ_ALL_DATA" not in general_permissions
            assert "MANAGE_ADMIN" in admin_permissions
            
        finally:
            db.close()
    
    def test_5_1_encryption_decryption(self):
        """5.1 加密工具測試"""
        original_text = "A123456789"
        
        # 測試加密解密正確性
        encrypted_data = encrypt_id_number(original_text)
        decrypted_text = decrypt_id_number(encrypted_data)
        
        assert decrypted_text == original_text
        assert encrypted_data != original_text
        
        # 測試加密完整性
        assert verify_encryption_integrity(original_text)
    
    def test_5_2_validation_tools(self):
        """5.2 驗證工具測試"""
        # 有效身分證字號（使用已知有效的A123456789）
        assert validate_id_number("A123456789") == True
        
        # 無效身分證字號
        assert validate_id_number("123456789") == False
        assert validate_id_number("A12345678") == False
        assert validate_id_number("A1234567890") == False
        assert validate_id_number("") == False
        
        # 姓名驗證
        assert validate_name("張三") == True
        assert validate_name("John Doe") == True
        assert validate_name("") == False
    
    def test_5_3_audit_logging(self):
        """5.3 審計日誌工具測試"""
        db = SessionLocal()
        try:
            log_entry = create_audit_log(
                db=db,
                user_id=1,
                action=AuditAction.CREATE,
                table_name="personal_data",
                record_id=1,
                new_values={"name": "測試用戶"}
            )
            
            assert log_entry.user_id == 1
            assert log_entry.action == "CREATE"
            assert log_entry.table_name == "personal_data"
            assert log_entry.timestamp is not None
            
        finally:
            db.close()


def run_phase1_tests():
    """執行第一階段測試"""
    print("=" * 80)
    print("第一階段測試執行 - 基礎架構與認證系統")
    print("=" * 80)
    
    # 使用pytest執行測試
    test_result = pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-x"  # 遇到第一個失敗就停止
    ])
    
    return test_result == 0


if __name__ == "__main__":
    success = run_phase1_tests()
    
    if success:
        print("\n" + "=" * 80)
        print("✅ 第一階段測試全部通過！")
        print("基礎架構與認證系統測試成功完成")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ 第一階段測試失敗！")
        print("請檢查上述錯誤並修正後重新測試")
        print("=" * 80) 
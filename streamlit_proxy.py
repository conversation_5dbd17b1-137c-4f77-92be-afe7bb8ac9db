#!/usr/bin/env python3
"""
CBA受款人資料搜集系統 - Streamlit專用代理服務器
專門處理Streamlit的WebSocket和靜態資源代理問題
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
import subprocess
import time

try:
    import aiohttp
    from aiohttp import web, ClientSession
    from aiohttp_proxy import ProxyConnector
except ImportError:
    print("❌ 錯誤: 缺少 aiohttp 套件")
    print("請執行: pip install aiohttp aiohttp-proxy")
    sys.exit(1)

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StreamlitProxy:
    """Streamlit專用代理服務器"""
    
    def __init__(self):
        self.app = web.Application()
        self.setup_routes()
        self.services = {}
        self.running = False
        
        # 服務配置
        self.backend_url = "http://localhost:8080"
        self.frontend_url = "http://localhost:8501"
        
        # 子服務配置
        self.service_configs = {
            "backend": {
                "name": "後端API",
                "port": 8080,
                "cwd": Path(__file__).parent / "backend",
                "command": ["uv", "run", "uvicorn", "main:app", "--host", "127.0.0.1", "--port", "8080"]
            },
            "frontend": {
                "name": "前端UI",
                "port": 8501,
                "cwd": Path(__file__).parent / "frontend",
                "command": ["streamlit", "run", "main.py", "--server.port", "8501", "--server.address", "127.0.0.1", "--server.headless", "true"]
            }
        }
    
    def setup_routes(self):
        """設定路由"""
        # 健康檢查
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/status', self.status_check)
        
        # API代理 - 直接轉發到後端
        self.app.router.add_route('*', '/api/{path:.*}', self.proxy_api)
        
        # 所有其他請求轉發到Streamlit
        self.app.router.add_route('*', '/{path:.*}', self.proxy_streamlit)
    
    async def health_check(self, request):
        """健康檢查端點"""
        status = {
            "status": "healthy",
            "service": "CBA Streamlit代理服務器",
            "version": "1.0.0",
            "backend": self.backend_url,
            "frontend": self.frontend_url
        }
        return web.json_response(status)
    
    async def status_check(self, request):
        """簡單狀態檢查"""
        return web.Response(text="OK")
    
    async def proxy_api(self, request):
        """代理API請求到後端"""
        # 移除 /api 前綴
        path = request.path_qs
        if path.startswith('/api'):
            path = path[4:]
        if not path.startswith('/'):
            path = '/' + path
        
        target_url = f"{self.backend_url}{path}"
        
        try:
            headers = dict(request.headers)
            headers.pop('host', None)
            headers.pop('content-length', None)
            
            data = await request.read() if request.can_read_body else None
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    data=data,
                    params=request.query
                ) as resp:
                    body = await resp.read()
                    
                    response_headers = {}
                    for key, value in resp.headers.items():
                        if key.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                            response_headers[key] = value
                    
                    return web.Response(
                        body=body,
                        status=resp.status,
                        headers=response_headers
                    )
        
        except Exception as e:
            logger.error(f"API代理失敗: {e}")
            return web.json_response(
                {"error": "後端API不可用", "detail": str(e)},
                status=502
            )
    
    async def proxy_streamlit(self, request):
        """代理請求到Streamlit"""
        target_url = f"{self.frontend_url}{request.path_qs}"
        
        try:
            headers = dict(request.headers)
            headers.pop('host', None)
            headers.pop('content-length', None)
            
            # 添加必要的標頭
            headers['X-Forwarded-For'] = request.remote
            headers['X-Forwarded-Proto'] = 'http'
            headers['X-Forwarded-Host'] = request.host
            
            data = await request.read() if request.can_read_body else None
            
            # 處理WebSocket升級請求
            if request.headers.get('upgrade', '').lower() == 'websocket':
                return await self.handle_websocket_upgrade(request, target_url)
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                async with session.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    data=data,
                    params=request.query
                ) as resp:
                    body = await resp.read()
                    
                    response_headers = {}
                    for key, value in resp.headers.items():
                        if key.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                            response_headers[key] = value
                    
                    # 添加CORS標頭
                    response_headers['Access-Control-Allow-Origin'] = '*'
                    response_headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
                    response_headers['Access-Control-Allow-Headers'] = '*'
                    
                    return web.Response(
                        body=body,
                        status=resp.status,
                        headers=response_headers
                    )
        
        except Exception as e:
            logger.error(f"Streamlit代理失敗: {e}")
            return web.json_response(
                {"error": "前端服務不可用", "detail": str(e)},
                status=502
            )
    
    async def handle_websocket_upgrade(self, request, target_url):
        """處理WebSocket升級（簡化版本）"""
        # 對於WebSocket，我們返回一個重定向到直接的Streamlit服務
        return web.Response(
            status=302,
            headers={'Location': target_url}
        )
    
    def start_service(self, service_name):
        """啟動子服務"""
        config = self.service_configs[service_name]
        logger.info(f"🚀 啟動 {config['name']}...")
        
        try:
            process = subprocess.Popen(
                config["command"],
                cwd=config["cwd"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services[service_name] = process
            time.sleep(3)
            
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {config['name']} 啟動失敗")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
            
            logger.info(f"✅ {config['name']} 已啟動在埠 {config['port']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 啟動 {config['name']} 時發生錯誤: {e}")
            return False
    
    def stop_service(self, service_name):
        """停止子服務"""
        if service_name in self.services:
            process = self.services[service_name]
            config = self.service_configs[service_name]
            
            logger.info(f"🛑 停止 {config['name']}...")
            
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️ {config['name']} 未在10秒內停止，強制終止")
                process.kill()
                process.wait()
            
            del self.services[service_name]
            logger.info(f"✅ {config['name']} 已停止")
    
    def start_all_services(self):
        """啟動所有子服務"""
        logger.info("🚀 啟動所有子服務...")
        
        service_order = ["backend", "frontend"]
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ 啟動 {self.service_configs[service_name]['name']} 失敗")
                self.stop_all_services()
                return False
        
        logger.info("🎉 所有子服務已啟動！")
        return True
    
    def stop_all_services(self):
        """停止所有子服務"""
        logger.info("🛑 停止所有子服務...")
        
        service_order = ["frontend", "backend"]
        
        for service_name in service_order:
            if service_name in self.services:
                self.stop_service(service_name)
    
    async def start_server(self):
        """啟動代理服務器"""
        # 啟動子服務
        if not self.start_all_services():
            return False
        
        # 啟動代理服務器
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, '0.0.0.0', 80)
        await site.start()
        
        self.running = True
        logger.info("🌐 Streamlit代理服務器已啟動在 http://localhost:80")
        logger.info("📋 服務路由:")
        logger.info("  • 前端UI: http://localhost:80/")
        logger.info("  • 後端API: http://localhost:80/api/")
        logger.info("  • 健康檢查: http://localhost:80/health")
        logger.info("  • 直接存取前端: http://localhost:8501")
        logger.info("  • 直接存取後端: http://localhost:8080")
        
        return True
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        logger.info("📡 收到停止信號，正在關閉系統...")
        self.stop_all_services()
        self.running = False
        sys.exit(0)

async def main():
    """主函數"""
    print("🏢 CBA受款人資料搜集系統 - Streamlit專用代理服務器")
    print("=" * 60)
    
    proxy = StreamlitProxy()
    
    # 註冊信號處理器
    signal.signal(signal.SIGINT, proxy.signal_handler)
    signal.signal(signal.SIGTERM, proxy.signal_handler)
    
    try:
        # 啟動服務器
        if await proxy.start_server():
            # 保持運行
            while proxy.running:
                await asyncio.sleep(1)
        else:
            logger.error("❌ 服務器啟動失敗")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("📡 收到中斷信號")
    finally:
        proxy.stop_all_services()

if __name__ == "__main__":
    asyncio.run(main())

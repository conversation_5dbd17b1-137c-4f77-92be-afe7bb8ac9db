#!/usr/bin/env python3
"""
JWT認證調試腳本
"""

import os
import sys
import base64

# 設定測試環境變數
os.environ["ENCRYPTION_KEY"] = base64.b64encode(b"test-encryption-key-32-bytes-abc").decode('utf-8')
os.environ["JWT_SECRET_KEY"] = "test-jwt-secret-key-for-testing-purposes"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"  # 使用內存資料庫

# 添加專案根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from main import app
from app.utils.jwt_auth import create_access_token, decode_token
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.database import Base
from app.models.user import User, Role, UserRole, Department
from app.utils.jwt_auth import hash_password

# 建立內存測試資料庫
test_engine = create_engine("sqlite:///:memory:")
Base.metadata.create_all(bind=test_engine)
TestingSessionLocal = sessionmaker(bind=test_engine)

def setup_test_data():
    """建立測試資料"""
    # 刪除現有的測試資料庫檔案
    db_path = "./test.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # 重新建立資料庫表
    Base.metadata.create_all(bind=test_engine)
    
    db = TestingSessionLocal()
    try:
        # 建立部門
        dept = Department(name="測試部門", description="測試用部門")
        db.add(dept)
        db.commit()
        
        # 建立角色
        role = Role(name="general", description="一般用戶")
        db.add(role)
        db.commit()
        
        # 建立用戶
        user = User(
            username="test_user",
            hashed_password=hash_password("password123"),
            full_name="測試用戶",
            department_id=dept.id,
            is_active=True
        )
        db.add(user)
        db.commit()
        
        # 分配角色
        user_role = UserRole(user_id=user.id, role_id=role.id)
        db.add(user_role)
        db.commit()
        
        return user.id
        
    finally:
        db.close()

def test_jwt_flow():
    """測試JWT流程"""
    print("=== JWT認證調試 ===")
    
    # 建立測試資料
    user_id = setup_test_data()
    print(f"建立測試用戶，ID: {user_id}")
    
    # 測試登入
    client = TestClient(app)
    
    print("\n1. 測試登入...")
    login_response = client.post("/api/v1/auth/login-json", json={
        "username": "test_user",
        "password": "password123"
    })
    
    print(f"登入回應狀態碼: {login_response.status_code}")
    if login_response.status_code == 200:
        login_data = login_response.json()
        token = login_data.get("access_token")
        print(f"取得Token: {token[:50]}...")
        
        # 測試Token解碼
        print("\n2. 測試Token解碼...")
        try:
            payload = decode_token(token)
            print(f"Token payload: {payload}")
        except Exception as e:
            print(f"Token解碼失敗: {e}")
        
        # 測試直接資料庫查詢
        print("\n3. 測試直接資料庫查詢...")
        db = TestingSessionLocal()
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                print(f"找到用戶: {user.username}, 啟用狀態: {user.is_active}")
                print(f"用戶角色數量: {len(user.roles)}")
                if user.roles:
                    for role in user.roles:
                        print(f"  - 角色: {role.name}")
                        print(f"  - 權限: {role.permission_names}")
                print(f"用戶所有權限: {user.permissions}")
            else:
                print("找不到用戶")
        except Exception as e:
            print(f"資料庫查詢失敗: {e}")
        finally:
            db.close()
        
        # 測試JWT驗證函數
        print("\n4. 測試JWT驗證函數...")
        try:
            from app.utils.jwt_auth import verify_token
            is_valid = verify_token(token)
            print(f"Token驗證結果: {is_valid}")
        except Exception as e:
            print(f"Token驗證失敗: {e}")
        
        # 測試認證API
        print("\n5. 測試認證API...")
        headers = {"Authorization": f"Bearer {token}"}
        me_response = client.get("/api/v1/auth/me", headers=headers)
        print(f"認證API回應狀態碼: {me_response.status_code}")
        if me_response.status_code != 200:
            print(f"認證API錯誤: {me_response.text}")
        else:
            print(f"認證API回應: {me_response.json()}")
    else:
        print(f"登入失敗: {login_response.text}")

if __name__ == "__main__":
    test_jwt_flow() 
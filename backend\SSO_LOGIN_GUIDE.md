# SSO 登入使用指南

## 🔑 如何使用 SSO 登入

### 1. 直接訪問 SSO 登入端點
```
http://localhost:8080/api/v1/auth/sso_login?ssoToken1=your_token
```

### 2. 在前端應用中使用 SSO 登入
```python
from utils.auth_manager import AuthManager

# 觸發 SSO 登入
AuthManager.sso_login()
```

### 3. 測試用 SSO Token
```
cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956
```

## 📋 完整登入流程

### 步驟 1: 用戶點擊 SSO 登入
- 前端顯示「🔑 SSO登入」按鈕
- 用戶點擊按鈕觸發 `AuthManager.sso_login()`

### 步驟 2: 重導向到 SSO 認證
- 前端重導向到後端 SSO 登入端點
- 後端呼叫 SOAP SSO 服務驗證 token

### 步驟 3: 認證成功處理
- 後端解析用戶資訊並建立/更新用戶記錄
- 生成 JWT token
- 重導向到前端應用並包含 token 參數

### 步驟 4: 前端接收登入狀態
- 前端檢查 URL 中的 token 參數
- 設置登入狀態並獲取用戶資訊
- 自動導航到資料概覽頁面

### 步驟 5: 用戶進入系統首頁
- 顯示個人資料搜集系統主頁
- 顯示用戶資訊和權限相關功能
- 提供完整的系統功能訪問

## 🧪 測試 SSO 登入

### 使用測試腳本
```bash
cd backend
python test_full_sso_flow.py
```

### 直接測試 URL
```
http://localhost:8080/api/v1/auth/sso_login?ssoToken1=cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956
```

## 🔧 系統組件

### 後端組件
- **SSO 認證器**: `backend/app/core/sso.py`
- **認證端點**: `backend/app/api/v1/auth.py`
- **JWT 工具**: `backend/app/utils/jwt_auth.py`

### 前端組件
- **認證管理器**: `frontend/utils/auth_manager.py`
- **API 客戶端**: `frontend/utils/api_client.py`
- **主應用**: `frontend/main.py`

### SSO 服務
- **模擬 SSO 服務**: `odc_sso/sso_service.py`
- **服務端點**: `http://localhost:8000`

## 📊 系統狀態檢查

### 檢查後端服務
```bash
curl http://localhost:8080/health
```

### 檢查 SSO 服務
```bash
curl http://localhost:8000/
```

### 檢查前端應用
```bash
# 在瀏覽器中訪問
http://localhost:8501
```

## 🎯 成功指標

### ✅ 登入成功後應該看到：
1. 瀏覽器重導向到 `http://localhost:8501`
2. 顯示「🎉 SSO登入成功！歡迎使用個人資料搜集系統」
3. 自動進入資料概覽頁面
4. 側邊欄顯示用戶資訊和導航選單
5. 可以訪問系統所有功能

### ❌ 如果登入失敗：
1. 檢查所有服務是否正常運行
2. 檢查 SSO token 是否有效
3. 檢查網路連接
4. 查看系統日誌獲取詳細錯誤資訊

## 🔐 安全注意事項

1. **Token 傳遞**: JWT token 會短暫出現在 URL 中，請確保在生產環境中使用 HTTPS
2. **Token 過期**: JWT token 有時間限制，過期後需要重新登入
3. **Cookie 備用**: 系統同時設置 HttpOnly Cookie 作為備用認證方式
4. **清理 URL**: 前端會自動清理 URL 中的 token 參數以避免外洩

## 🚀 部署建議

### 生產環境配置
1. 使用真實的 SSO 服務端點
2. 設置正確的前端應用 URL
3. 啟用 HTTPS 和安全 Cookie
4. 配置適當的 CORS 政策
5. 設置合理的 Token 過期時間 
/**
 * Vue.js 應用程式入口
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 導入全域樣式
import '@/assets/styles/main.scss'

// 創建Vue應用實例
const app = createApp(App)

// 創建Pinia實例
const pinia = createPinia()

// 註冊Element Plus圖標
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 全域錯誤處理
app.config.errorHandler = (err, vm, info) => {
  console.error('全域錯誤:', err, info)
  
  // 在生產環境中可以發送錯誤到監控服務
  if (import.meta.env.PROD) {
    // 發送錯誤報告
    console.error('生產環境錯誤:', { err, info })
  }
}

// 全域警告處理
app.config.warnHandler = (msg, vm, trace) => {
  if (import.meta.env.DEV) {
    console.warn('Vue警告:', msg, trace)
  }
}

// 初始化應用
async function initApp() {
  try {
    // 初始化認證狀態
    const authStore = useAuthStore()
    await authStore.initAuth()
    
    // 掛載應用
    app.mount('#app')
    
    console.log('🚀 CBA受款人資料搜集系統已啟動')
    console.log('📦 Vue版本:', app.version)
    console.log('🌍 環境:', import.meta.env.MODE)
    
  } catch (error) {
    console.error('應用初始化失敗:', error)
    
    // 即使初始化失敗也要掛載應用，讓用戶可以重新登入
    app.mount('#app')
  }
}

// 啟動應用
initApp()

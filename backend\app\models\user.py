"""
用戶相關資料模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base


class Department(Base):
    """部門模型"""
    __tablename__ = "departments"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True, default="", server_default="")
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # 關聯
    users = relationship("User", back_populates="department")
    payee_data = relationship("PayeeData", back_populates="created_by_dept")


class Role(Base):
    """角色模型"""
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text)
    permissions = Column(Text)  # JSON字串格式存儲權限列表（向後兼容）
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # 關聯
    user_roles = relationship("UserRole", back_populates="role")
    role_permissions = relationship("RolePermission", back_populates="role")
    
    @property
    def permission_objects(self):
        """取得角色的權限物件列表"""
        return [rp.permission for rp in self.role_permissions if rp.permission.is_active]
    
    @property
    def permission_names(self):
        """取得角色的權限名稱列表"""
        # 優先使用新的權限關聯，但確保關聯中真的有數據
        permission_objects = self.permission_objects
        if permission_objects and len(permission_objects) > 0:
            return [p.name for p in permission_objects]
        
        # 向後兼容：使用JSON格式的權限
        if self.permissions:
            try:
                import json
                return json.loads(self.permissions)
            except json.JSONDecodeError:
                return []
        
        return []


class User(Base):
    """用戶模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)  # 雜湊後的密碼
    full_name = Column(String(100), nullable=False)
    department_id = Column(Integer, ForeignKey("departments.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # 關聯
    department = relationship("Department", back_populates="users")
    user_roles = relationship("UserRole", back_populates="user")
    payee_data = relationship("PayeeData", back_populates="created_by_user")
    audit_logs = relationship("AuditLog", back_populates="user")
    
    @property
    def roles(self):
        """取得用戶角色列表"""
        return [ur.role for ur in self.user_roles]
    
    @property
    def permissions(self):
        """取得用戶所有權限"""
        all_permissions = set()
        for role in self.roles:
            # 使用新的權限名稱列表
            all_permissions.update(role.permission_names)
        return list(all_permissions)
    
    def has_permission(self, permission: str) -> bool:
        """檢查用戶是否具有特定權限"""
        return permission in self.permissions
    
    def has_role(self, role_name: str) -> bool:
        """檢查用戶是否具有特定角色"""
        return any(role.name == role_name for role in self.roles)


class UserRole(Base):
    """用戶角色關聯模型"""
    __tablename__ = "user_roles"
    
    user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    role_id = Column(Integer, ForeignKey("roles.id"), primary_key=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 關聯
    user = relationship("User", back_populates="user_roles")
    role = relationship("Role", back_populates="user_roles")


class Permission(Base):
    """權限模型"""
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    category = Column(String(50))  # 權限分類
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # 關聯
    role_permissions = relationship("RolePermission", back_populates="permission")


class RolePermission(Base):
    """角色權限關聯模型"""
    __tablename__ = "role_permissions"
    
    role_id = Column(Integer, ForeignKey("roles.id"), primary_key=True)
    permission_id = Column(Integer, ForeignKey("permissions.id"), primary_key=True)
    granted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 關聯
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions") 
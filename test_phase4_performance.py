#!/usr/bin/env python3
"""
CBA個人資料搜集系統 - 第四階段效能測試

測試範圍：
1. 負載測試（正常負載、峰值負載）
2. 壓力測試（極限壓力、資料庫壓力）
3. 效能基準測試（API回應時間、資料庫查詢效能）
"""

import asyncio
import httpx
import time
import statistics
import threading
import concurrent.futures
import psutil
import sqlite3
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 配置
BASE_URL = "http://localhost:8000"
TEST_DATABASE_PATH = "./database/test_phase4_performance.db"

class PerformanceTestRunner:
    """效能測試執行器"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_results = []
        self.test_tokens = {}
        
    def setup_test_environment(self):
        """設置測試環境"""
        print("🔧 設置效能測試環境...")
        
        # 創建測試用戶Token
        self.test_tokens = {
            "general_user": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test_token",
            "global_user": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test_token_global",
            "admin_user": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test_token_admin"
        }
        
        print("✅ 測試環境設置完成")
    
    def measure_response_time(self, func, *args, **kwargs) -> Tuple[float, Any]:
        """測量函數執行時間"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return (end_time - start_time) * 1000, result  # 返回毫秒
    
    # ===========================================
    # 1. 負載測試
    # ===========================================
    
    def test_01_normal_load(self):
        """測試3.1.1：正常負載測試"""
        print("\n=== 測試3.1.1：正常負載測試 ===")
        
        concurrent_users = 10
        test_duration = 60  # 60秒
        expected_response_time = 1000  # 1秒
        
        print(f"併發用戶數: {concurrent_users}")
        print(f"測試時間: {test_duration}秒")
        print(f"預期回應時間: <{expected_response_time}ms")
        
        results = []
        start_time = time.time()
        
        def user_simulation(user_id):
            """模擬用戶操作"""
            user_results = []
            headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
            
            while time.time() - start_time < test_duration:
                try:
                    # 模擬API呼叫
                    response_time, response = self.measure_response_time(
                        self._make_request, "GET", "/api/v1/personal-data", headers=headers
                    )
                    
                    user_results.append({
                        "user_id": user_id,
                        "response_time": response_time,
                        "status_code": response.status_code if response else 500,
                        "timestamp": time.time()
                    })
                    
                    time.sleep(0.1)  # 模擬用戶思考時間
                    
                except Exception as e:
                    user_results.append({
                        "user_id": user_id,
                        "response_time": None,
                        "status_code": 500,
                        "error": str(e),
                        "timestamp": time.time()
                    })
            
            return user_results
        
        # 啟動併發用戶
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(user_simulation, i) for i in range(concurrent_users)]
            
            for future in concurrent.futures.as_completed(futures):
                results.extend(future.result())
        
        # 分析結果
        valid_results = [r for r in results if r["response_time"] is not None]
        response_times = [r["response_time"] for r in valid_results]
        success_count = len([r for r in valid_results if r["status_code"] == 200])
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            
            print(f"總請求數: {len(valid_results)}")
            print(f"成功請求數: {success_count}")
            print(f"成功率: {success_count/len(valid_results)*100:.1f}%")
            print(f"平均回應時間: {avg_response_time:.2f}ms")
            print(f"最大回應時間: {max_response_time:.2f}ms")
            print(f"最小回應時間: {min_response_time:.2f}ms")
            print(f"95th百分位數: {p95_response_time:.2f}ms")
            
            # 評估標準
            if avg_response_time < expected_response_time and success_count/len(valid_results) > 0.95:
                print("✅ 正常負載測試通過")
                return True
        
        print("❌ 正常負載測試失敗")
        return False
    
    def test_02_peak_load(self):
        """測試3.1.2：峰值負載測試"""
        print("\n=== 測試3.1.2：峰值負載測試 ===")
        
        concurrent_users = 50
        test_duration = 30  # 30秒
        expected_response_time = 2000  # 2秒
        
        print(f"併發用戶數: {concurrent_users}")
        print(f"測試時間: {test_duration}秒")
        print(f"預期回應時間: <{expected_response_time}ms")
        
        results = []
        start_time = time.time()
        
        def peak_user_simulation(user_id):
            """模擬峰值用戶操作"""
            user_results = []
            headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
            
            while time.time() - start_time < test_duration:
                try:
                    # 混合API呼叫
                    operations = [
                        ("GET", "/api/v1/personal-data"),
                        ("POST", "/api/v1/personal-data", {
                            "name": f"峰值測試用戶{user_id}",
                            "id_number": f"P{user_id:08d}",
                            "address": f"峰值測試地址{user_id}"
                        })
                    ]
                    
                    for method, endpoint, data in operations:
                        response_time, response = self.measure_response_time(
                            self._make_request, method, endpoint, headers=headers, json=data
                        )
                        
                        user_results.append({
                            "user_id": user_id,
                            "method": method,
                            "endpoint": endpoint,
                            "response_time": response_time,
                            "status_code": response.status_code if response else 500,
                            "timestamp": time.time()
                        })
                        
                        if len(operations) > 1:
                            time.sleep(0.05)  # 短暫間隔
                
                except Exception as e:
                    user_results.append({
                        "user_id": user_id,
                        "response_time": None,
                        "status_code": 500,
                        "error": str(e),
                        "timestamp": time.time()
                    })
            
            return user_results
        
        # 啟動峰值併發用戶
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(peak_user_simulation, i) for i in range(concurrent_users)]
            
            for future in concurrent.futures.as_completed(futures):
                results.extend(future.result())
        
        # 分析峰值負載結果
        valid_results = [r for r in results if r["response_time"] is not None]
        response_times = [r["response_time"] for r in valid_results]
        success_count = len([r for r in valid_results if r["status_code"] in [200, 201]])
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            
            print(f"總請求數: {len(valid_results)}")
            print(f"成功請求數: {success_count}")
            print(f"成功率: {success_count/len(valid_results)*100:.1f}%")
            print(f"平均回應時間: {avg_response_time:.2f}ms")
            print(f"最大回應時間: {max_response_time:.2f}ms")
            
            # 峰值負載評估標準（較寬鬆）
            if avg_response_time < expected_response_time and success_count/len(valid_results) > 0.8:
                print("✅ 峰值負載測試通過")
                return True
        
        print("❌ 峰值負載測試失敗")
        return False
    
    # ===========================================
    # 2. 壓力測試
    # ===========================================
    
    def test_03_stress_limit(self):
        """測試3.2.1：極限壓力測試"""
        print("\n=== 測試3.2.1：極限壓力測試 ===")
        
        print("逐步增加負載直到系統極限...")
        
        max_concurrent = 100
        step_size = 10
        step_duration = 10  # 每階段10秒
        
        for concurrent_users in range(step_size, max_concurrent + 1, step_size):
            print(f"\n📊 測試併發數: {concurrent_users}")
            
            results = []
            start_time = time.time()
            
            def stress_user_simulation(user_id):
                """壓力測試用戶模擬"""
                user_results = []
                headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
                
                while time.time() - start_time < step_duration:
                    try:
                        response_time, response = self.measure_response_time(
                            self._make_request, "GET", "/api/v1/personal-data", headers=headers
                        )
                        
                        user_results.append({
                            "response_time": response_time,
                            "status_code": response.status_code if response else 500
                        })
                        
                    except Exception as e:
                        user_results.append({
                            "response_time": None,
                            "status_code": 500,
                            "error": str(e)
                        })
                
                return user_results
            
            # 執行當前階段測試
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(stress_user_simulation, i) for i in range(concurrent_users)]
                
                for future in concurrent.futures.as_completed(futures):
                    results.extend(future.result())
            
            # 分析當前階段結果
            valid_results = [r for r in results if r["response_time"] is not None]
            if valid_results:
                success_rate = len([r for r in valid_results if r["status_code"] == 200]) / len(valid_results)
                avg_response_time = statistics.mean([r["response_time"] for r in valid_results])
                
                print(f"成功率: {success_rate*100:.1f}%")
                print(f"平均回應時間: {avg_response_time:.2f}ms")
                
                # 如果成功率低於50%或回應時間超過5秒，視為達到極限
                if success_rate < 0.5 or avg_response_time > 5000:
                    print(f"🚨 系統極限: {concurrent_users}併發用戶")
                    if concurrent_users >= 30:  # 如果能支持30以上併發，視為通過
                        print("✅ 極限壓力測試通過")
                        return True
                    else:
                        print("❌ 極限壓力測試失敗")
                        return False
        
        print("✅ 極限壓力測試通過（達到最大測試併發數）")
        return True
    
    def test_04_database_stress(self):
        """測試3.2.2：資料庫壓力測試"""
        print("\n=== 測試3.2.2：資料庫壓力測試 ===")
        
        # 測試大量資料庫操作
        operation_count = 1000
        batch_size = 50
        
        print(f"執行{operation_count}次資料庫操作，批次大小：{batch_size}")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        total_operations = 0
        successful_operations = 0
        total_time = 0
        
        for batch in range(0, operation_count, batch_size):
            batch_start = time.time()
            batch_success = 0
            
            for i in range(batch_size):
                if batch + i >= operation_count:
                    break
                
                try:
                    # 執行資料庫密集操作
                    personal_data = {
                        "name": f"資料庫壓力測試{batch + i}",
                        "id_number": f"D{batch + i:08d}",
                        "address": f"壓力測試地址{batch + i}",
                        "notes": f"資料庫壓力測試{batch + i}"
                    }
                    
                    response_time, response = self.measure_response_time(
                        self._make_request, "POST", "/api/v1/personal-data", 
                        headers=headers, json=personal_data
                    )
                    
                    if response and response.status_code == 201:
                        batch_success += 1
                        successful_operations += 1
                    
                    total_operations += 1
                    
                except Exception as e:
                    print(f"資料庫操作失敗: {str(e)}")
                    total_operations += 1
            
            batch_time = time.time() - batch_start
            total_time += batch_time
            
            print(f"批次 {batch//batch_size + 1}: {batch_success}/{batch_size} 成功，耗時 {batch_time:.2f}s")
        
        # 計算整體統計
        success_rate = successful_operations / total_operations
        avg_time_per_operation = total_time / total_operations * 1000  # 毫秒
        
        print(f"\n📊 資料庫壓力測試結果:")
        print(f"總操作數: {total_operations}")
        print(f"成功操作數: {successful_operations}")
        print(f"成功率: {success_rate*100:.1f}%")
        print(f"總耗時: {total_time:.2f}s")
        print(f"平均每操作時間: {avg_time_per_operation:.2f}ms")
        
        # 評估標準
        if success_rate > 0.9 and avg_time_per_operation < 1000:
            print("✅ 資料庫壓力測試通過")
            return True
        
        print("❌ 資料庫壓力測試失敗")
        return False
    
    # ===========================================
    # 3. 效能基準測試
    # ===========================================
    
    def test_05_api_response_time_benchmark(self):
        """測試3.3.1：API回應時間基準測試"""
        print("\n=== 測試3.3.1：API回應時間基準測試 ===")
        
        benchmarks = {
            "登入API": ("/api/v1/auth/me", "GET", None, 500),
            "查詢API": ("/api/v1/personal-data", "GET", None, 1000),
            "新增API": ("/api/v1/personal-data", "POST", {
                "name": "基準測試用戶",
                "id_number": "B123456789",
                "address": "基準測試地址"
            }, 1000),
            "複雜查詢": ("/api/v1/personal-data?search=test&limit=10&offset=0", "GET", None, 2000)
        }
        
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        all_passed = True
        
        for api_name, (endpoint, method, data, threshold) in benchmarks.items():
            print(f"\n📈 測試 {api_name}")
            
            # 執行多次測試
            response_times = []
            for i in range(10):
                try:
                    response_time, response = self.measure_response_time(
                        self._make_request, method, endpoint, headers=headers, json=data
                    )
                    
                    if response and response.status_code in [200, 201]:
                        response_times.append(response_time)
                    
                except Exception as e:
                    print(f"API測試失敗: {str(e)}")
            
            if response_times:
                avg_time = statistics.mean(response_times)
                max_time = max(response_times)
                min_time = min(response_times)
                
                print(f"平均回應時間: {avg_time:.2f}ms")
                print(f"最大回應時間: {max_time:.2f}ms")
                print(f"最小回應時間: {min_time:.2f}ms")
                print(f"基準值: <{threshold}ms")
                
                if avg_time <= threshold:
                    print(f"✅ {api_name} 基準測試通過")
                else:
                    print(f"❌ {api_name} 基準測試失敗")
                    all_passed = False
            else:
                print(f"❌ {api_name} 無法取得有效回應")
                all_passed = False
        
        if all_passed:
            print("\n✅ API回應時間基準測試全部通過")
            return True
        else:
            print("\n❌ 部分API回應時間基準測試失敗")
            return False
    
    def test_06_database_query_performance(self):
        """測試3.3.2：資料庫查詢效能測試"""
        print("\n=== 測試3.3.2：資料庫查詢效能測試 ===")
        
        # 預先準備測試資料
        print("📊 準備測試資料...")
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        # 新增100筆測試資料
        for i in range(100):
            test_data = {
                "name": f"查詢效能測試用戶{i}",
                "id_number": f"Q{i:08d}",
                "address": f"查詢效能測試地址{i}",
                "notes": f"查詢效能測試{i}"
            }
            
            try:
                self._make_request("POST", "/api/v1/personal-data", headers=headers, json=test_data)
            except:
                pass  # 忽略建立資料的錯誤
        
        print("🔍 執行查詢效能測試...")
        
        # 測試不同類型的查詢
        queries = {
            "簡單查詢": "/api/v1/personal-data?limit=10",
            "分頁查詢": "/api/v1/personal-data?limit=20&offset=10",
            "搜尋查詢": "/api/v1/personal-data?search=查詢效能&limit=10",
            "大量資料查詢": "/api/v1/personal-data?limit=100"
        }
        
        all_passed = True
        
        for query_name, endpoint in queries.items():
            print(f"\n📊 測試 {query_name}")
            
            query_times = []
            for i in range(5):
                try:
                    response_time, response = self.measure_response_time(
                        self._make_request, "GET", endpoint, headers=headers
                    )
                    
                    if response and response.status_code == 200:
                        query_times.append(response_time)
                
                except Exception as e:
                    print(f"查詢測試失敗: {str(e)}")
            
            if query_times:
                avg_time = statistics.mean(query_times)
                print(f"平均查詢時間: {avg_time:.2f}ms")
                
                # 查詢效能基準：500ms以內
                if avg_time <= 500:
                    print(f"✅ {query_name} 效能測試通過")
                else:
                    print(f"❌ {query_name} 效能測試失敗")
                    all_passed = False
            else:
                print(f"❌ {query_name} 無法取得有效查詢結果")
                all_passed = False
        
        if all_passed:
            print("\n✅ 資料庫查詢效能測試全部通過")
            return True
        else:
            print("\n❌ 部分資料庫查詢效能測試失敗")
            return False
    
    # ===========================================
    # 4. 系統資源監控測試
    # ===========================================
    
    def test_07_memory_usage_monitoring(self):
        """測試記憶體使用監控"""
        print("\n=== 測試記憶體使用監控 ===")
        
        # 監控記憶體使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始記憶體使用: {initial_memory:.2f} MB")
        
        # 執行記憶體密集操作
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        for i in range(200):
            try:
                test_data = {
                    "name": f"記憶體測試用戶{i}",
                    "id_number": f"M{i:08d}",
                    "address": f"記憶體測試地址{i}" * 10,  # 較大的資料
                    "notes": f"記憶體使用監控測試{i}" * 5
                }
                
                self._make_request("POST", "/api/v1/personal-data", headers=headers, json=test_data)
                
                if i % 50 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_growth = current_memory - initial_memory
                    print(f"第{i}次操作後記憶體: {current_memory:.2f} MB (增長: {memory_growth:.2f} MB)")
            
            except Exception as e:
                pass  # 忽略個別操作失敗
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_growth = final_memory - initial_memory
        
        print(f"最終記憶體使用: {final_memory:.2f} MB")
        print(f"總記憶體增長: {total_growth:.2f} MB")
        
        # 記憶體使用評估
        if total_growth < 200:  # 增長少於200MB
            print("✅ 記憶體使用監控測試通過")
            return True
        else:
            print("❌ 記憶體使用過多，可能存在記憶體洩漏")
            return False
    
    def test_08_cpu_usage_monitoring(self):
        """測試CPU使用監控"""
        print("\n=== 測試CPU使用監控 ===")
        
        # 監控CPU使用
        cpu_percentages = []
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        start_time = time.time()
        operation_count = 0
        
        while time.time() - start_time < 30:  # 30秒測試
            try:
                # 執行CPU密集操作
                self._make_request("GET", "/api/v1/personal-data", headers=headers)
                operation_count += 1
                
                # 每10次操作檢查一次CPU
                if operation_count % 10 == 0:
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    cpu_percentages.append(cpu_percent)
                    print(f"第{operation_count}次操作後CPU使用率: {cpu_percent:.1f}%")
            
            except Exception as e:
                pass
        
        if cpu_percentages:
            avg_cpu = statistics.mean(cpu_percentages)
            max_cpu = max(cpu_percentages)
            
            print(f"平均CPU使用率: {avg_cpu:.1f}%")
            print(f"最大CPU使用率: {max_cpu:.1f}%")
            print(f"總操作數: {operation_count}")
            
            # CPU使用評估
            if avg_cpu < 80 and max_cpu < 95:  # 平均低於80%，峰值低於95%
                print("✅ CPU使用監控測試通過")
                return True
            else:
                print("❌ CPU使用率過高")
                return False
        else:
            print("❌ 無法取得CPU使用數據")
            return False
    
    # ===========================================
    # 輔助方法
    # ===========================================
    
    def _make_request(self, method: str, endpoint: str, headers=None, json=None, timeout=30):
        """發送HTTP請求"""
        try:
            import requests
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, timeout=timeout)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=json, timeout=timeout)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers, json=json, timeout=timeout)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers, timeout=timeout)
            else:
                return None
            
            return response
            
        except Exception as e:
            print(f"請求失敗: {str(e)}")
            return None
    
    # ===========================================
    # 測試執行主程式
    # ===========================================
    
    def run_all_performance_tests(self):
        """執行所有效能測試"""
        print("\n" + "="*60)
        print("⚡ 開始執行第四階段效能測試")
        print("="*60)
        
        # 設置測試環境
        self.setup_test_environment()
        
        test_methods = [
            self.test_01_normal_load,
            self.test_02_peak_load,
            self.test_03_stress_limit,
            self.test_04_database_stress,
            self.test_05_api_response_time_benchmark,
            self.test_06_database_query_performance,
            self.test_07_memory_usage_monitoring,
            self.test_08_cpu_usage_monitoring
        ]
        
        results = []
        for i, test_method in enumerate(test_methods, 1):
            try:
                print(f"\n⏱️  執行效能測試 {i}/{len(test_methods)}")
                result = test_method()
                results.append((f"效能測試{i:02d}", test_method.__name__, result))
            except Exception as e:
                print(f"效能測試{i:02d}執行異常: {str(e)}")
                results.append((f"效能測試{i:02d}", test_method.__name__, False))
        
        # 輸出測試結果摘要
        print("\n" + "="*60)
        print("📊 第四階段效能測試結果摘要")
        print("="*60)
        
        passed_count = 0
        failed_count = 0
        
        for test_id, test_name, result in results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_id}: {status} - {test_name}")
            
            if result:
                passed_count += 1
            else:
                failed_count += 1
        
        print(f"\n📈 效能測試統計:")
        print(f"通過: {passed_count}/{len(results)} ({passed_count/len(results)*100:.1f}%)")
        print(f"失敗: {failed_count}/{len(results)} ({failed_count/len(results)*100:.1f}%)")
        
        # 效能測試評估
        if passed_count == len(results):
            print("\n🎉 所有效能測試通過！系統效能良好。")
        elif passed_count >= len(results) * 0.8:
            print("\n⚠️  大部分效能測試通過，部分項目需要優化。")
        else:
            print(f"\n🚨 {failed_count}項效能測試失敗，系統效能需要改善！")
        
        return passed_count >= len(results) * 0.8


# 主程式入口
if __name__ == "__main__":
    print("🚀 CBA個人資料搜集系統 - 第四階段效能測試")
    print("="*60)
    
    # 檢查必要的套件
    try:
        import requests
        import psutil
    except ImportError as e:
        print(f"❌ 缺少必要套件: {e}")
        print("請安裝: pip install requests psutil")
        exit(1)
    
    # 執行效能測試
    test_runner = PerformanceTestRunner()
    success = test_runner.run_all_performance_tests()
    
    if success:
        print("\n🎉 效能測試完成！系統效能符合要求。")
    else:
        print("\n⚠️  效能測試完成，部分項目需要改善。") 
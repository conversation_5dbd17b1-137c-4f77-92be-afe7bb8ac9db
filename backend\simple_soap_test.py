#!/usr/bin/env python3
"""
簡單的 SOAP 客戶端測試
不需要完整的應用程式框架
"""

import os
import sys
import requests
import urllib3
from xml.etree import ElementTree as ET

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_soap_with_zeep():
    """使用 zeep 測試 SOAP 客戶端"""
    print("🧪 使用 zeep 測試 SOAP 客戶端")
    print("=" * 50)
    
    try:
        from zeep import Client, Transport
        from requests.adapters import HTTPAdapter
        
        # 測試token
        test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
        soap_ws_url = "http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx?WSDL"
        
        print(f"📡 連接到: {soap_ws_url}")
        print(f"🎯 使用 Token: {test_token[:20]}...")
        
        # 確保使用HTTP協議
        soap_url = soap_ws_url.replace('https://', 'http://')
        print(f"🔄 調整後的URL: {soap_url}")
        
        # 建立HTTP會話
        session = requests.Session()
        session.verify = False
        session.mount('http://', HTTPAdapter())
        
        # 建立Transport和Client
        transport = Transport(session=session, timeout=10)
        client = Client(soap_url, transport=transport)
        
        print("✅ zeep 客戶端建立成功！")
        
        # 測試服務呼叫
        print("\n🔑 呼叫 getUserProfile 服務...")
        result = client.service.getUserProfile(test_token)
        
        print(f"✅ 服務呼叫成功！")
        print(f"📝 回應類型: {type(result)}")
        print(f"📄 回應內容: {str(result)[:300]}...")
        
        # 解析XML
        if isinstance(result, str):
            try:
                root = ET.fromstring(result)
                print(f"\n📋 解析的用戶資訊:")
                for child in root:
                    print(f"  - {child.tag}: {child.text}")
            except ET.ParseError as e:
                print(f"❌ XML 解析失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ zeep 測試失敗: {str(e)}")
        print(f"🔍 錯誤類型: {type(e).__name__}")
        import traceback
        print(f"🔍 詳細錯誤:\n{traceback.format_exc()}")
        return False

def test_soap_raw_request():
    """使用原始 HTTP 請求測試 SOAP"""
    print("\n🌐 使用原始 HTTP 請求測試 SOAP")
    print("=" * 50)
    
    try:
        test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
        soap_url = "http://127.0.0.1:8000/SS/SS0/CommonWebService.asmx"
        
        # 構建 SOAP 請求
        soap_envelope = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <getUserProfile xmlns="http://serivce.common.kangdainfo.com">
            <in0>{test_token}</in0>
        </getUserProfile>
    </soap:Body>
</soap:Envelope>"""
        
        headers = {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': 'getUserProfile'
        }
        
        print(f"📡 發送 SOAP 請求到: {soap_url}")
        print(f"🎯 使用 Token: {test_token[:20]}...")
        
        response = requests.post(soap_url, data=soap_envelope, headers=headers, timeout=10)
        
        print(f"✅ HTTP 回應狀態碼: {response.status_code}")
        print(f"📄 回應內容: {response.text[:500]}...")
        
        # 解析回應
        if response.status_code == 200:
            try:
                root = ET.fromstring(response.text)
                # 查找用戶資料
                for elem in root.iter():
                    if 'userProfile' in elem.tag or '帳號' in elem.tag:
                        print(f"📋 找到用戶資料: {elem.tag} = {elem.text}")
            except ET.ParseError as e:
                print(f"❌ XML 解析失敗: {e}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 原始 SOAP 測試失敗: {str(e)}")
        return False

def compare_approaches():
    """比較兩種方法的結果"""
    print("\n🔍 比較兩種 SOAP 呼叫方法")
    print("=" * 60)
    
    zeep_ok = test_soap_with_zeep()
    raw_ok = test_soap_raw_request()
    
    print("\n" + "=" * 60)
    print("📊 測試結果對比:")
    print(f"  - zeep 客戶端: {'✅ 成功' if zeep_ok else '❌ 失敗'}")
    print(f"  - 原始 HTTP 請求: {'✅ 成功' if raw_ok else '❌ 失敗'}")
    
    if zeep_ok and raw_ok:
        print("🎉 兩種方法都成功！")
    elif zeep_ok:
        print("⚠️  只有 zeep 成功")
    elif raw_ok:
        print("⚠️  只有原始 HTTP 請求成功")
    else:
        print("❌ 兩種方法都失敗")
    
    return zeep_ok, raw_ok

if __name__ == "__main__":
    print("🔬 簡單 SOAP 客戶端測試")
    print("=" * 80)
    
    zeep_ok, raw_ok = compare_approaches()
    
    if zeep_ok:
        print("\n✅ zeep 方法正常工作，問題可能在應用程式集成中")
        sys.exit(0)
    else:
        print("\n❌ zeep 方法失敗，需要修復 SOAP 客戶端配置")
        sys.exit(1) 
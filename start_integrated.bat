@echo off
chcp 65001 >nul
echo.
echo ========================================
echo CBA受款人資料搜集系統 - 整合啟動 (80埠)
echo ========================================
echo.

REM 檢查管理員權限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理員權限才能使用80埠
    echo 請以管理員身分執行此腳本
    pause
    exit /b 1
)

REM 檢查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Python
    pause
    exit /b 1
)

REM 檢查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Node.js
    pause
    exit /b 1
)

REM 檢查uv
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到uv包管理器
    echo 安裝命令: pip install uv
    pause
    exit /b 1
)

echo ✅ 環境檢查通過
echo.

REM 檢查前端是否已建構
if not exist "frontend-vue\dist" (
    echo 📦 前端未建構，開始建構...
    
    cd frontend-vue
    if not exist "node_modules" (
        echo 安裝前端依賴...
        npm install
    )
    
    echo 建構前端...
    npm run build
    cd ..
    
    echo ✅ 前端建構完成
    echo.
)

REM 檢查後端依賴
if not exist "backend\.venv" (
    if not exist "backend\uv.lock" (
        echo 📦 安裝後端依賴...
        cd backend
        uv sync
        cd ..
        echo ✅ 後端依賴安裝完成
        echo.
    )
)

REM 檢查80埠是否被佔用
netstat -an | find "0.0.0.0:80" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ❌ 80埠已被佔用
    echo 請停止佔用80埠的服務後重試
    pause
    exit /b 1
)

REM 設定環境變數
set ENVIRONMENT=production
set DEBUG=false
set HOST=0.0.0.0
set PORT=80
set DATABASE_URL=sqlite:///./cba_system.db

echo 🔧 啟動整合服務...
echo 服務地址: http://localhost
echo API文檔: http://localhost/docs
echo 健康檢查: http://localhost/health
echo.
echo 按 Ctrl+C 停止服務
echo.

REM 啟動服務
cd backend
uv run uvicorn main:app --host %HOST% --port %PORT%

pause

"""
SSO (Single Sign-On) 整合功能
支援政府SOAP-based SSO
"""

import os
import logging
from typing import Dict, Any, Optional
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from xml.etree import ElementTree as ET
from datetime import datetime, timedelta

from .config import settings
from ..models.user import User, UserRole, Role, Department
from ..models.database import SessionLocal
from ..utils.audit import log_operation
from ..core.exceptions import CBAException

load_dotenv()

# 設置日誌
logger = logging.getLogger(__name__)


class SSOError(CBAException):
    """SSO錯誤基類"""
    
    def __init__(
        self, 
        message: str, 
        error_type: str = "SSO_ERROR",
        details: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message, error_type, details or {})
        self.user_message = user_message or self._get_user_friendly_message(error_type)
    
    def _get_user_friendly_message(self, error_type: str) -> str:
        """返回用戶友好的錯誤訊息"""
        user_messages = {
            "SSO_SERVICE_UNAVAILABLE": "SSO服務暫時無法使用，請稍後再試或聯繫系統管理員",
            "SSO_TOKEN_INVALID": "認證憑證無效，請重新登入",
            "SSO_TOKEN_EXPIRED": "認證憑證已過期，請重新登入",
            "SSO_USER_DATA_INVALID": "用戶資料格式錯誤，請聯繫系統管理員",
            "SSO_PERMISSION_DENIED": "權限不足，請聯繫系統管理員申請權限",
            "SSO_NETWORK_ERROR": "網路連線問題，請檢查網路連線或稍後再試",
            "SSO_CONFIGURATION_ERROR": "SSO設定錯誤，請聯繫系統管理員",
            "SSO_SOAP_ERROR": "SOAP服務呼叫失敗，請稍後再試",
            "SSO_USER_CREATION_FAILED": "建立用戶失敗，請聯繫系統管理員"
        }
        return user_messages.get(error_type, "SSO認證過程發生錯誤，請稍後再試")


class SSOTokenInvalidError(SSOError):
    """SSO Token無效錯誤"""
    def __init__(self, message: str = "SSO Token無效"):
        super().__init__(message, "SSO_TOKEN_INVALID")


class SSOTokenExpiredError(SSOError):
    """SSO Token過期錯誤"""
    def __init__(self, message: str = "SSO Token已過期"):
        super().__init__(message, "SSO_TOKEN_EXPIRED")


class SSOServiceUnavailableError(SSOError):
    """SSO服務不可用錯誤"""
    def __init__(self, message: str = "SSO服務暫時無法使用"):
        super().__init__(message, "SSO_SERVICE_UNAVAILABLE")


class SSONetworkError(SSOError):
    """SSO網路錯誤"""
    def __init__(self, message: str = "SSO服務網路連線失敗"):
        super().__init__(message, "SSO_NETWORK_ERROR")


class SOAPSSOAuthenticator:
    """SOAP-based 政府SSO認證器"""
    
    def __init__(self):
        self.soap_ws_url = settings.sso_soap_ws_url
        print(f"SOAP SSO Authenticator URL: {self.soap_ws_url}")
        self.max_retries = 3
        self.retry_delay = 1  # 秒
        
        # 檢查SOAP SSO配置是否完整
        self.is_configured = bool(self.soap_ws_url)
        
        if not self.is_configured:
            logger.warning("SOAP SSO配置不完整，SOAP SSO功能將無法使用。請檢查環境變數：SSO_SOAP_WS_URL")
    
    async def authenticate_with_token(self, sso_token: str, db: Session) -> Dict[str, Any]:
        """
        使用SSO Token進行認證
        
        Args:
            sso_token: SSO Token (ssoToken1)
            db: 資料庫會話
            
        Returns:
            包含JWT token和用戶資訊的字典
            
        Raises:
            SSOError: 認證失敗時拋出
        """
        if not self.is_configured:
            raise SSOError(
                "SOAP SSO服務未配置",
                "SSO_CONFIGURATION_ERROR"
            )
        
        if not sso_token:
            raise SSOTokenInvalidError("SSO Token不能為空")
        
        try:
            # 使用帶重試機制的SOAP呼叫
            user_info = await self._call_soap_service_with_retry(sso_token)
            
            # 解析用戶資訊並建立/更新用戶
            user = await self._process_user_profile(user_info, db)
            
            # 建立JWT Token
            access_token = self._create_jwt_token(user)
            
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user_info": {
                    "id": user.id,
                    "username": user.username,
                    "full_name": user.full_name,
                    "department": user.department.name if user.department else None,
                    "roles": [role.name for role in user.roles],
                    "permissions": user.permissions
                }
            }
            
        except SSOTokenInvalidError:
            # 重新拋出SSO Token無效錯誤
            raise
        except SSOServiceUnavailableError:
            # 重新拋出SSO服務不可用錯誤
            raise
        except Exception as e:
            logger.error(f"SOAP SSO認證過程發生未預期錯誤: {str(e)}", exc_info=True)
            raise SSOError(
                f"認證過程發生錯誤: {str(e)}",
                "SSO_ERROR",
                {"original_error": str(e)}
            )
    
    async def _call_soap_service_with_retry(self, sso_token: str) -> Dict[str, str]:
        """
        帶重試機制的SOAP服務呼叫
        
        Args:
            sso_token: SSO Token
            
        Returns:
            解析後的用戶資訊字典
        """
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                return await self._call_soap_service(sso_token)
            except SSONetworkError as e:
                last_error = e
                if attempt < self.max_retries - 1:
                    logger.warning(f"SOAP呼叫失敗，嘗試第 {attempt + 1} 次重試: {str(e)}")
                    import asyncio
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                continue
            except SSOError:
                # 非網路錯誤不重試
                raise
        
        # 重試次數用盡
        raise SSOServiceUnavailableError(f"SOAP服務經過 {self.max_retries} 次重試仍然失敗")
    
    async def _call_soap_service(self, sso_token: str) -> Dict[str, str]:
        """
        呼叫SOAP Web Service取得用戶資訊
        
        Args:
            sso_token: SSO Token
            
        Returns:
            解析後的用戶資訊字典
            
        Raises:
            SSOError: SOAP呼叫失敗時拋出
        """
        try:
            # 導入zeep進行SOAP呼叫
            try:
                from zeep import Client, Transport
                from zeep.exceptions import Fault, TransportError, Error as ZeepError
                import requests
            except ImportError:
                raise SSOError(
                    "缺少zeep依賴庫",
                    "SSO_CONFIGURATION_ERROR",
                    {"solution": "請執行: pip install zeep"}
                )
            
            # 建立SOAP客戶端，設置超時和HTTP配置
            try:
                # 確保使用HTTP協議 - 替換可能的https為http
                soap_url = self.soap_ws_url.replace('https://', 'http://')
                
                # 建立HTTP會話，明確指定不使用SSL
                session = requests.Session()
                # 禁用SSL驗證並強制使用HTTP
                session.verify = False
                
                # 抑制SSL警告
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                
                # 設置HTTP適配器
                from requests.adapters import HTTPAdapter
                session.mount('http://', HTTPAdapter())
                
                transport = Transport(session=session, timeout=10)  # 10秒超時，使用自定義session
                client = Client(soap_url, transport=transport)
                
                logger.info(f"SOAP客戶端已建立，使用URL: {soap_url}")
            except TransportError as e:
                raise SSONetworkError(f"無法連接到SOAP服務: {str(e)}")
            except Exception as e:
                raise SSOServiceUnavailableError(f"SOAP客戶端初始化失敗: {str(e)}")
            
            # 呼叫getUserProfile服務
            try:
                result = client.service.getUserProfile(sso_token)
                logger.info(f"SOAP呼叫結果類型: {type(result)}")
            except Fault as e:
                # SOAP服務錯誤
                if "invalid token" in str(e).lower() or "token expired" in str(e).lower():
                    raise SSOTokenInvalidError(f"Token無效或已過期: {str(e)}")
                else:
                    raise SSOError(f"SOAP服務內部錯誤: {str(e)}", "SSO_SOAP_ERROR")
            except TransportError as e:
                raise SSONetworkError(f"SOAP服務連線錯誤: {str(e)}")
            except requests.exceptions.Timeout:
                raise SSONetworkError("SOAP服務請求逾時")
            except requests.exceptions.ConnectionError:
                raise SSONetworkError("SOAP服務連線失敗")
            except ZeepError as e:
                raise SSOError(f"SOAP處理錯誤: {str(e)}", "SSO_SOAP_ERROR")
            
            if not result:
                raise SSOError("SOAP服務返回空結果", "SSO_USER_DATA_INVALID")
            
            # 解析XML格式的結果
            try:
                if isinstance(result, str):
                    root = ET.fromstring(result)
                else:
                    # 如果result不是字串，嘗試轉換
                    root = ET.fromstring(str(result))
            except ET.ParseError as e:
                raise SSOError(
                    f"SOAP回應XML格式錯誤: {str(e)}", 
                    "SSO_USER_DATA_INVALID",
                    {"raw_response": str(result)[:500]}  # 只記錄前500字符
                )
            
            # 檢查是否有錯誤
            if root.tag == 'Error':
                error_msg = root.text or "未知的SOAP服務錯誤"
                if "token" in error_msg.lower():
                    raise SSOTokenInvalidError(f"Token錯誤: {error_msg}")
                else:
                    raise SSOError(f"SOAP服務錯誤: {error_msg}", "SSO_SOAP_ERROR")
            
            # 提取用戶資訊
            user_info = {
                'account': self._get_xml_text(root, '帳號'),
                'full_name': self._get_xml_text(root, '姓名'),
                'dept_code': self._get_xml_text(root, '單位代碼'),
                'org_name': self._get_xml_text(root, '機關名稱')
            }
            
            # 驗證必要欄位
            if not user_info['account']:
                raise SSOError(
                    "SOAP服務返回的用戶資訊中缺少帳號", 
                    "SSO_USER_DATA_INVALID",
                    {"user_info": user_info}
                )
            
            logger.info(f"成功取得用戶資訊: {user_info['account']}")
            return user_info
            
        except SSOError:
            # 重新拋出SSO錯誤
            raise
        except Exception as e:
            logger.error(f"SOAP呼叫過程發生未預期錯誤: {str(e)}", exc_info=True)
            raise SSOError(
                f"SOAP呼叫失敗: {str(e)}",
                "SSO_SOAP_ERROR",
                {"original_error": str(e)}
            )
    
    def _get_xml_text(self, root: ET.Element, tag_name: str) -> Optional[str]:
        """從XML中安全地取得文字內容"""
        element = root.find(tag_name)
        return element.text if element is not None else None
    
    def _parse_department_name(self, dept_code: str, org_name: str) -> str:
        """
        解析部門編碼為完整的部門名稱
        
        Args:
            dept_code: 部門代碼 (例: "1030")
            org_name: 機關名稱 (例: "屏東縣政府")
            
        Returns:
            完整的部門名稱
        """
        if not dept_code or len(dept_code) < 4:
            return org_name or "未知部門"
        
        # 部門代碼對應表
        department_mapping = {
            # 行政暨研考處 (10xx)
            "10": "行政暨研考處",
            "1030": "行政暨研考處資訊管理科",
            "1010": "行政暨研考處綜合企劃科",
            "1020": "行政暨研考處管制考核科",
            "1040": "行政暨研考處法制科",
            
            # 其他處室可以繼續擴充
            "20": "人事處",
            "30": "主計處",
            "40": "政風處",
            "50": "秘書處",
            "60": "新聞處",
            "70": "地政處",
            "80": "工務處",
            "90": "都市發展處",
            
            # 科室級別
            "2010": "人事處第一科",
            "2020": "人事處第二科",
            "3010": "主計處第一科",
            "3020": "主計處第二科",
        }
        
        # 先嘗試精確匹配完整代碼
        if dept_code in department_mapping:
            return department_mapping[dept_code]
        
        # 如果沒有精確匹配，嘗試匹配處級代碼
        if len(dept_code) >= 2:
            bureau_code = dept_code[:2]
            if bureau_code in department_mapping:
                # 如果是四位代碼，添加科室資訊
                if len(dept_code) == 4:
                    section_code = dept_code[2:]
                    if section_code != "00":
                        return f"{department_mapping[bureau_code]}第{section_code}科"
                
                return department_mapping[bureau_code]
        
        # 如果都沒有匹配，返回原始機關名稱
        return org_name or f"部門{dept_code}"
    
    async def _process_user_profile(self, user_info: Dict[str, str], db: Session) -> User:
        """
        處理用戶資料，建立或更新用戶
        
        Args:
            user_info: 從SOAP服務取得的用戶資訊
            db: 資料庫會話
            
        Returns:
            用戶對象
        """
        account = user_info['account']
        full_name = user_info['full_name'] or account
        dept_code = user_info['dept_code']
        org_name = user_info['org_name']
        
        # 處理部門代碼 (解析完整的部門結構)
        department_name = self._parse_department_name(dept_code, org_name)
        
        # 查找或建立部門
        department = await self._get_or_create_department(dept_code, department_name, db)
        
        # 查找或建立用戶
        user = db.query(User).filter(User.username == account).first()
        
        if not user:
            # 建立新用戶
            user = await self._create_new_user(account, full_name, department, db)
        else:
            # 更新現有用戶
            user = await self._update_existing_user(user, full_name, department, db)
        
        return user
    
    async def _get_or_create_department(self, dept_code: str, dept_name: str, db: Session) -> Department:
        """取得或建立部門"""
        # 先嘗試用部門代碼查找
        department = db.query(Department).filter(Department.code == dept_code).first()
        
        if not department:
            # 再嘗試用部門名稱查找
            department = db.query(Department).filter(Department.name == dept_name).first()
            
            if not department:
                # 建立新部門，設定 code 與 name
                try:
                    department = Department(
                        code=dept_code,
                        name=dept_name,
                        description=""  # 可選描述
                    )
                    db.add(department)
                    db.commit()
                    db.refresh(department)
                    logger.info(f"建立新部門: {dept_name} (代碼: {dept_code})")
                except Exception as e:
                    db.rollback()
                    # 如果創建失敗（可能是重複名稱），再次嘗試查找
                    department = db.query(Department).filter(Department.name == dept_name).first()
                    if not department:
                        # 如果還是找不到，使用回退策略
                        logger.warning(f"無法創建部門 {dept_name}，使用預設部門: {str(e)}")
                        department = db.query(Department).filter(Department.name == "行政暨研考處資訊管理科").first()
                        if not department:
                            # 建立預設部門
                            department = Department(
                                code="1030",
                                name="行政暨研考處資訊管理科",
                                description=""
                            )
                            db.add(department)
                            db.commit()
                            db.refresh(department)
                            logger.info("建立預設部門: 行政暨研考處資訊管理科")
                    else:
                        # 若存在且缺少 code，則更新 code
                        if not department.code:
                            department.code = dept_code
                            db.commit()
                        logger.info(f"使用現有部門: {dept_name} (代碼更新: {dept_code})")
            else:
                # 現有部門，確保 code 欄位正確
                if department.code != dept_code:
                    department.code = dept_code
                    db.commit()
                    logger.info(f"更新部門代碼: {dept_name} -> {dept_code}")
        
        return department
    
    async def _create_new_user(self, username: str, full_name: str, department: Department, db: Session) -> User:
        """建立新用戶"""
        # 取得系統預設的 general 角色
        general_role = db.query(Role).filter(Role.name == "general").first()
        if not general_role:
            # 如果找不到 general 角色，記錄詳細信息並嘗試查找所有角色
            all_roles = db.query(Role).all()
            role_names = [r.name for r in all_roles]
            logger.error(f"找不到 'general' 角色，系統中存在的角色: {role_names}")
            
            # 嘗試使用其他可能的角色名稱
            general_role = db.query(Role).filter(Role.name == "一般帳號").first()
            if not general_role:
                raise CBAException(f"找不到 'general' 或 '一般帳號' 角色，請確認系統角色初始化配置是否正確。存在的角色: {role_names}")
        
        # 建立新用戶 (SSO用戶不需要密碼)
        user = User(
            username=username,
            full_name=full_name,
            department_id=department.id,
            hashed_password="sso_user",
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # 分配 general 角色
        user_role = UserRole(user_id=user.id, role_id=general_role.id)
        db.add(user_role)
        db.commit()
        
        logger.info(f"建立新SSO用戶: {username} ({full_name})，分配角色: {general_role.name}")
        return user
    
    async def _update_existing_user(self, user: User, full_name: str, department: Department, db: Session) -> User:
        """更新現有用戶資訊"""
        user.full_name = full_name
        user.department_id = department.id
        db.commit()
        db.refresh(user)
        
        logger.info(f"更新SSO用戶: {user.username} ({full_name})")
        return user
    
    def _create_jwt_token(self, user: User) -> str:
        """建立JWT Token"""
        from datetime import timedelta
        # 延遲導入避免循環導入
        from ..utils.jwt_auth import create_access_token
        
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "username": user.username,
            "full_name": user.full_name,
            "department_id": user.department_id,
            "roles": [role.name for role in user.roles],
            "permissions": user.permissions
        }
        
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )
        
        return access_token


# OAuth2 SSO功能已移除 - 只保留SOAP SSO


def get_soap_sso_authenticator() -> Optional[SOAPSSOAuthenticator]:
    """取得SOAP SSO認證器實例"""
    authenticator = SOAPSSOAuthenticator()
    if not authenticator.is_configured:
        return None
    return authenticator 
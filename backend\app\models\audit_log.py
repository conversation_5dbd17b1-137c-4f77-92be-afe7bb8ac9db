"""
審計日誌模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import pytz

# 設定台北時區
taipei_tz = pytz.timezone('Asia/Taipei')

class AuditLog(Base):
    """審計日誌模型"""
    __tablename__ = "audit_log"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(50), nullable=False, index=True)  # CREATE, READ, UPDATE, DELETE
    table_name = Column(String(100), index=True)
    record_id = Column(Integer, index=True)
    old_values = Column(Text)  # JSON格式的舊值
    new_values = Column(Text)  # JSON格式的新值
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    ip_address = Column(String(45))  # 支援IPv6
    user_agent = Column(Text)
    
    # 關聯
    user = relationship("User", back_populates="audit_logs")
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action}', table='{self.table_name}', record_id={self.record_id})>" 
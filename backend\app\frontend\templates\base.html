<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CBA受款人資料搜集系統{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定義CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 導航欄 -->
    {% if user %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-building"></i>
                CBA系統
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/dashboard' %}active{% endif %}" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> 主控台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/payee' in request.url.path %}active{% endif %}" href="/payee">
                            <i class="bi bi-people"></i> 受款人管理
                        </a>
                    </li>
                    {% if user.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">
                            <i class="bi bi-gear"></i> 系統管理
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ user.full_name or user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person"></i> 個人資料
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">
                                <i class="bi bi-box-arrow-right"></i> 登出
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- 主要內容 -->
    <main class="{% if user %}container-fluid mt-4{% else %}d-flex align-items-center min-vh-100{% endif %}">
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}
        
        {% if success %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> {{ success }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- 底部 -->
    {% if user %}
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <span class="text-muted">
                &copy; 2024 CBA受款人資料搜集系統. All rights reserved.
            </span>
        </div>
    </footer>
    {% endif %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定義JS -->
    <script src="/static/js/app.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

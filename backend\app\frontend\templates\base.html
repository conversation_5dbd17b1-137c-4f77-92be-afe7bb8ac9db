<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="CBA受款人資料搜集系統 - 現代化的受款人資料管理平台">
    <meta name="keywords" content="CBA, 受款人, 資料管理, 銀行帳戶">
    <meta name="author" content="CBA系統">
    <meta name="theme-color" content="#2563eb">

    <title>{% block title %}CBA受款人資料搜集系統{% endblock %}</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">

    <!-- 自定義CSS -->
    <link href="/static/css/style.css?v=2.0" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏦</text></svg>">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 跳過連結 -->
    <a href="#main-content" class="skip-link">跳到主要內容</a>

    <!-- 導航欄 -->
    {% if user %}
    <nav class="navbar navbar-expand-lg navbar-dark" role="navigation" aria-label="主要導航"
         style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); border: none; padding: 1rem 0; backdrop-filter: blur(10px);">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard" aria-label="CBA系統首頁"
               style="font-weight: 700; font-size: 1.25rem; color: white !important; display: flex; align-items: center; gap: 0.5rem; transition: transform 150ms ease-in-out;"
               onmouseover="this.style.transform='scale(1.05)'; this.style.color='white';"
               onmouseout="this.style.transform='scale(1)'; this.style.color='white';">
                <i class="bi bi-building" aria-hidden="true" style="font-size: 1.5rem; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));"></i>
                <span>CBA系統</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="切換導航選單">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" role="menubar">
                    <li class="nav-item" role="none">
                        <a class="nav-link {% if request.url.path == '/dashboard' %}active{% endif %}"
                           href="/dashboard" role="menuitem"
                           {% if request.url.path == '/dashboard' %}aria-current="page"{% endif %}
                           style="font-weight: 500; color: rgba(255, 255, 255, 0.9) !important; padding: 0.5rem 1rem !important; border-radius: 0.5rem; transition: all 150ms ease-in-out; position: relative; display: flex; align-items: center; gap: 0.25rem; {% if request.url.path == '/dashboard' %}background-color: rgba(255, 255, 255, 0.2); box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);{% endif %}"
                           onmouseover="this.style.color='white'; this.style.backgroundColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateY(-1px)';"
                           onmouseout="this.style.color='rgba(255, 255, 255, 0.9)'; {% if request.url.path != '/dashboard' %}this.style.backgroundColor='transparent';{% endif %} this.style.transform='translateY(0)';">
                            <i class="bi bi-speedometer2" aria-hidden="true" style="font-size: 0.875rem;"></i>
                            <span>主控台</span>
                        </a>
                    </li>
                    <li class="nav-item" role="none">
                        <a class="nav-link {% if '/payee' in request.url.path %}active{% endif %}"
                           href="/payee" role="menuitem"
                           {% if '/payee' in request.url.path %}aria-current="page"{% endif %}
                           style="font-weight: 500; color: rgba(255, 255, 255, 0.9) !important; padding: 0.5rem 1rem !important; border-radius: 0.5rem; transition: all 150ms ease-in-out; position: relative; display: flex; align-items: center; gap: 0.25rem; {% if '/payee' in request.url.path %}background-color: rgba(255, 255, 255, 0.2); box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);{% endif %}"
                           onmouseover="this.style.color='white'; this.style.backgroundColor='rgba(255, 255, 255, 0.1)'; this.style.transform='translateY(-1px)';"
                           onmouseout="this.style.color='rgba(255, 255, 255, 0.9)'; {% if '/payee' not in request.url.path %}this.style.backgroundColor='transparent';{% endif %} this.style.transform='translateY(0)';">
                            <i class="bi bi-people" aria-hidden="true" style="font-size: 0.875rem;"></i>
                            <span>受款人管理</span>
                        </a>
                    </li>
                    {% if user.roles == 'admin' %}
                    <li class="nav-item" role="none">
                        <a class="nav-link" href="/admin" role="menuitem">
                            <i class="bi bi-gear" aria-hidden="true"></i>
                            <span>系統管理</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav" role="menubar">
                    <li class="nav-item dropdown" role="none">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                           aria-expanded="false" aria-haspopup="true" id="userDropdown">
                            <i class="bi bi-person-circle" aria-hidden="true"></i>
                            <span>{{ user.full_name or user.username }}</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="userDropdown" role="menu">
                            <li role="none">
                                <a class="dropdown-item" href="/profile" role="menuitem">
                                    <i class="bi bi-person" aria-hidden="true"></i>
                                    <span>個人資料</span>
                                </a>
                            </li>
                            <li role="separator"><hr class="dropdown-divider"></li>
                            <li role="none">
                                <a class="dropdown-item" href="/logout" role="menuitem">
                                    <i class="bi bi-box-arrow-right" aria-hidden="true"></i>
                                    <span>登出</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- 主要內容 -->
    <main id="main-content" class="{% if user %}container-fluid{% else %}d-flex align-items-center min-vh-100{% endif %} page-enter"
          role="main" aria-label="主要內容區域">

        <!-- 載入指示器 -->
        <div id="loading-indicator" class="d-none position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
             style="background: rgba(255,255,255,0.8); z-index: 9999;" aria-hidden="true">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
        </div>

        <!-- 錯誤和成功訊息 -->
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show error-shake" role="alert" aria-live="polite">
            <i class="bi bi-exclamation-triangle" aria-hidden="true"></i>
            <span>{{ error }}</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="關閉錯誤訊息"></button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible fade show success-flash" role="alert" aria-live="polite">
            <i class="bi bi-check-circle" aria-hidden="true"></i>
            <span>{{ success }}</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="關閉成功訊息"></button>
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>
    
    <!-- 底部 -->
    {% if user %}
    <footer class="bg-light text-center py-4 mt-5 border-top" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-start">
                    <small class="text-muted">
                        © 2024 CBA受款人資料搜集系統
                    </small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        版本 1.0.0 |
                        <a href="/docs" class="text-decoration-none" target="_blank" rel="noopener">API文檔</a> |
                        <a href="/health" class="text-decoration-none" target="_blank" rel="noopener">系統狀態</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
            crossorigin="anonymous"></script>

    <!-- 自定義JS -->
    <script src="/static/js/app.js"></script>

    <!-- 無障礙性增強腳本 -->
    <script>
        // 鍵盤導航增強
        document.addEventListener('DOMContentLoaded', function() {
            // 為所有可聚焦元素添加鍵盤導航
            const focusableElements = document.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');

            // 改善焦點可見性
            focusableElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.style.outline = '2px solid var(--primary-color)';
                    this.style.outlineOffset = '2px';
                });

                element.addEventListener('blur', function() {
                    this.style.outline = '';
                    this.style.outlineOffset = '';
                });
            });

            // 載入指示器控制
            window.showLoading = function() {
                document.getElementById('loading-indicator').classList.remove('d-none');
            };

            window.hideLoading = function() {
                document.getElementById('loading-indicator').classList.add('d-none');
            };

            // 表單提交時顯示載入
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    showLoading();
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

# CBA受款人資料搜集系統 - 整合版 (80埠)

## 📋 系統概述

CBA受款人資料搜集系統整合版，將Vue.js前端和FastAPI後端合併為單一服務，統一使用80埠提供服務。

## 🏗️ 系統架構

```
用戶瀏覽器
    ↓ http://localhost:80
FastAPI整合服務 (80埠)
    ├── /api/* → 後端API服務
    ├── /docs → API文檔
    ├── /health → 健康檢查
    └── /* → Vue.js前端SPA
```

## 🚀 快速開始

### 方法一：一鍵啟動 (推薦)

```bash
# Linux/macOS
chmod +x start_integrated.sh
sudo ./start_integrated.sh

# Windows (以管理員身分執行)
start_integrated.bat
```

### 方法二：完整建構部署

```bash
# 完整建構和部署
chmod +x build_and_deploy.sh
./build_and_deploy.sh full

# 或分步執行
./build_and_deploy.sh build    # 僅建構
sudo ./build_and_deploy.sh start  # 僅啟動
```

### 方法三：開發模式

```bash
# 啟動開發模式 (前端3000埠 + 後端80埠)
chmod +x start_dev_integrated.sh
./start_dev_integrated.sh

# 停止開發服務
./stop_dev.sh
```

## 🌐 存取地址

- **主要入口**: http://localhost
- **API文檔**: http://localhost/docs
- **健康檢查**: http://localhost/health
- **前端應用**: http://localhost/ (Vue.js SPA)
- **後端API**: http://localhost/api/

## 📦 系統需求

### 基本需求
- **Python**: 3.9 或以上
- **Node.js**: 18 或以上
- **uv**: Python包管理器
- **管理員權限**: 80埠需要管理員權限

### 安裝依賴

```bash
# 安裝uv (如果未安裝)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安裝Node.js (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 🔧 手動部署步驟

### 1. 建構前端

```bash
cd frontend-vue
npm install
npm run build
cd ..
```

### 2. 安裝後端依賴

```bash
cd backend
uv sync
cd ..
```

### 3. 啟動整合服務

```bash
cd backend
export ENVIRONMENT=production
export HOST=0.0.0.0
export PORT=80
sudo uv run uvicorn main:app --host $HOST --port $PORT
```

## 🛠️ 開發指南

### 開發模式架構

開發時建議使用分離模式：
- 前端開發服務器: http://localhost:3000 (熱重載)
- 後端API服務器: http://localhost:80 (支援重載)

```bash
# 啟動開發模式
./start_dev_integrated.sh

# 前端會自動代理API請求到後端80埠
```

### 建構流程

1. **前端建構**: Vue.js → 靜態檔案 (dist/)
2. **後端整合**: FastAPI掛載靜態檔案
3. **路由處理**: SPA路由由Vue Router處理
4. **API代理**: /api/* 請求轉發到FastAPI

### 檔案結構

```
project/
├── frontend-vue/          # Vue.js前端
│   ├── dist/             # 建構輸出 (自動生成)
│   ├── src/              # 源碼
│   └── package.json      # 前端依賴
├── backend/              # FastAPI後端
│   ├── main.py          # 整合服務入口
│   └── app/             # 應用程式碼
├── start_integrated.sh   # 快速啟動腳本
├── build_and_deploy.sh  # 建構部署腳本
└── start_dev_integrated.sh # 開發模式腳本
```

## 🔒 權限說明

### 80埠權限

80埠是特權埠，需要管理員權限：

```bash
# Linux/macOS
sudo ./start_integrated.sh

# 或使用sudo啟動服務
sudo uv run uvicorn main:app --host 0.0.0.0 --port 80
```

### Windows權限

Windows需要以管理員身分執行：
1. 右鍵點擊命令提示字元
2. 選擇「以系統管理員身分執行」
3. 執行 `start_integrated.bat`

## 📊 效能優化

### 生產環境優化

1. **靜態檔案快取**: 自動設定適當的快取標頭
2. **Gzip壓縮**: 自動壓縮文字檔案
3. **資源分割**: 按需載入JavaScript模組
4. **CDN支援**: 可配置CDN加速靜態資源

### 監控和日誌

```bash
# 檢查服務狀態
./build_and_deploy.sh status

# 查看應用程式日誌
tail -f logs/backend.log    # 後端日誌
tail -f logs/frontend.log   # 前端日誌 (開發模式)
```

## 🚨 故障排除

### 常見問題

1. **80埠被佔用**
   ```bash
   # 查看佔用進程
   sudo lsof -i :80
   
   # 停止佔用進程
   sudo lsof -ti:80 | xargs sudo kill -9
   ```

2. **權限不足**
   ```bash
   # 確保使用sudo執行
   sudo ./start_integrated.sh
   ```

3. **前端未建構**
   ```bash
   # 手動建構前端
   cd frontend-vue
   npm run build
   ```

4. **依賴問題**
   ```bash
   # 重新安裝依賴
   cd frontend-vue && npm install
   cd ../backend && uv sync
   ```

### 日誌查看

```bash
# 即時查看後端日誌
tail -f logs/backend.log

# 查看系統日誌
journalctl -u cba-system -f  # 如果使用systemd
```

## 🔄 從舊版本遷移

### 從Streamlit版本遷移

1. 停止舊的Streamlit服務
2. 備份資料庫檔案
3. 執行新版本部署腳本
4. 驗證功能正常

### 從分離部署遷移

1. 停止nginx和分離的服務
2. 執行整合部署腳本
3. 更新任何外部配置

## 📚 相關文檔

- [Vue.js前端開發指南](./frontend-vue/README.md)
- [FastAPI後端API文檔](http://localhost/docs)
- [Streamlit到Vue.js遷移指南](./Streamlit到Vue.js遷移指南.md)

## 🎯 生產環境部署

### 使用systemd (推薦)

```bash
# 創建systemd服務檔案
sudo tee /etc/systemd/system/cba-system.service << EOF
[Unit]
Description=CBA受款人資料搜集系統
After=network.target

[Service]
Type=exec
User=cba-app
Group=cba-app
WorkingDirectory=/opt/cba-system/backend
Environment=ENVIRONMENT=production
Environment=HOST=0.0.0.0
Environment=PORT=80
ExecStart=/opt/cba-system/backend/.venv/bin/uvicorn main:app --host 0.0.0.0 --port 80
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 啟用和啟動服務
sudo systemctl enable cba-system
sudo systemctl start cba-system
```

### 使用Docker (可選)

```dockerfile
# Dockerfile範例
FROM python:3.11-slim

# 安裝Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
RUN apt-get install -y nodejs

# 複製和建構應用
COPY . /app
WORKDIR /app

# 建構前端
RUN cd frontend-vue && npm install && npm run build

# 安裝後端依賴
RUN cd backend && pip install uv && uv sync

# 啟動服務
EXPOSE 80
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "80"]
```

---

**注意**: 此整合版本將前端和後端合併為單一服務，簡化了部署和維護。適合中小型部署環境使用。

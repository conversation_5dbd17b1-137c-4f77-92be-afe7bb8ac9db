"""
審計日誌工具
"""

import json
import logging
from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import Request

from ..models.audit_log import AuditLog
from ..models.user import User

# 設置日誌
logger = logging.getLogger(__name__)


class AuditAction(str, Enum):
    """審計操作類型"""
    CREATE = "CREATE"
    READ = "READ"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    EXPORT = "EXPORT"
    IMPORT = "IMPORT"
    PERMISSION_CHANGE = "PERMISSION_CHANGE"
    ROLE_CHANGE = "ROLE_CHANGE"


def log_operation(
    db: Session,
    user_id: Optional[int],
    action: str,
    table_name: Optional[str] = None,
    record_id: Optional[int] = None,
    old_values: Optional[Dict[str, Any]] = None,
    new_values: Optional[Dict[str, Any]] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> AuditLog:
    """
    通用審計日誌記錄函數
    
    Args:
        db: 資料庫會話
        user_id: 用戶ID
        action: 操作類型 (字串)
        table_name: 操作的資料表名稱
        record_id: 操作的記錄ID
        old_values: 舊值（更新前的值）
        new_values: 新值（更新後的值）
        ip_address: 用戶IP地址
        user_agent: 用戶代理字串
        
    Returns:
        建立的審計日誌記錄
    """
    
    # 轉換字典為JSON字串
    old_values_json = json.dumps(old_values, ensure_ascii=False) if old_values else None
    new_values_json = json.dumps(new_values, ensure_ascii=False) if new_values else None
    
    # 建立審計日誌記錄
    audit_log = AuditLog(
        user_id=user_id,
        action=action,
        table_name=table_name,
        record_id=record_id,
        old_values=old_values_json,
        new_values=new_values_json,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    # 保存到資料庫
    try:
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)
        return audit_log
    except Exception as e:
        db.rollback()
        logger.error(f"審計日誌保存失敗: {str(e)}")
        # 重新嘗試一次
        try:
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            return audit_log
        except Exception as retry_e:
            db.rollback()
            logger.error(f"審計日誌重試保存失敗: {str(retry_e)}")
            # 即使審計日誌失敗，也不應該影響主要業務流程
    return audit_log


def create_audit_log(
    db: Session,
    user_id: Optional[int],
    action: AuditAction,
    table_name: Optional[str] = None,
    record_id: Optional[int] = None,
    old_values: Optional[Dict[str, Any]] = None,
    new_values: Optional[Dict[str, Any]] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> AuditLog:
    """
    建立審計日誌記錄
    
    Args:
        db: 資料庫會話
        user_id: 用戶ID
        action: 操作類型
        table_name: 操作的資料表名稱
        record_id: 操作的記錄ID
        old_values: 舊值（更新前的值）
        new_values: 新值（更新後的值）
        ip_address: 用戶IP地址
        user_agent: 用戶代理字串
        
    Returns:
        建立的審計日誌記錄
    """
    
    # 轉換字典為JSON字串
    old_values_json = json.dumps(old_values, ensure_ascii=False) if old_values else None
    new_values_json = json.dumps(new_values, ensure_ascii=False) if new_values else None
    
    # 建立審計日誌記錄
    audit_log = AuditLog(
        user_id=user_id,
        action=action.value,
        table_name=table_name,
        record_id=record_id,
        old_values=old_values_json,
        new_values=new_values_json,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    # 保存到資料庫
    try:
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)
        return audit_log
    except Exception as e:
        db.rollback()
        logger.error(f"審計日誌保存失敗: {str(e)}")
        # 重新嘗試一次
        try:
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            return audit_log
        except Exception as retry_e:
            db.rollback()
            logger.error(f"審計日誌重試保存失敗: {str(retry_e)}")
            # 即使審計日誌失敗，也不應該影響主要業務流程
    return audit_log


def log_personal_data_create(
    db: Session,
    user: User,
    personal_data_id: int,
    personal_data: Dict[str, Any],
    request: Optional[Request] = None
):
    """
    記錄個人資料建立操作
    
    Args:
        db: 資料庫會話
        user: 操作用戶
        personal_data_id: 個人資料ID
        personal_data: 個人資料內容
        request: HTTP請求對象
    """
    # 過濾敏感資訊（不記錄加密的身分證字號）
    safe_data = personal_data.copy()
    if 'id_number_encrypted' in safe_data:
        safe_data['id_number_encrypted'] = '[ENCRYPTED]'
    
    create_audit_log(
        db=db,
        user_id=user.id,
        action=AuditAction.CREATE,
        table_name="personal_data",
        record_id=personal_data_id,
        new_values=safe_data,
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_personal_data_read(
    db: Session,
    user: User,
    personal_data_id: int,
    request: Optional[Request] = None
):
    """
    記錄個人資料查詢操作
    
    Args:
        db: 資料庫會話
        user: 操作用戶
        personal_data_id: 個人資料ID
        request: HTTP請求對象
    """
    create_audit_log(
        db=db,
        user_id=user.id,
        action=AuditAction.READ,
        table_name="personal_data",
        record_id=personal_data_id,
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_personal_data_update(
    db: Session,
    user: User,
    personal_data_id: int,
    old_values: Dict[str, Any],
    new_values: Dict[str, Any],
    request: Optional[Request] = None
):
    """
    記錄個人資料更新操作
    
    Args:
        db: 資料庫會話
        user: 操作用戶
        personal_data_id: 個人資料ID
        old_values: 更新前的值
        new_values: 更新後的值
        request: HTTP請求對象
    """
    # 過濾敏感資訊
    safe_old_values = old_values.copy()
    safe_new_values = new_values.copy()
    
    if 'id_number_encrypted' in safe_old_values:
        safe_old_values['id_number_encrypted'] = '[ENCRYPTED]'
    if 'id_number_encrypted' in safe_new_values:
        safe_new_values['id_number_encrypted'] = '[ENCRYPTED]'
    
    create_audit_log(
        db=db,
        user_id=user.id,
        action=AuditAction.UPDATE,
        table_name="personal_data",
        record_id=personal_data_id,
        old_values=safe_old_values,
        new_values=safe_new_values,
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_personal_data_delete(
    db: Session,
    user: User,
    personal_data_id: int,
    personal_data: Dict[str, Any],
    request: Optional[Request] = None
):
    """
    記錄個人資料刪除操作
    
    Args:
        db: 資料庫會話
        user: 操作用戶
        personal_data_id: 個人資料ID
        personal_data: 被刪除的個人資料內容
        request: HTTP請求對象
    """
    # 過濾敏感資訊
    safe_data = personal_data.copy()
    if 'id_number_encrypted' in safe_data:
        safe_data['id_number_encrypted'] = '[ENCRYPTED]'
    
    create_audit_log(
        db=db,
        user_id=user.id,
        action=AuditAction.DELETE,
        table_name="personal_data",
        record_id=personal_data_id,
        old_values=safe_data,
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_user_login(
    db: Session,
    user_id: int,
    request: Optional[Request] = None
):
    """
    記錄用戶登入操作
    
    Args:
        db: 資料庫會話
        user_id: 用戶ID
        request: HTTP請求對象
    """
    create_audit_log(
        db=db,
        user_id=user_id,
        action=AuditAction.LOGIN,
        new_values={"login_time": datetime.utcnow().isoformat()},
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_user_logout(
    db: Session,
    user_id: int,
    request: Optional[Request] = None
):
    """
    記錄用戶登出操作
    
    Args:
        db: 資料庫會話
        user_id: 用戶ID
        request: HTTP請求對象
    """
    create_audit_log(
        db=db,
        user_id=user_id,
        action=AuditAction.LOGOUT,
        new_values={"logout_time": datetime.utcnow().isoformat()},
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_role_change(
    db: Session,
    admin_user: User,
    target_user_id: int,
    old_roles: list,
    new_roles: list,
    request: Optional[Request] = None
):
    """
    記錄角色變更操作
    
    Args:
        db: 資料庫會話
        admin_user: 執行變更的管理員
        target_user_id: 被變更的用戶ID
        old_roles: 舊角色列表
        new_roles: 新角色列表
        request: HTTP請求對象
    """
    create_audit_log(
        db=db,
        user_id=admin_user.id,
        action=AuditAction.ROLE_CHANGE,
        table_name="user_roles",
        record_id=target_user_id,
        old_values={"roles": old_roles},
        new_values={"roles": new_roles},
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def log_data_export(
    db: Session,
    user: User,
    export_type: str,
    record_count: int,
    filters: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None
):
    """
    記錄資料匯出操作
    
    Args:
        db: 資料庫會話
        user: 操作用戶
        export_type: 匯出類型
        record_count: 匯出記錄數量
        filters: 匯出過濾條件
        request: HTTP請求對象
    """
    export_data = {
        "export_type": export_type,
        "record_count": record_count,
        "filters": filters or {}
    }
    
    create_audit_log(
        db=db,
        user_id=user.id,
        action=AuditAction.EXPORT,
        table_name="personal_data",
        new_values=export_data,
        ip_address=_get_client_ip(request) if request else None,
        user_agent=_get_user_agent(request) if request else None
    )


def _get_client_ip(request: Request) -> Optional[str]:
    """
    取得用戶端IP地址
    
    Args:
        request: HTTP請求對象
        
    Returns:
        IP地址字串
    """
    if not request:
        return None
    
    # 檢查代理伺服器標頭
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # 取第一個IP（原始用戶端IP）
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 回退到直接連接IP
    return request.client.host if request.client else None


def _get_user_agent(request: Request) -> Optional[str]:
    """
    取得用戶代理字串
    
    Args:
        request: HTTP請求對象
        
    Returns:
        用戶代理字串
    """
    if not request:
        return None
    
    return request.headers.get("User-Agent")


def get_audit_logs(
    db: Session,
    user_id: Optional[int] = None,
    action: Optional[AuditAction] = None,
    table_name: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0
) -> list:
    """
    查詢審計日誌
    
    Args:
        db: 資料庫會話
        user_id: 用戶ID過濾
        action: 操作類型過濾
        table_name: 資料表名稱過濾
        start_date: 開始日期過濾
        end_date: 結束日期過濾
        limit: 限制返回數量
        offset: 偏移量
        
    Returns:
        審計日誌列表
    """
    query = db.query(AuditLog)
    
    if user_id:
        query = query.filter(AuditLog.user_id == user_id)
    
    if action:
        query = query.filter(AuditLog.action == action.value)
    
    if table_name:
        query = query.filter(AuditLog.table_name == table_name)
    
    if start_date:
        query = query.filter(AuditLog.timestamp >= start_date)
    
    if end_date:
        query = query.filter(AuditLog.timestamp <= end_date)
    
    # 按時間降序排列
    query = query.order_by(AuditLog.timestamp.desc())
    
    # 應用分頁
    query = query.offset(offset).limit(limit)
    
    return query.all() 
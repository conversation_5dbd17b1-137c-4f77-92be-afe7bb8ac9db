#!/usr/bin/env python3
"""
資料庫初始化腳本
"""

import sqlite3
import os
from datetime import datetime
from dotenv import load_dotenv
import os.path
from passlib.context import CryptContext

# 載入環境變數，指定backend目錄中的.env檔案
load_dotenv(os.path.join('backend', '.env'))

# 密碼雜湊設定，與jwt_auth.py保持一致
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def init_database():
    """初始化資料庫"""
    conn = sqlite3.connect('database/cba_personal_data.db')
    cursor = conn.cursor()
    
    try:
        # 創建權限表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                category TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            )
        """)
        
        # 創建角色表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            )
        """)
        
        # 創建用戶角色關聯表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id INTEGER,
                role_id INTEGER,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
            )
        """)
        
        # 插入基本權限
        permissions = [
            ('CREATE_PAYEE_DATA', '新增受款人資料', 'payee_data'),
            ('READ_PAYEE_DATA', '查詢受款人資料', 'payee_data'),
            ('UPDATE_PAYEE_DATA', '修改受款人資料', 'payee_data'),
            ('DELETE_PAYEE_DATA', '刪除受款人資料', 'payee_data'),
            ('READ_ALL_DATA', '查詢所有資料', 'global'),
            ('EXPORT_DATA', '匯出資料', 'global'),
            ('MANAGE_ADMIN', '管理管理者帳號', 'admin'),
            ('MANAGE_GLOBAL', '管理全域帳號', 'admin'),
            ('MANAGE_USERS', '管理用戶', 'admin'),
            ('VIEW_AUDIT_LOG', '查看審計日誌', 'admin')
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO permissions (name, description, category) 
            VALUES (?, ?, ?)
        """, permissions)
        
        # 插入基本角色
        roles = [
            ('general', '一般帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA"]'),
            ('global', '全域帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA"]'),
            ('admin', '管理者帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS", "VIEW_AUDIT_LOG"]')
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO roles (name, description, permissions) 
            VALUES (?, ?, ?)
        """, roles)
        
        # 創建測試用戶，使用環境變數中的密碼或預設密碼
        admin_password = os.getenv("ADMIN_PASSWORD", "admin123")
        print(f"使用管理員密碼: {admin_password}")
        
        # 使用passlib進行密碼雜湊，與jwt_auth.py保持一致
        hashed_password = pwd_context.hash(admin_password)
        
        cursor.execute("""
            INSERT OR IGNORE INTO users (username, hashed_password, full_name, department_id, is_active)
            VALUES (?, ?, ?, ?, ?)
        """, ('admin', hashed_password, '系統管理員', 1, True))
        
        # 獲取用戶ID和角色ID
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        user_result = cursor.fetchone()
        if user_result:
            user_id = user_result[0]
            
            cursor.execute("SELECT id FROM roles WHERE name = 'admin'")
            role_result = cursor.fetchone()
            if role_result:
                role_id = role_result[0]
                
                # 分配角色給用戶
                cursor.execute("""
                    INSERT OR IGNORE INTO user_roles (user_id, role_id) 
                    VALUES (?, ?)
                """, (user_id, role_id))
        
        conn.commit()
        print("資料庫初始化完成！")
        print(f"測試帳號: admin / {admin_password}")
        
    except Exception as e:
        print(f"初始化失敗: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    init_database() 
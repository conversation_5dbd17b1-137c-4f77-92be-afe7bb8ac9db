# CBA受款人資料搜集系統 - 簡化nginx配置
# 適用於Windows開發環境 (前端+後端雙服務)

worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    # 後端API伺服器
    upstream cba_backend {
        server 127.0.0.1:8080;
    }

    # 前端Streamlit伺服器
    upstream cba_frontend {
        server 127.0.0.1:8501;
    }
    
    server {
        listen       80;
        server_name  localhost;
        
        # 前端應用 (Streamlit)
        location / {
            proxy_pass http://cba_frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
        
        # 後端API
        location /api/ {
            proxy_pass http://cba_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 健康檢查
        location /health {
            proxy_pass http://cba_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        

        
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}

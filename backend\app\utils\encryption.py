"""
身分證字號加密工具
使用AES-256-GCM加密演算法
"""

import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from dotenv import load_dotenv

load_dotenv()


class EncryptionError(Exception):
    """加密相關錯誤"""
    pass


def generate_encryption_key() -> str:
    """
    生成32字節的加密金鑰
    返回base64編碼的金鑰字串
    """
    key = os.urandom(32)
    return base64.b64encode(key).decode('utf-8')


def _get_encryption_key() -> bytes:
    """
    從環境變數取得加密金鑰
    如果沒有設定，將生成新的金鑰
    """
    key_str = os.getenv("ENCRYPTION_KEY")
    if not key_str:
        raise EncryptionError("ENCRYPTION_KEY環境變數未設定")
    
    try:
        return base64.b64decode(key_str.encode('utf-8'))
    except Exception as e:
        raise EncryptionError(f"無效的加密金鑰格式: {e}")


def encrypt_id_number(id_number: str) -> str:
    """
    加密身分證字號
    
    Args:
        id_number: 原始身分證字號
        
    Returns:
        加密後的字串 (base64編碼的 IV + 密文 + 標籤)
        
    Raises:
        EncryptionError: 加密失敗時拋出
    """
    if not id_number:
        raise EncryptionError("身分證字號不能為空")
        
    try:
        # 取得加密金鑰
        key = _get_encryption_key()
        
        # 生成隨機IV (初始化向量)
        iv = os.urandom(16)  # AES-GCM建議使用16字節IV
        
        # 建立AES-GCM加密器
        cipher = Cipher(algorithms.AES(key), modes.GCM(iv))
        encryptor = cipher.encryptor()
        
        # 執行加密
        plaintext = id_number.encode('utf-8')
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        
        # 組合 IV + 密文 + 認證標籤
        encrypted_data = iv + ciphertext + encryptor.tag
        
        # 使用base64編碼以便儲存
        return base64.b64encode(encrypted_data).decode('utf-8')
        
    except Exception as e:
        raise EncryptionError(f"加密失敗: {e}")


def decrypt_id_number(encrypted_data: str) -> str:
    """
    解密身分證字號
    
    Args:
        encrypted_data: 加密後的字串
        
    Returns:
        原始身分證字號
        
    Raises:
        EncryptionError: 解密失敗時拋出
    """
    if not encrypted_data:
        raise EncryptionError("加密資料不能為空")
        
    try:
        # 取得加密金鑰
        key = _get_encryption_key()
        
        # 解碼base64
        encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
        
        # 提取 IV (前16字節)
        iv = encrypted_bytes[:16]
        
        # 提取認證標籤 (後16字節)
        tag = encrypted_bytes[-16:]
        
        # 提取密文 (中間部分)
        ciphertext = encrypted_bytes[16:-16]
        
        # 建立AES-GCM解密器
        cipher = Cipher(algorithms.AES(key), modes.GCM(iv, tag))
        decryptor = cipher.decryptor()
        
        # 執行解密
        plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        
        return plaintext.decode('utf-8')
        
    except Exception as e:
        raise EncryptionError(f"解密失敗: {e}")


def verify_encryption_integrity(id_number: str) -> bool:
    """
    驗證加密解密的完整性
    測試用函數
    
    Args:
        id_number: 要測試的身分證字號
        
    Returns:
        True如果加密解密正確，False否則
    """
    try:
        encrypted = encrypt_id_number(id_number)
        decrypted = decrypt_id_number(encrypted)
        return decrypted == id_number
    except EncryptionError:
        return False


# 通用加密/解密函數（向後相容）
def encrypt_data(data: str) -> str:
    """通用資料加密函數"""
    return encrypt_id_number(data)


def decrypt_data(encrypted_data: str) -> str:
    """通用資料解密函數"""
    return decrypt_id_number(encrypted_data)


"""
IV機制說明 (初始化向量):

1. 作用：
   - 確保相同的明文每次加密都產生不同的密文
   - 防止重放攻擊和模式分析攻擊
   - 提高加密系統的安全性

2. 實作方式：
   - 每次加密時使用 os.urandom(16) 生成16字節的隨機IV
   - IV與密文一起存儲，解密時使用相同的IV
   - IV不需要保密，但必須是隨機且唯一的

3. 存儲格式：
   - 最終存儲格式：base64(IV + 密文 + 認證標籤)
   - IV(16字節) + 密文(變長) + 標籤(16字節)

4. 安全性：
   - AES-GCM模式提供認證加密，同時確保機密性和完整性
   - 每次加密使用不同的IV，即使相同身分證字號也產生不同密文
   - 認證標籤防止密文被竄改
""" 
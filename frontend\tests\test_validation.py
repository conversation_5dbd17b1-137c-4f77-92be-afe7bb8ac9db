"""
資料驗證功能測試
"""

import pytest
import sys
import os

# 確保可以導入組件
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from components.personal_data_form import validate_id_number

class TestIDNumberValidation:
    """身分證字號驗證測試"""
    
    def test_valid_id_numbers(self, valid_id_numbers):
        """測試有效的身分證字號"""
        for id_number in valid_id_numbers:
            is_valid, error_msg = validate_id_number(id_number)
            assert is_valid, f"身分證字號 {id_number} 應該是有效的，但驗證失敗: {error_msg}"
            assert error_msg == "", f"有效身分證字號不應該有錯誤訊息: {error_msg}"
    
    def test_invalid_id_numbers(self, invalid_id_numbers):
        """測試無效的身分證字號"""
        for id_number in invalid_id_numbers:
            is_valid, error_msg = validate_id_number(id_number)
            assert not is_valid, f"身分證字號 {id_number} 應該是無效的，但驗證通過"
            assert error_msg != "", f"無效身分證字號應該有錯誤訊息"
    
    def test_empty_id_number(self):
        """測試空的身分證字號"""
        is_valid, error_msg = validate_id_number("")
        assert not is_valid
        assert "必填欄位" in error_msg
    
    def test_none_id_number(self):
        """測試None值"""
        is_valid, error_msg = validate_id_number(None)
        assert not is_valid
        assert "必填欄位" in error_msg
    
    def test_whitespace_id_number(self):
        """測試空白字串"""
        is_valid, error_msg = validate_id_number("   ")
        assert not is_valid
        assert "必填欄位" in error_msg
    
    def test_id_number_with_spaces(self):
        """測試帶空格的身分證字號"""
        # 前後空格應該被移除
        is_valid, error_msg = validate_id_number("  A123456789  ")
        assert is_valid, f"帶空格的有效身分證字號應該通過驗證: {error_msg}"
    
    def test_lowercase_id_number(self):
        """測試小寫字母"""
        is_valid, error_msg = validate_id_number("a123456789")
        assert is_valid, f"小寫字母應該被轉換為大寫並通過驗證: {error_msg}"
    
    def test_specific_invalid_cases(self):
        """測試特定的無效案例"""
        test_cases = [
            ("A123456788", "檢核碼錯誤"),  # 最後一位錯誤
            ("Z123456789", "首位字母無效"),  # Z不是有效的開頭字母
            ("1A23456789", "格式錯誤"),     # 開頭不是字母
            ("A12345678A", "格式錯誤"),     # 最後一位不是數字
        ]
        
        for id_number, expected_error_type in test_cases:
            is_valid, error_msg = validate_id_number(id_number)
            assert not is_valid, f"身分證字號 {id_number} 應該無效"
            # 可以根據需要檢查特定的錯誤訊息
    
    def test_boundary_lengths(self):
        """測試邊界長度"""
        # 長度不足
        is_valid, error_msg = validate_id_number("A1234567")
        assert not is_valid
        assert "格式錯誤" in error_msg
        
        # 長度過長
        is_valid, error_msg = validate_id_number("A12345678901")
        assert not is_valid
        assert "格式錯誤" in error_msg
        
        # 正確長度
        is_valid, error_msg = validate_id_number("A123456789")
        assert is_valid

class TestFormValidation:
    """表單驗證測試"""
    
    def test_name_validation(self):
        """測試姓名驗證邏輯"""
        import re
        
        # 有效姓名
        valid_names = ["張三", "李小明", "王大華", "John Smith", "Mary Jane"]
        name_pattern = r'^[\u4e00-\u9fa5a-zA-Z\s]+$'
        
        for name in valid_names:
            assert 2 <= len(name.strip()) <= 20, f"姓名長度應該在2-20字符之間: {name}"
            assert re.match(name_pattern, name.strip()), f"姓名格式應該有效: {name}"
        
        # 無效姓名
        invalid_names = ["", "A", "123", "張三@", "a" * 21]
        
        for name in invalid_names:
            if name == "":
                assert len(name.strip()) == 0, "空字串應該被識別"
            elif name == "A":
                assert len(name.strip()) < 2, "單字符姓名應該無效"
            elif name == "123":
                assert not re.match(name_pattern, name), "純數字姓名應該無效"
            elif name == "張三@":
                assert not re.match(name_pattern, name), "包含特殊字符的姓名應該無效"
            elif name == "a" * 21:
                assert len(name.strip()) > 20, "過長姓名應該無效"
    
    def test_address_validation(self):
        """測試地址驗證邏輯"""
        # 地址長度限制
        valid_address = "台北市信義區信義路100號"
        assert len(valid_address) <= 200, "有效地址應該在長度限制內"
        
        # 過長地址
        long_address = "a" * 201
        assert len(long_address) > 200, "過長地址應該被識別"
    
    def test_notes_validation(self):
        """測試備註驗證邏輯"""
        # 備註長度限制
        valid_notes = "這是一些備註資訊"
        assert len(valid_notes) <= 500, "有效備註應該在長度限制內"
        
        # 過長備註
        long_notes = "a" * 501
        assert len(long_notes) > 500, "過長備註應該被識別"

class TestDataProcessing:
    """資料處理功能測試"""
    
    def test_data_cleaning(self):
        """測試資料清理功能"""
        # 測試字串清理
        test_data = "  測試資料  "
        cleaned = test_data.strip()
        assert cleaned == "測試資料", "應該移除前後空格"
        
        # 測試身分證字號大寫轉換
        id_number = "a123456789"
        cleaned_id = id_number.upper()
        assert cleaned_id == "A123456789", "應該轉換為大寫"
    
    def test_data_masking(self):
        """測試資料遮罩功能"""
        # 模擬身分證字號遮罩
        id_number = "A123456789"
        masked = f"{id_number[:4]}****{id_number[-2:]}"
        assert masked == "A123****89", "身分證字號應該正確遮罩"
    
    def test_form_data_preparation(self):
        """測試表單資料準備"""
        raw_data = {
            "name": "  張三  ",
            "id_number": "a123456789",
            "address": "  台北市信義區  ",
            "notes": "  測試備註  "
        }
        
        # 模擬資料清理過程
        cleaned_data = {
            "name": raw_data["name"].strip(),
            "id_number": raw_data["id_number"].strip().upper(),
            "address": raw_data["address"].strip() if raw_data["address"] else "",
            "notes": raw_data["notes"].strip() if raw_data["notes"] else ""
        }
        
        assert cleaned_data["name"] == "張三"
        assert cleaned_data["id_number"] == "A123456789"
        assert cleaned_data["address"] == "台北市信義區"
        assert cleaned_data["notes"] == "測試備註"

# 額外的驗證測試
class TestValidationEdgeCases:
    """驗證邊界案例測試"""
    
    def test_unicode_characters(self):
        """測試Unicode字符處理"""
        # 測試中文姓名
        chinese_name = "張三"
        assert len(chinese_name) == 2, "中文姓名長度計算正確"
        
        # 測試英文姓名
        english_name = "John Smith"
        assert len(english_name) == 10, "英文姓名長度計算正確"
    
    def test_special_characters_in_address(self):
        """測試地址中的特殊字符"""
        addresses = [
            "台北市信義區信義路100號1樓",
            "新北市板橋區中山路二段200號B1",
            "桃園市中壢區復興路300號3F-1"
        ]
        
        for address in addresses:
            assert len(address) <= 200, f"地址應該在長度限制內: {address}"
    
    def test_boundary_conditions(self):
        """測試邊界條件"""
        # 最短有效姓名
        min_name = "李明"
        assert len(min_name) >= 2, "最短姓名應該滿足條件"
        
        # 最長有效姓名
        max_name = "a" * 20
        assert len(max_name) <= 20, "最長姓名應該滿足條件" 
/**
 * CBA系統前端JavaScript - 優化版本
 */

// 全域變數和配置
window.CBASystem = {
    version: '1.0.0',
    debug: false,
    animations: {
        enabled: !window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        duration: 250
    },
    theme: {
        primary: '#2563eb',
        success: '#059669',
        warning: '#d97706',
        danger: '#dc2626'
    }
};

// DOM載入完成後執行
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 CBA系統前端已載入 v' + window.CBASystem.version);

    // 初始化各種功能
    initializeTooltips();
    initializeAlerts();
    initializeFormValidation();
    initializeDataTables();
    initializeConfirmDialogs();
    initializeAnimations();
    initializeAccessibility();
    initializePerformanceOptimizations();

    // 顯示載入完成
    hideLoadingIndicator();
});

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化警告框自動關閉和動畫
 */
function initializeAlerts() {
    // 為所有警告框添加動畫類
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.classList.add('fade', 'show');

        // 添加關閉按鈕事件監聽
        const closeBtn = alert.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                alert.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            });
        }
    });

    // 自動關閉成功訊息
    setTimeout(function() {
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(function(alert) {
            if (alert.classList.contains('show')) {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        });
    }, 5000);
}

/**
 * 初始化動畫效果
 */
function initializeAnimations() {
    if (!window.CBASystem.animations.enabled) return;

    // 為卡片添加進入動畫
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // 統計數字動畫
    animateCounters();

    // 懸停效果增強
    enhanceHoverEffects();
}

/**
 * 統計數字動畫
 */
function animateCounters() {
    const counters = document.querySelectorAll('.h3');

    counters.forEach(counter => {
        const target = parseInt(counter.textContent) || 0;
        if (target === 0) return;

        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 30);
    });
}

/**
 * 增強懸停效果
 */
function enhanceHoverEffects() {
    // 為按鈕添加波紋效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 初始化無障礙性功能
 */
function initializeAccessibility() {
    // 鍵盤導航增強
    document.addEventListener('keydown', function(e) {
        // ESC 鍵關閉模態框
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            });
        }

        // Tab 鍵焦點管理
        if (e.key === 'Tab') {
            const focusedElement = document.activeElement;
            if (focusedElement) {
                focusedElement.classList.add('keyboard-focused');
                setTimeout(() => {
                    focusedElement.classList.remove('keyboard-focused');
                }, 3000);
            }
        }
    });

    // 為所有互動元素添加焦點指示器
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');
    interactiveElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid var(--primary-color)';
            this.style.outlineOffset = '2px';
        });

        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

/**
 * 初始化性能優化
 */
function initializePerformanceOptimizations() {
    // 圖片懶載入
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // 防抖動搜索
    const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
    searchInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                // 執行搜索邏輯
                console.log('搜索:', this.value);
            }, 300);
        });
    });
}

/**
 * 載入指示器控制
 */
function showLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.classList.remove('d-none');
    }
}

function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.classList.add('d-none');
    }
}

/**
 * 初始化表單驗證
 */
function initializeFormValidation() {
    // Bootstrap表單驗證
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // 身分證號驗證
    var idInputs = document.querySelectorAll('input[name="id_number"]');
    idInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validateIdNumber(this);
        });
    });
    
    // 電話號碼驗證
    var phoneInputs = document.querySelectorAll('input[name="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validatePhoneNumber(this);
        });
    });
}

/**
 * 初始化資料表格
 */
function initializeDataTables() {
    // 為表格添加排序功能
    var tables = document.querySelectorAll('.table-sortable');
    tables.forEach(function(table) {
        // 這裡可以整合第三方表格排序庫
        console.log('初始化表格排序:', table);
    });
}

/**
 * 初始化確認對話框
 */
function initializeConfirmDialogs() {
    var confirmButtons = document.querySelectorAll('[data-confirm]');
    confirmButtons.forEach(function(button) {
        button.addEventListener('click', function(event) {
            var message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                event.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 驗證身分證號
 */
function validateIdNumber(input) {
    var idNumber = input.value.trim();
    var isValid = isValidIdNumber(idNumber);
    
    if (idNumber && !isValid) {
        input.classList.add('is-invalid');
        showFieldError(input, '身分證號格式不正確');
    } else {
        input.classList.remove('is-invalid');
        hideFieldError(input);
    }
    
    return isValid;
}

/**
 * 驗證電話號碼
 */
function validatePhoneNumber(input) {
    var phone = input.value.trim();
    var isValid = isValidPhoneNumber(phone);
    
    if (phone && !isValid) {
        input.classList.add('is-invalid');
        showFieldError(input, '電話號碼格式不正確');
    } else {
        input.classList.remove('is-invalid');
        hideFieldError(input);
    }
    
    return isValid;
}

/**
 * 檢查身分證號是否有效
 */
function isValidIdNumber(idNumber) {
    if (!idNumber || idNumber.length !== 10) {
        return false;
    }
    
    // 台灣身分證號驗證邏輯
    var pattern = /^[A-Z][12]\d{8}$/;
    if (!pattern.test(idNumber)) {
        return false;
    }
    
    // 檢查碼驗證
    var letterMap = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17,
        'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22, 'O': 35, 'P': 23,
        'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28, 'V': 29, 'W': 32, 'X': 30,
        'Y': 31, 'Z': 33
    };
    
    var firstLetter = idNumber.charAt(0);
    var letterValue = letterMap[firstLetter];
    
    var sum = Math.floor(letterValue / 10) + (letterValue % 10) * 9;
    
    for (var i = 1; i < 9; i++) {
        sum += parseInt(idNumber.charAt(i)) * (9 - i);
    }
    
    var checkDigit = parseInt(idNumber.charAt(9));
    var calculatedCheck = (10 - (sum % 10)) % 10;
    
    return checkDigit === calculatedCheck;
}

/**
 * 檢查電話號碼是否有效
 */
function isValidPhoneNumber(phone) {
    if (!phone) {
        return true; // 電話號碼可以為空
    }
    
    // 移除所有非數字字符
    var cleanPhone = phone.replace(/\D/g, '');
    
    // 台灣電話號碼格式
    var patterns = [
        /^09\d{8}$/, // 手機號碼
        /^0[2-8]\d{7,8}$/, // 市話
        /^[2-8]\d{7,8}$/ // 市話（不含區碼0）
    ];
    
    return patterns.some(function(pattern) {
        return pattern.test(cleanPhone);
    });
}

/**
 * 顯示欄位錯誤訊息
 */
function showFieldError(input, message) {
    hideFieldError(input);
    
    var errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
}

/**
 * 隱藏欄位錯誤訊息
 */
function hideFieldError(input) {
    var errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 顯示載入狀態
 */
function showLoading(element, text = '載入中...') {
    if (element) {
        element.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            ${text}
        `;
        element.disabled = true;
    }
}

/**
 * 隱藏載入狀態
 */
function hideLoading(element, originalText) {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
}

/**
 * 顯示成功訊息
 */
function showSuccess(message) {
    showAlert(message, 'success');
}

/**
 * 顯示錯誤訊息
 */
function showError(message) {
    showAlert(message, 'danger');
}

/**
 * 顯示警告訊息
 */
function showWarning(message) {
    showAlert(message, 'warning');
}

/**
 * 顯示資訊訊息
 */
function showInfo(message) {
    showAlert(message, 'info');
}

/**
 * 顯示警告框
 */
function showAlert(message, type = 'info') {
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '1050';
        document.body.appendChild(alertContainer);
    }
    
    var alertId = 'alert-' + Date.now();
    var alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-${getAlertIcon(type)}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自動關閉
    setTimeout(function() {
        var alert = document.getElementById(alertId);
        if (alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

/**
 * 獲取警告框圖示
 */
function getAlertIcon(type) {
    var icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * AJAX請求封裝
 */
function makeRequest(url, options = {}) {
    var defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    };
    
    var finalOptions = Object.assign(defaultOptions, options);
    
    return fetch(url, finalOptions)
        .then(function(response) {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .catch(function(error) {
            console.error('Request failed:', error);
            showError('請求失敗，請稍後再試');
            throw error;
        });
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    
    var date = new Date(dateString);
    return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 格式化數字
 */
function formatNumber(number) {
    if (number === null || number === undefined) return '-';
    return number.toLocaleString('zh-TW');
}

/**
 * 複製到剪貼簿
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            showSuccess('已複製到剪貼簿');
        }).catch(function() {
            showError('複製失敗');
        });
    } else {
        // 降級方案
        var textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('已複製到剪貼簿');
        } catch (err) {
            showError('複製失敗');
        }
        document.body.removeChild(textArea);
    }
}

// 匯出到全域
window.CBASystem.utils = {
    validateIdNumber: validateIdNumber,
    validatePhoneNumber: validatePhoneNumber,
    showLoading: showLoading,
    hideLoading: hideLoading,
    showSuccess: showSuccess,
    showError: showError,
    showWarning: showWarning,
    showInfo: showInfo,
    makeRequest: makeRequest,
    formatDate: formatDate,
    formatNumber: formatNumber,
    copyToClipboard: copyToClipboard
};

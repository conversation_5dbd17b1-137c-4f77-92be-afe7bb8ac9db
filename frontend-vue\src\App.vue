<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // 應用掛載後的初始化工作
  console.log('CBA受款人資料搜集系統 - Vue.js前端已載入')
  
  // 檢查瀏覽器兼容性
  checkBrowserCompatibility()
})

// 檢查瀏覽器兼容性
function checkBrowserCompatibility() {
  const isModernBrowser = 
    'fetch' in window &&
    'Promise' in window &&
    'localStorage' in window &&
    'sessionStorage' in window
  
  if (!isModernBrowser) {
    console.warn('瀏覽器版本過舊，可能影響系統功能')
  }
}
</script>

<style lang="scss">
// 全域樣式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// Element Plus 自定義樣式
.el-message {
  min-width: 300px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

// 自定義滾動條
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 響應式斷點
@media (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
    margin-top: 5vh !important;
  }
}
</style>

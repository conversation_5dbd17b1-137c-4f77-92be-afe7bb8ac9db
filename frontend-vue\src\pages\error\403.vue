<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <el-icon size="120" color="#f56c6c"><Lock /></el-icon>
      </div>
      <h1 class="error-title">403</h1>
      <p class="error-message">抱歉，您沒有權限訪問此頁面</p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><House /></el-icon>
          返回首頁
        </el-button>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回上頁
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Lock, House, Back } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  
  .error-content {
    text-align: center;
    padding: 48px;
    
    .error-image {
      margin-bottom: 24px;
    }
    
    .error-title {
      font-size: 72px;
      font-weight: 600;
      color: #f56c6c;
      margin: 0 0 16px 0;
    }
    
    .error-message {
      font-size: 18px;
      color: #606266;
      margin: 0 0 32px 0;
    }
    
    .error-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      
      .el-button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    .error-content {
      padding: 24px;
      
      .error-title {
        font-size: 48px;
      }
      
      .error-message {
        font-size: 16px;
      }
      
      .error-actions {
        flex-direction: column;
        align-items: center;
        
        .el-button {
          width: 200px;
        }
      }
    }
  }
}
</style>

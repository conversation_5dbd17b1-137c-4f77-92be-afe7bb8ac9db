#!/usr/bin/env python3
"""
檢查用戶狀態腳本
"""

import sys
import os

# 加入backend路徑以便導入模組
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_path)

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.user import User
from app.models.database import Base
from app.utils.jwt_auth import verify_password

# 資料庫設置
DATABASE_URL = "sqlite:///./backend/database/cba_personal_data.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def check_user_009999():
    """檢查009999用戶的狀態"""
    db = SessionLocal()
    try:
        # 查詢009999用戶
        user = db.query(User).filter(User.username == "009999").first()
        
        if not user:
            print("❌ 用戶009999不存在")
            
            # 列出所有用戶
            all_users = db.query(User).all()
            print("\n📋 現有用戶列表:")
            for u in all_users:
                print(f"  - {u.username} ({u.full_name}) - 活躍: {u.is_active}")
            
            return False
        
        print(f"✅ 找到用戶009999")
        print(f"用戶資訊:")
        print(f"  ID: {user.id}")
        print(f"  用戶名: {user.username}")
        print(f"  姓名: {user.full_name}")
        print(f"  部門: {user.department.name if user.department else '未指定'}")
        print(f"  狀態: {'啟用' if user.is_active else '停用'}")
        print(f"  建立時間: {user.created_at}")
        print(f"  最後更新: {user.updated_at}")
        
        # 檢查常見密碼
        common_passwords = [
            "password",
            "123456",
            "009999",
            "admin",
            "test",
            "password123"
        ]
        
        print("\n🔍 測試常見密碼:")
        for pwd in common_passwords:
            if verify_password(pwd, user.hashed_password):
                print(f"  ✅ 密碼 '{pwd}' 正確!")
                return True
            else:
                print(f"  ❌ 密碼 '{pwd}' 不正確")
        
        return False
        
    except Exception as e:
        print(f"❌ 檢查用戶時發生錯誤: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 檢查用戶009999狀態")
    print("=" * 40)
    
    success = check_user_009999()
    
    if not success:
        print("\n⚠️ 需要創建或重置009999用戶")
        print("建議操作:")
        print("1. 檢查用戶初始化腳本")
        print("2. 重新創建009999用戶")
        print("3. 確認正確的密碼") 
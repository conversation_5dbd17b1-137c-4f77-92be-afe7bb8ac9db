@echo off
chcp 65001 >nul
echo.
echo ========================================
echo CBA受款人資料搜集系統 - 簡化啟動器
echo (前端 + 後端雙服務架構)
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Python，請先安裝Python 3.9或更高版本
    pause
    exit /b 1
)

REM 檢查uv是否安裝
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到uv包管理器，請先安裝uv
    echo 安裝命令: pip install uv
    pause
    exit /b 1
)

echo ✅ 環境檢查通過
echo.

REM 設定環境變數
set API_BASE_URL=http://localhost:8080
set FRONTEND_URL=http://localhost:8501

echo 🚀 啟動雙服務系統...
echo   • 後端API: http://localhost:8080
echo   • 前端UI: http://localhost:8501
echo   • 整合入口: http://localhost:80 (需要nginx或整合服務器)
echo.

REM 啟動後端服務
echo 📡 啟動後端API服務...
start "CBA後端API" cmd /k "cd backend && uv run uvicorn main:app --host 0.0.0.0 --port 8080"

REM 等待後端啟動
timeout /t 5 /nobreak >nul

REM 啟動前端服務
echo 🖥️ 啟動前端UI服務...
start "CBA前端UI" cmd /k "cd frontend && streamlit run main.py --server.port 8501 --server.address 0.0.0.0 --server.headless true"

echo.
echo ✅ 服務啟動完成！
echo.
echo 📋 存取地址:
echo   • 前端UI: http://localhost:8501
echo   • 後端API: http://localhost:8080
echo   • API文檔: http://localhost:8080/docs
echo   • 健康檢查: http://localhost:8080/health
echo.
echo 💡 提示:
echo   • 如需80埠整合存取，請使用 python integrated_server.py
echo   • 按任意鍵關閉此視窗（服務將繼續運行）
echo.
pause

# CBA 人員資料調查系統 - 系統說明文件

## 1. 專案概覽

本專案是一個基於Web的人員資料管理系統，旨在提供一個集中化的平台來管理使用者、角色、權限及部門資訊。系統具備安全的身份驗證機制，並整合了單一登入(SSO)功能，以簡化使用者登入流程並自動同步部門資訊。

## 2. 系統架構

本系統採用前後端分離的架構。

- **後端 (Backend):**
  - **框架:** FastAPI
  - **語言:** Python
  - **主要職責:** 提供 RESTful API 進行資料操作、處理業務邏輯及使用者身份驗證。所有核心業務邏輯皆實作於後端。

- **前端 (Frontend):**
  - **框架:** Streamlit
  - **語言:** Python
  - **主要職責:** 提供使用者互動介面，方便管理員進行使用者、角色和部門的管理。前端透過呼叫後端 API 來完成所有操作。

- **資料庫 (Database):**
  - **類型:** SQLite
  - **主要職責:** 儲存系統所有資料，包括使用者、角色、權限、部門及審計日誌。

- **身份驗證 (Authentication):**
  - **機制:** JWT Bearer Token
  - **整合:** 支援與外部 SOAP 服務進行單一登入 (SSO)，並在使用者登入時自動同步其部門資訊。

## 3. 核心功能

- **使用者管理 (User Management):**
  - 檢視所有使用者列表，並可依使用者名稱或狀態進行篩選。
  - 系統管理員可以啟用/停用使用者帳號。
  - 系統管理員可以更新使用者的角色及所屬部門。

- **角色與權限管理 (Role & Permission Management):**
  - 完整的角色 CRUD (建立、讀取、更新、刪除) 功能。
  - 可為每個角色分配多個系統權限。
  - 權限列表由後端統一管理，確保一致性。

- **部門管理 (Department Management):**
  - 完整的部門 CRUD 功能。
  - 每個部門包含名稱和唯一的部門代碼。
  - 使用者登入時，其部門資訊會根據SSO提供方的資料自動更新。

## 4. 設定與部署

- **環境需求:**
  - Python 3.8+
  - `uv` 套件管理工具

- **後端啟動:**
  1. 進入 `backend` 目錄: `cd backend`
  2. 使用 `uv` 安裝依賴: `uv sync`
  3. 啟動後端服務: `uvicorn main:app --reload --host 0.0.0.0 --port 8000`

- **前端啟動:**
  1. 進入 `frontend` 目錄: `cd frontend`
  2. 使用 `uv` 安裝依賴: `uv sync`
  3. 啟動前端應用: `streamlit run main.py`

- **資料庫初始化:**
  - 首次啟動後端服務時，系統會自動在 `backend/database/` 目錄下建立 `cba_personal_data.db` 資料庫檔案。
  - 系統會建立所有必要的資料表。
  - 系統會新增一個預設的系統管理員帳號 (帳號: `admin`, 密碼: `admin123`) 以供首次登入使用。
  - 若偵測到資料表欄位缺失 (例如 `departments` 表缺少 `code` 欄位)，系統會嘗試自動執行簡易的 schema migration (資料庫結構遷移) 來新增欄位。

## 5. 關鍵邏輯與實作細節

- **SSO 登入流程:**
  1. 使用者在前端登入頁面觸發 SSO 登入。
  2. 後端 `sso.py` 模組會與外部的 SOAP 服務進行驗證。
  3. 驗證成功後，系統會根據 SSO 回傳的部門代碼 (`department_code`) 查找本地資料庫中是否已有對應的部門。如果沒有，則會自動建立新部門。
  4. 接著，系統會建立或更新本地使用者資料，並將其部門與上一步骤中同步後的部門進行關聯。
  5. 最後，系統簽發一個 JWT 權杖給前端，前端儲存此權杖並完成登入，之後的每次請求都會攜帶此權杖。

- **API 結構:**
  - 後端 API 依據功能進行模組化，相關的端點被組織在不同的檔案中 (例如 `backend/app/api/v1/auth.py`, `departments.py`, `users.py` 等)。這種結構使得程式碼清晰且易於未來擴充與維護。

- **安全性:**
  - 所有需要驗證的 API 端點都透過 JWT 權杖進行保護。未經驗證的請求將會收到 `401 Unauthorized` 錯誤。
  - 敏感的操作 (例如使用者管理、角色管理) 都受到權限控管。後端會驗證使用者的角色是否擁有執行該操作的權限，若無權限則會回傳 `403 Forbidden` 錯誤。 
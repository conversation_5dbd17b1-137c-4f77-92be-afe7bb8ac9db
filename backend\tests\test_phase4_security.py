"""
CBA個人資料搜集系統 - 第四階段安全測試

測試範圍：
1. 認證安全測試
2. 權限安全測試
3. 資料加密測試
4. 輸入驗證測試
5. 會話安全測試
"""

import pytest
import time
import json
import hashlib
import base64
import hmac
from typing import Dict, List, Any
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from jose import jwt, JWTError
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from main import app
from app.models.database import Base, get_db
from app.models.user import User
from app.models.personal_data import PersonalData
from app.core.config import settings
from app.utils.jwt_auth import create_access_token, verify_token
from app.utils.encryption import encrypt_data, decrypt_data
from app.core.sso import get_sso_authenticator


class TestPhase4Security:
    """第四階段安全測試"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """測試前設置"""
        # 設定測試資料庫
        self.engine = create_engine(
            "sqlite:///./test_phase4_security.db",
            connect_args={"check_same_thread": False}
        )
        Base.metadata.create_all(bind=self.engine)
        
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        def override_get_db():
            db = TestingSessionLocal()
            try:
                yield db
            finally:
                db.close()
        
        app.dependency_overrides[get_db] = override_get_db
        self.client = TestClient(app)
        
        # 測試用戶和Token
        self.test_users = self._create_test_users()
        self.test_tokens = self._create_test_tokens()
        
    def _create_test_users(self) -> Dict[str, Dict]:
        """創建測試用戶資料"""
        return {
            "general_user": {
                "id": 1,
                "username": "security_general",
                "full_name": "安全測試一般用戶",
                "role": "一般員工",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA"]
            },
            "global_user": {
                "id": 2,
                "username": "security_global",
                "full_name": "安全測試全域用戶",
                "role": "全域員工",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA"]
            },
            "admin_user": {
                "id": 3,
                "username": "security_admin",
                "full_name": "安全測試管理員",
                "role": "管理員",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS", "VIEW_AUDIT_LOG"]
            }
        }
    
    def _create_test_tokens(self) -> Dict[str, str]:
        """創建測試JWT Token"""
        tokens = {}
        for user_type, user_data in self.test_users.items():
            token_data = {
                "sub": user_data["username"],
                "user_id": user_data["id"],
                "username": user_data["username"],
                "type": user_data["role"],
                "permissions": user_data["permissions"]
            }
            tokens[user_type] = create_access_token(data=token_data)
        return tokens
    
    def _create_expired_token(self, user_data: Dict) -> str:
        """創建過期Token"""
        token_data = {
            "sub": user_data["username"],
            "user_id": user_data["id"],
            "username": user_data["username"],
            "type": user_data["role"],
            "permissions": user_data["permissions"],
            "exp": datetime.utcnow() - timedelta(hours=1)  # 1小時前過期
        }
        return jwt.encode(token_data, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    
    def _tamper_token(self, token: str) -> str:
        """篡改Token"""
        # 解碼Token，修改payload，重新編碼
        try:
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            payload["permissions"] = ["MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS"]  # 嘗試提升權限
            
            # 使用錯誤的密鑰重新編碼
            return jwt.encode(payload, "wrong_secret_key", algorithm=settings.JWT_ALGORITHM)
        except:
            return "tampered_token"
    
    # ===========================================
    # 1. 認證安全測試
    # ===========================================
    
    def test_01_jwt_token_expiry(self):
        """測試2.1.1：JWT Token過期驗證"""
        print("\n=== 測試2.1.1：JWT Token過期驗證 ===")
        
        # 創建過期Token
        expired_token = self._create_expired_token(self.test_users["general_user"])
        
        # 嘗試使用過期Token存取API
        response = self.client.get("/api/v1/auth/me", 
                                 headers={"Authorization": f"Bearer {expired_token}"})
        
        print(f"過期Token存取回應: {response.status_code}")
        
        # 應該被拒絕
        if response.status_code == 401:
            error_detail = response.json()
            print(f"錯誤詳情: {error_detail}")
            print("✅ JWT Token過期驗證測試通過")
            return True
        
        print("❌ JWT Token過期驗證測試失敗")
        return False
    
    def test_02_jwt_token_tampering(self):
        """測試2.1.1：JWT Token篡改驗證"""
        print("\n=== 測試2.1.1：JWT Token篡改驗證 ===")
        
        # 篡改Token
        valid_token = self.test_tokens["general_user"]
        tampered_token = self._tamper_token(valid_token)
        
        # 嘗試使用篡改Token存取API
        response = self.client.get("/api/v1/auth/me", 
                                 headers={"Authorization": f"Bearer {tampered_token}"})
        
        print(f"篡改Token存取回應: {response.status_code}")
        
        # 應該被拒絕
        if response.status_code == 401:
            error_detail = response.json()
            print(f"錯誤詳情: {error_detail}")
            print("✅ JWT Token篡改驗證測試通過")
            return True
        
        print("❌ JWT Token篡改驗證測試失敗")
        return False
    
    def test_03_sso_token_security(self):
        """測試2.1.2：SSO Token安全性"""
        print("\n=== 測試2.1.2：SSO Token安全性 ===")
        
        # 測試無效Token
        invalid_response = self.client.get("/api/v1/auth/sso_login?ssoToken1=invalid_token")
        print(f"無效SSO Token回應: {invalid_response.status_code}")
        
        # 測試Token重放攻擊防護
        with patch.object(get_sso_authenticator(), 'authenticate') as mock_auth:
            # 第一次使用Token（成功）
            mock_auth.return_value = {
                "success": True,
                "user_info": {
                    "帳號": "sso_security_test",
                    "姓名": "SSO安全測試用戶",
                    "source_org_no": "屏東縣政府:001:0101",
                    "ssoToken1": "replay_test_token"
                }
            }
            
            first_response = self.client.get("/api/v1/auth/sso_login?ssoToken1=replay_test_token")
            print(f"第一次SSO登入回應: {first_response.status_code}")
            
            # 第二次使用相同Token（應該被拒絕）
            mock_auth.return_value = {
                "success": False,
                "error": "TOKEN_ALREADY_USED",
                "user_info": None
            }
            
            second_response = self.client.get("/api/v1/auth/sso_login?ssoToken1=replay_test_token")
            print(f"重複SSO登入回應: {second_response.status_code}")
            
            # 驗證Token重放攻擊防護
            if second_response.status_code in [401, 403]:
                print("✅ SSO Token安全性測試通過")
                return True
        
        print("❌ SSO Token安全性測試失敗")
        return False
    
    def test_04_csrf_protection(self):
        """測試2.1.2：CSRF攻擊防護"""
        print("\n=== 測試2.1.2：CSRF攻擊防護 ===")
        
        # 模擬CSRF攻擊
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 測試沒有適當標頭的請求
        malicious_data = {
            "name": "CSRF測試用戶",
            "id_number": "X123456789",
            "address": "CSRF測試地址"
        }
        
        # 模擬來自惡意網站的請求
        csrf_headers = {
            "Authorization": f"Bearer {self.test_tokens['general_user']}",
            "Origin": "https://malicious-site.com",
            "Referer": "https://malicious-site.com/attack.html"
        }
        
        response = self.client.post("/api/v1/personal-data", 
                                  json=malicious_data, 
                                  headers=csrf_headers)
        
        print(f"CSRF攻擊回應: {response.status_code}")
        
        # 檢查是否有適當的CSRF防護
        if response.status_code in [403, 400]:
            print("✅ CSRF攻擊防護測試通過")
            return True
        elif response.status_code == 201:
            print("⚠️  CSRF攻擊防護可能需要加強")
            return False
        
        print("❌ CSRF攻擊防護測試失敗")
        return False
    
    # ===========================================
    # 2. 權限安全測試
    # ===========================================
    
    def test_05_privilege_escalation_prevention(self):
        """測試2.2.1：垂直權限提升防護"""
        print("\n=== 測試2.2.1：垂直權限提升防護 ===")
        
        # 一般用戶嘗試存取管理功能
        general_headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 嘗試存取管理用戶功能
        admin_response = self.client.get("/api/v1/admin/users", headers=general_headers)
        print(f"一般用戶存取管理功能: {admin_response.status_code}")
        
        # 嘗試存取審計日誌
        audit_response = self.client.get("/api/v1/audit/logs", headers=general_headers)
        print(f"一般用戶存取審計日誌: {audit_response.status_code}")
        
        # 嘗試修改用戶角色
        role_change_data = {"role": "管理員", "permissions": ["MANAGE_ADMIN"]}
        role_response = self.client.put("/api/v1/admin/users/1", 
                                      json=role_change_data, 
                                      headers=general_headers)
        print(f"一般用戶修改角色: {role_response.status_code}")
        
        # 所有操作都應該被拒絕
        if all(resp.status_code == 403 for resp in [admin_response, audit_response, role_response]):
            print("✅ 垂直權限提升防護測試通過")
            return True
        
        print("❌ 垂直權限提升防護測試失敗")
        return False
    
    def test_06_horizontal_privilege_bypass(self):
        """測試2.2.2：水平權限繞過防護"""
        print("\n=== 測試2.2.2：水平權限繞過防護 ===")
        
        # 創建測試資料
        user1_headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        user2_headers = {"Authorization": f"Bearer {self.test_tokens['global_user']}"}
        
        # 用戶1創建個人資料
        personal_data = {
            "name": "用戶1的資料",
            "id_number": "H123456789",
            "address": "用戶1的地址",
            "notes": "水平權限測試"
        }
        
        create_response = self.client.post("/api/v1/personal-data", 
                                         json=personal_data, 
                                         headers=user1_headers)
        print(f"用戶1創建資料: {create_response.status_code}")
        
        if create_response.status_code == 201:
            data_id = create_response.json()["id"]
            
            # 用戶2嘗試存取用戶1的資料（如果權限設計正確，應該要能存取）
            # 但要確保不是管理員才能修改他人資料
            access_response = self.client.get(f"/api/v1/personal-data/{data_id}", 
                                            headers=user2_headers)
            print(f"用戶2存取用戶1資料: {access_response.status_code}")
            
            # 用戶2嘗試修改用戶1的資料（應該被拒絕，除非有權限）
            update_data = {"notes": "用戶2嘗試修改"}
            modify_response = self.client.put(f"/api/v1/personal-data/{data_id}", 
                                            json=update_data, 
                                            headers=user2_headers)
            print(f"用戶2修改用戶1資料: {modify_response.status_code}")
            
            # 用戶2嘗試刪除用戶1的資料（應該被拒絕）
            delete_response = self.client.delete(f"/api/v1/personal-data/{data_id}", 
                                               headers=user2_headers)
            print(f"用戶2刪除用戶1資料: {delete_response.status_code}")
            
            # 檢查權限控制
            if modify_response.status_code == 403 and delete_response.status_code == 403:
                print("✅ 水平權限繞過防護測試通過")
                return True
        
        print("❌ 水平權限繞過防護測試失敗")
        return False
    
    def test_07_session_hijacking_prevention(self):
        """測試2.5.1：會話劫持防護"""
        print("\n=== 測試2.5.1：會話劫持防護 ===")
        
        # 模擬會話劫持攻擊
        valid_token = self.test_tokens["general_user"]
        
        # 正常請求
        normal_headers = {
            "Authorization": f"Bearer {valid_token}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        normal_response = self.client.get("/api/v1/auth/me", headers=normal_headers)
        print(f"正常請求回應: {normal_response.status_code}")
        
        # 模擬劫持請求（不同的User-Agent）
        hijacked_headers = {
            "Authorization": f"Bearer {valid_token}",
            "User-Agent": "Malicious-Bot/1.0",
            "X-Forwarded-For": "*************"  # 不同的IP
        }
        
        hijacked_response = self.client.get("/api/v1/auth/me", headers=hijacked_headers)
        print(f"劫持請求回應: {hijacked_response.status_code}")
        
        # 如果有會話指紋驗證，劫持請求應該被拒絕
        if hijacked_response.status_code == 401:
            print("✅ 會話劫持防護測試通過")
            return True
        elif hijacked_response.status_code == 200:
            print("⚠️  會話劫持防護可能需要加強")
            return False
        
        print("❌ 會話劫持防護測試失敗")
        return False
    
    def test_08_session_timeout_mechanism(self):
        """測試2.5.2：會話超時機制"""
        print("\n=== 測試2.5.2：會話超時機制 ===")
        
        # 創建短期過期Token
        short_expire_data = {
            "sub": self.test_users["general_user"]["username"],
            "user_id": self.test_users["general_user"]["id"],
            "username": self.test_users["general_user"]["username"],
            "type": self.test_users["general_user"]["role"],
            "permissions": self.test_users["general_user"]["permissions"]
        }
        
        # 創建1分鐘過期的Token
        short_token = create_access_token(data=short_expire_data, expires_delta=timedelta(minutes=1))
        
        # 立即使用Token（應該成功）
        immediate_headers = {"Authorization": f"Bearer {short_token}"}
        immediate_response = self.client.get("/api/v1/auth/me", headers=immediate_headers)
        print(f"立即使用Token: {immediate_response.status_code}")
        
        # 等待Token過期（在實際測試中可能需要調整等待時間）
        time.sleep(2)  # 等待2秒模擬時間流逝
        
        # 再次使用Token（應該失敗，如果有適當的過期檢查）
        expired_response = self.client.get("/api/v1/auth/me", headers=immediate_headers)
        print(f"Token過期後使用: {expired_response.status_code}")
        
        # 檢查會話超時
        if immediate_response.status_code == 200 and expired_response.status_code == 401:
            print("✅ 會話超時機制測試通過")
            return True
        elif immediate_response.status_code == 200:
            print("⚠️  會話超時機制可能需要調整")
            return False
        
        print("❌ 會話超時機制測試失敗")
        return False
    
    # ===========================================
    # 3. 資料加密測試
    # ===========================================
    
    def test_09_id_number_encryption_storage(self):
        """測試2.3.1：身分證字號加密儲存"""
        print("\n=== 測試2.3.1：身分證字號加密儲存 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 新增個人資料
        personal_data = {
            "name": "加密測試用戶",
            "id_number": "A123456789",
            "address": "加密測試地址",
            "notes": "身分證字號加密測試"
        }
        
        create_response = self.client.post("/api/v1/personal-data", 
                                         json=personal_data, 
                                         headers=headers)
        print(f"新增資料回應: {create_response.status_code}")
        
        if create_response.status_code == 201:
            data_id = create_response.json()["id"]
            
            # 直接查詢資料庫，檢查身分證字號是否加密
            with self.engine.connect() as conn:
                result = conn.execute(
                    text("SELECT id_number_encrypted FROM personal_data WHERE id = :id"),
                    {"id": data_id}
                ).fetchone()
                
                if result:
                    encrypted_id = result[0]
                    print(f"資料庫中的加密身分證字號: {encrypted_id[:20]}...")
                    
                    # 驗證加密
                    if encrypted_id != "A123456789" and len(encrypted_id) > 20:
                        # 測試解密
                        try:
                            decrypted_id = decrypt_data(encrypted_id)
                            print(f"解密後身分證字號: {decrypted_id}")
                            
                            if decrypted_id == "A123456789":
                                print("✅ 身分證字號加密儲存測試通過")
                                return True
                        except Exception as e:
                            print(f"解密失敗: {str(e)}")
            
        print("❌ 身分證字號加密儲存測試失敗")
        return False
    
    def test_10_data_transmission_security(self):
        """測試2.3.2：資料傳輸安全性"""
        print("\n=== 測試2.3.2：資料傳輸安全性 ===")
        
        # 檢查API是否強制使用HTTPS（在生產環境中）
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 測試API回應是否包含安全標頭
        response = self.client.get("/api/v1/auth/me", headers=headers)
        print(f"API回應狀態: {response.status_code}")
        
        if response.status_code == 200:
            # 檢查安全標頭
            security_headers = {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block"
            }
            
            present_headers = []
            for header_name, expected_value in security_headers.items():
                if header_name in response.headers:
                    present_headers.append(header_name)
                    print(f"安全標頭 {header_name}: {response.headers[header_name]}")
            
            # 檢查是否有敏感資料洩漏
            response_data = response.json()
            if "password" not in response_data and "secret" not in response_data:
                print("✅ 資料傳輸安全性測試通過")
                return True
        
        print("❌ 資料傳輸安全性測試失敗")
        return False
    
    # ===========================================
    # 4. 輸入驗證測試
    # ===========================================
    
    def test_11_sql_injection_prevention(self):
        """測試2.4.1：SQL注入防護"""
        print("\n=== 測試2.4.1：SQL注入防護 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 嘗試SQL注入攻擊
        sql_injection_payloads = [
            "'; DROP TABLE personal_data; --",
            "' OR '1'='1",
            "'; INSERT INTO personal_data (name) VALUES ('hacked'); --",
            "' UNION SELECT * FROM users --"
        ]
        
        injection_blocked = 0
        
        for payload in sql_injection_payloads:
            malicious_data = {
                "name": payload,
                "id_number": "A123456789",
                "address": "測試地址",
                "notes": "SQL注入測試"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=malicious_data, 
                                      headers=headers)
            
            print(f"SQL注入負載 '{payload[:20]}...' 回應: {response.status_code}")
            
            # 如果回應是400或422，表示被成功阻擋
            if response.status_code in [400, 422]:
                injection_blocked += 1
            elif response.status_code == 201:
                # 檢查是否真的插入了惡意資料
                created_data = response.json()
                if created_data.get("name") == payload:
                    print(f"⚠️  SQL注入負載被存入資料庫: {payload}")
        
        success_rate = injection_blocked / len(sql_injection_payloads)
        print(f"SQL注入防護成功率: {success_rate:.2%}")
        
        if success_rate >= 0.8:  # 80%以上被阻擋
            print("✅ SQL注入防護測試通過")
            return True
        
        print("❌ SQL注入防護測試失敗")
        return False
    
    def test_12_xss_attack_prevention(self):
        """測試2.4.2：XSS攻擊防護"""
        print("\n=== 測試2.4.2：XSS攻擊防護 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 嘗試XSS攻擊
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>"
        ]
        
        xss_blocked = 0
        
        for payload in xss_payloads:
            malicious_data = {
                "name": payload,
                "id_number": "A123456789",
                "address": "測試地址",
                "notes": "XSS攻擊測試"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=malicious_data, 
                                      headers=headers)
            
            print(f"XSS負載 '{payload[:20]}...' 回應: {response.status_code}")
            
            if response.status_code == 201:
                # 檢查回應是否進行了適當的編碼
                created_data = response.json()
                returned_name = created_data.get("name", "")
                
                # 檢查是否包含原始腳本標籤
                if "<script>" in returned_name or "javascript:" in returned_name:
                    print(f"⚠️  XSS負載未被適當處理: {payload}")
                else:
                    xss_blocked += 1
                    print(f"✅ XSS負載被適當處理")
            elif response.status_code in [400, 422]:
                xss_blocked += 1
                print(f"✅ XSS負載被阻擋")
        
        success_rate = xss_blocked / len(xss_payloads)
        print(f"XSS攻擊防護成功率: {success_rate:.2%}")
        
        if success_rate >= 0.8:  # 80%以上被防護
            print("✅ XSS攻擊防護測試通過")
            return True
        
        print("❌ XSS攻擊防護測試失敗")
        return False
    
    # ===========================================
    # 測試執行主程式
    # ===========================================
    
    def run_all_security_tests(self):
        """執行所有安全測試"""
        print("\n" + "="*60)
        print("🔒 開始執行第四階段安全測試")
        print("="*60)
        
        test_methods = [
            self.test_01_jwt_token_expiry,
            self.test_02_jwt_token_tampering,
            self.test_03_sso_token_security,
            self.test_04_csrf_protection,
            self.test_05_privilege_escalation_prevention,
            self.test_06_horizontal_privilege_bypass,
            self.test_07_session_hijacking_prevention,
            self.test_08_session_timeout_mechanism,
            self.test_09_id_number_encryption_storage,
            self.test_10_data_transmission_security,
            self.test_11_sql_injection_prevention,
            self.test_12_xss_attack_prevention
        ]
        
        results = []
        for i, test_method in enumerate(test_methods, 1):
            try:
                result = test_method()
                results.append((f"安全測試{i:02d}", test_method.__name__, result))
            except Exception as e:
                print(f"安全測試{i:02d}執行異常: {str(e)}")
                results.append((f"安全測試{i:02d}", test_method.__name__, False))
        
        # 輸出測試結果摘要
        print("\n" + "="*60)
        print("🔐 第四階段安全測試結果摘要")
        print("="*60)
        
        passed_count = 0
        failed_count = 0
        warning_count = 0
        
        for test_id, test_name, result in results:
            if result is True:
                status = "✅ 通過"
                passed_count += 1
            elif result is False:
                status = "❌ 失敗"
                failed_count += 1
            else:
                status = "⚠️  警告"
                warning_count += 1
            
            print(f"{test_id}: {status} - {test_name}")
        
        print(f"\n📊 安全測試統計:")
        print(f"通過: {passed_count}/{len(results)} ({passed_count/len(results)*100:.1f}%)")
        print(f"失敗: {failed_count}/{len(results)} ({failed_count/len(results)*100:.1f}%)")
        print(f"警告: {warning_count}/{len(results)} ({warning_count/len(results)*100:.1f}%)")
        
        # 安全測試評估
        if passed_count == len(results):
            print("\n🎉 所有安全測試通過！系統安全性良好。")
        elif failed_count == 0:
            print("\n⚠️  部分測試有警告，建議加強安全措施。")
        else:
            print(f"\n🚨 {failed_count}項安全測試失敗，存在安全風險！")
        
        return passed_count == len(results)


# 如果直接執行此檔案，運行所有測試
if __name__ == "__main__":
    test_runner = TestPhase4Security()
    test_runner.setup_method()
    test_runner.run_all_security_tests() 
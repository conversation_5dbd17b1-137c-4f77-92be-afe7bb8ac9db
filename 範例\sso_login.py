@app.get("/sso_login")
async def sso_login(request: Request, ssoToken1: str = None, db: Session = Depends(get_db)):
    try:
        artifact = ssoToken1
        print(f"artifact: {artifact}")
        if not artifact:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

        logging.info(f"收到 SSO Token: {artifact}")

        # 使用 SOAP 呼叫 getUserProfile
        try:
            transport = Transport()
            client = Client(settings.SSO_SOAP_WS_URL, transport=transport)
            result = client.service.getUserProfile(artifact)
            logging.info(f"SOAP 呼叫結果: {result}")
            
            if result:  # 如果成功獲取用戶信息
                # 解析 XML 格式的 result
                from xml.etree import ElementTree as ET
                root = ET.fromstring(result)
                if root.tag == 'Error':
                    return RedirectResponse(url="/login?error=無法取得使用者資訊", status_code=status.HTTP_302_FOUND)
                
                # 讀取帳號及部門
                account = root.find('帳號').text
                original_dept_code = root.find('單位代碼').text
                bureau_code = original_dept_code[:2] + "00"  # 轉換為處層級代碼 (如 "02" -> "0200")
                full_name = root.find('姓名').text  # 讀取用戶姓名
                
                # 檢查部門是否存在，不存在則創建
                department = db.query(Department).filter(Department.code == bureau_code).first()
                if not department:
                    # 如果部門不存在，創建新部門
                    department_name = root.find('機關名稱').text if root.find('機關名稱') is not None else f"處{bureau_code}"
                    department = Department(
                        code=bureau_code,
                        name=department_name
                    )
                    db.add(department)
                    db.commit()
                    db.refresh(department)
                
                # 檢查用戶是否存在，不存在則創建
                user = db.query(User).filter(User.username == account).first()
                if not user:
                    # 獲取一般員工角色
                    employee_role = db.query(Role).filter(Role.name == "一般員工").first()
                    if not employee_role:
                        # 如果一般員工角色不存在，則創建
                        employee_role = Role(
                            name="一般員工",
                            description="一般員工角色",
                            permissions=["view_questions", "create_reports"]  # 設置基本權限
                        )
                        db.add(employee_role)
                        db.commit()
                        db.refresh(employee_role)
                    
                    # 創建新用戶並設置角色
                    user = User(
                        username=account,
                        full_name=full_name,
                        department_id=department.id,
                        hashed_password="sso_user",
                        is_active=True
                    )
                    db.add(user)
                    db.commit()
                    db.refresh(user)
                    
                    # 添加到多對多關係
                    user.departments.append(department)
                    user.roles.append(employee_role)
                    db.commit()
                else:
                    # 更新現有用戶資訊
                    user.full_name = full_name
                    user.department_id = department.id
                    
                    # 確保用戶在多對多關係中有此部門
                    if department not in user.departments:
                        user.departments.append(department)
                    
                    # 確保用戶有一般員工角色
                    employee_role = db.query(Role).filter(Role.name == "一般員工").first()
                    if employee_role and employee_role not in user.roles:
                        user.roles.append(employee_role)
                    
                    db.commit()
                    db.refresh(user)
                
                # 創建 JWT token
                access_token = create_access_token(
                    data={"sub": account},
                    expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
                )
                
                response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)
                response.set_cookie(
                    key="access_token",
                    value=f"Bearer {access_token}",
                    httponly=True
                )
                return response
                
            else:
                return RedirectResponse(url="/login?error=無法取得使用者資訊", status_code=status.HTTP_302_FOUND)
                
        except Exception as soap_error:
            logging.error(f"SOAP 呼叫失敗: {str(soap_error)}")
            return RedirectResponse(url=f"/login?error=SOAP呼叫失敗:{str(soap_error)}", status_code=status.HTTP_302_FOUND)

    except Exception as e:
        logging.error(f"登入過程發生錯誤: {str(e)}")
        return RedirectResponse(url=f"/login?error=系統錯誤:{str(e)}", status_code=status.HTTP_302_FOUND)

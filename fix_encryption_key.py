#!/usr/bin/env python3
"""
修正加密金鑰格式問題
產生正確的 base64 編碼的 32 字節加密金鑰
"""

import os
import base64
import secrets

def generate_valid_encryption_key() -> str:
    """
    產生32字節的安全加密金鑰並base64編碼
    """
    # 產生32字節的隨機金鑰
    key_bytes = secrets.token_bytes(32)
    # 轉換為base64編碼
    key_base64 = base64.b64encode(key_bytes).decode('utf-8')
    return key_base64

def update_env_file(env_file_path: str, new_key: str):
    """
    更新 .env 檔案中的 ENCRYPTION_KEY
    """
    if not os.path.exists(env_file_path):
        print(f"❌ 檔案不存在: {env_file_path}")
        return False
    
    # 讀取現有內容
    with open(env_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 更新 ENCRYPTION_KEY
    updated = False
    for i, line in enumerate(lines):
        if line.strip().startswith('ENCRYPTION_KEY='):
            lines[i] = f'ENCRYPTION_KEY={new_key}\n'
            updated = True
            print(f"✅ 更新 {env_file_path} 中的 ENCRYPTION_KEY")
            break
    
    if not updated:
        # 如果沒找到，就添加到檔案末尾
        lines.append(f'\nENCRYPTION_KEY={new_key}\n')
        print(f"✅ 添加 ENCRYPTION_KEY 到 {env_file_path}")
    
    # 寫回檔案
    with open(env_file_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    return True

def test_encryption_with_new_key(key: str) -> bool:
    """
    測試新金鑰是否可以正常加密解密
    """
    try:
        # 設定環境變數
        os.environ['ENCRYPTION_KEY'] = key
        
        # 導入加密模組
        import sys
        sys.path.append('./backend')
        from app.utils.encryption import encrypt_id_number, decrypt_id_number
        
        # 測試加密解密
        test_id = "A123456789"
        encrypted = encrypt_id_number(test_id)
        decrypted = decrypt_id_number(encrypted)
        
        if decrypted == test_id:
            print("✅ 加密解密測試通過")
            return True
        else:
            print("❌ 加密解密測試失敗：結果不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 加密解密測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔧 修正加密金鑰格式問題")
    print("=" * 50)
    
    # 1. 產生新的加密金鑰
    print("1️⃣ 產生新的加密金鑰...")
    new_key = generate_valid_encryption_key()
    print(f"✅ 新金鑰產生完成 (長度: {len(new_key)} 字元)")
    print(f"📋 新金鑰: {new_key}")
    
    # 2. 測試新金鑰
    print("\n2️⃣ 測試新金鑰...")
    if not test_encryption_with_new_key(new_key):
        print("❌ 新金鑰測試失敗，停止更新")
        return False
    
    # 3. 更新 backend/.env
    print("\n3️⃣ 更新環境變數檔案...")
    backend_env = './backend/.env'
    
    if update_env_file(backend_env, new_key):
        print(f"✅ 已更新 {backend_env}")
    else:
        print(f"❌ 更新 {backend_env} 失敗")
        return False
    
    # 4. 最終驗證
    print("\n4️⃣ 最終驗證...")
    
    # 重新載入環境變數
    from dotenv import load_dotenv
    load_dotenv('./backend/.env', override=True)
    
    # 再次測試
    if test_encryption_with_new_key(new_key):
        print("🎉 加密金鑰修正完成！")
        print("\n📋 後續步驟:")
        print("1. 重新啟動後端服務")
        print("2. 測試個人資料建立功能")
        print("3. 確認不再出現加密錯誤")
        return True
    else:
        print("❌ 最終驗證失敗")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 修正完成！")
    else:
        print("\n❌ 修正失敗！") 
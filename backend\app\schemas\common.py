"""
通用的回應模型
"""
from typing import Any, Optional
from pydantic import BaseModel


class ResponseBase(BaseModel):
    """基礎回應模型"""
    success: bool
    message: str
    data: Optional[Any] = None


class SuccessResponse(ResponseBase):
    """成功回應模型"""
    success: bool = True


class ErrorResponse(ResponseBase):
    """錯誤回應模型"""
    success: bool = False
    error_code: Optional[str] = None


class MessageResponse(BaseModel):
    """簡單訊息回應模型"""
    message: str 
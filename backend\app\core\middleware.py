"""
自訂中間件模組
"""
import time
import logging
import json
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.models.database import get_db
from app.utils.audit import log_operation
from app.core.exceptions import CBAException

logger = logging.getLogger(__name__)


class UnifiedErrorHandlingMiddleware(BaseHTTPMiddleware):
    """統一錯誤處理中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            return await self._handle_exception(request, exc)
    
    async def _handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """統一錯誤處理"""
        
        # 如果是CBA自訂例外
        if isinstance(exc, CBAException):
            # 組裝錯誤回應內容並記錄
            content = {
                "success": False,
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details
                },
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
            
            # 記錄更詳細的錯誤信息
            logger.error(f"CBAException: {exc.error_code} - {exc.message}")
            logger.error(f"詳細信息: {exc.details}")
            logger.error(f"請求路徑: {request.url.path}")
            logger.error(f"請求方法: {request.method}")
            
            # 如果是權限相關錯誤，記錄更多信息
            if exc.error_code == "PERMISSION_DENIED":
                # 嘗試從請求中獲取用戶信息
                try:
                    auth_header = request.headers.get("Authorization", "")
                    if auth_header.startswith("Bearer "):
                        from ..utils.jwt_auth import decode_token
                        token = auth_header.replace("Bearer ", "")
                        try:
                            payload = decode_token(token)
                            user_info = {
                                "user_id": payload.get("user_id"),
                                "username": payload.get("username"),
                                "roles": payload.get("roles", []),
                                "permissions": payload.get("permissions", [])
                            }
                            logger.error(f"權限被拒絕的用戶信息: {user_info}")
                        except Exception as e:
                            logger.error(f"無法解析令牌: {e}")
                except Exception as e:
                    logger.error(f"獲取用戶信息時出錯: {e}")
            
            return JSONResponse(
                status_code=self._get_status_code_for_exception(exc),
                content=content
            )
        
        # HTTP例外（FastAPI）
        from fastapi import HTTPException
        if isinstance(exc, HTTPException):
            # 檢查detail是否已經是結構化格式
            if isinstance(exc.detail, dict):
                error_detail = exc.detail
            else:
                error_detail = {
                    "code": f"HTTP_{exc.status_code}",
                    "message": str(exc.detail)
                }
            
            content = {
                "success": False,
                "error": error_detail,
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
            
            # 記錄詳細的HTTP錯誤信息
            logger.error(f"HTTPException: {exc.status_code} - {exc.detail}")
            logger.error(f"請求路徑: {request.url.path}")
            logger.error(f"請求方法: {request.method}")
            
            # 對於403錯誤，記錄更多信息
            if exc.status_code == 403:
                logger.error(f"權限被拒絕: {exc.detail}")
                # 嘗試從請求中獲取用戶信息
                try:
                    auth_header = request.headers.get("Authorization", "")
                    if auth_header.startswith("Bearer "):
                        from ..utils.jwt_auth import decode_token
                        token = auth_header.replace("Bearer ", "")
                        try:
                            payload = decode_token(token)
                            user_info = {
                                "user_id": payload.get("user_id"),
                                "username": payload.get("username"),
                                "roles": payload.get("roles", []),
                                "permissions": payload.get("permissions", [])
                            }
                            logger.error(f"權限被拒絕的用戶信息: {user_info}")
                        except Exception as e:
                            logger.error(f"無法解析令牌: {e}")
                except Exception as e:
                    logger.error(f"獲取用戶信息時出錯: {e}")
            
            return JSONResponse(
                status_code=exc.status_code,
                content=content,
                headers=getattr(exc, 'headers', {})
            )
        
        # 其他未預期的例外
        logger.error(f"未預期的錯誤: {exc}", exc_info=True)
        logger.error(f"請求路徑: {request.url.path}")
        logger.error(f"請求方法: {request.method}")
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "系統內部錯誤，請稍後再試"
                },
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
        )
    
    def _get_status_code_for_exception(self, exc: CBAException) -> int:
        """根據例外類型決定HTTP狀態碼"""
        error_code_mapping = {
            "VALIDATION_ERROR": 400,
            "AUTHENTICATION_ERROR": 401,
            "TOKEN_EXPIRED": 401,
            "INVALID_TOKEN": 401,
            "PERMISSION_DENIED": 403,
            "PERSONAL_DATA_NOT_FOUND": 404,
            "USER_NOT_FOUND": 404,
            "DUPLICATE_ID_NUMBER": 409,
            "DATABASE_ERROR": 500,
            "ENCRYPTION_ERROR": 500,
            "SSO_ERROR": 502
        }
        return error_code_mapping.get(exc.error_code, 400)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全性標頭中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全標頭
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        # 暫時使用寬鬆的CSP設定來解決載入問題
        response.headers["Content-Security-Policy"] = (
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https:; "
            "style-src 'self' 'unsafe-inline' https:; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; "
            "img-src 'self' data: https: blob:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "object-src 'none'"
        )
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """請求日誌中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 取得客戶端資訊
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        method = request.method
        url = str(request.url)
        
        # 處理請求
        response = await call_next(request)
        
        # 計算處理時間
        process_time = time.time() - start_time
        
        # 記錄日誌基本資訊
        log_data = {
            "method": method,
            "url": url,
            "status_code": response.status_code,
            "process_time": f"{process_time:.3f}",
            "client_ip": client_ip,
            "user_agent": user_agent[:100]
        }
        # 如果回應失敗，嘗試獲取並記錄完整的 JSON 回應主體
        if response.status_code >= 400:
            try:
                # JSONResponse 類型會將 body 存儲為 bytes
                raw_body = response.body  # type: ignore
                if isinstance(raw_body, (bytes, bytearray)):
                    text = raw_body.decode('utf-8')
                    try:
                        parsed = json.loads(text)
                    except Exception:
                        parsed = text
                else:
                    parsed = raw_body
                log_data['response_body'] = parsed
            except Exception:
                # 無法獲取回應主體時忽略
                pass
        # 實際記錄
        if response.status_code >= 400:
            logger.warning(f"請求失敗: {log_data}")
        else:
            logger.info(f"請求成功: {log_data}")
        
        # 添加回應標頭
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class AuditLoggingMiddleware(BaseHTTPMiddleware):
    """審計日誌中間件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 只對特定路徑進行審計
        audit_paths = ["/api/v1/personal-data", "/api/v1/auth"]
        should_audit = any(request.url.path.startswith(path) for path in audit_paths)
        
        if not should_audit:
            return await call_next(request)
        
        # 取得請求資訊
        method = request.method
        path = request.url.path
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        
        # 處理請求
        response = await call_next(request)
        
        # 只記錄成功的修改操作
        if response.status_code < 400 and method in ["POST", "PUT", "DELETE"]:
            try:
                # 這裡可以根據需要記錄特定的操作
                # 實際的審計日誌會在服務層處理
                pass
            except Exception as e:
                logger.error(f"審計日誌記錄失敗: {e}")
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """簡單的速率限制中間件"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理過期記錄
        if client_ip in self.clients:
            self.clients[client_ip] = [
                timestamp for timestamp in self.clients[client_ip]
                if current_time - timestamp < self.period
            ]
        
        # 檢查速率限制
        if client_ip not in self.clients:
            self.clients[client_ip] = []
        
        if len(self.clients[client_ip]) >= self.calls:
            logger.warning(f"速率限制觸發: {client_ip}")
            return Response(
                content="請求過於頻繁，請稍後再試",
                status_code=429,
                headers={"Retry-After": str(self.period)}
            )
        
        # 記錄請求時間
        self.clients[client_ip].append(current_time)
        
        return await call_next(request) 
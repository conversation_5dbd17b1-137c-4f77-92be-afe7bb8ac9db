/**
 * 通用類型定義
 */

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  error?: string
}

export interface PaginationParams {
  page: number
  size: number
}

export interface PaginationResponse {
  total: number
  page: number
  size: number
  pages: number
}

export interface ListResponse<T> extends PaginationResponse {
  data: T[]
}

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  align?: 'left' | 'center' | 'right'
}

export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any, callback: any) => void
}

export interface Department {
  id: string
  name: string
  code?: string
  description?: string
  parent_id?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface AuditLog {
  id: string
  user_id: string
  username: string
  action: string
  resource: string
  resource_id?: string
  details?: Record<string, any>
  ip_address?: string
  user_agent?: string
  timestamp: string
}

export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  filename?: string
  columns?: string[]
  filters?: Record<string, any>
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface ErrorInfo {
  message: string
  code?: string | number
  details?: any
}

<template>
  <div class="app-header">
    <div class="header-left">
      <el-button
        type="text"
        size="large"
        @click="$emit('toggle-sidebar')"
      >
        <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
      </el-button>
      
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item 
            v-for="item in breadcrumbItems" 
            :key="item.path"
            :to="item.path"
          >
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 通知 -->
      <el-badge :value="notificationCount" :hidden="notificationCount === 0">
        <el-button type="text" size="large">
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>
      
      <!-- 用戶菜單 -->
      <el-dropdown @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userName }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              個人資料
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系統設定
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              登出
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Expand,
  Fold,
  Bell,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

interface Props {
  sidebarCollapsed: boolean
}

defineProps<Props>()
defineEmits<{
  'toggle-sidebar': []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 通知數量
const notificationCount = ref(3)

// 用戶資訊
const userName = computed(() => authStore.userName || '用戶')
const userAvatar = computed(() => {
  // 這裡可以根據用戶資訊返回頭像URL
  return ''
})

// 麵包屑導航
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const items = matched.map(item => ({
    path: item.path,
    title: item.meta.title as string
  }))
  
  // 如果不是首頁，添加首頁鏈接
  if (route.path !== '/') {
    items.unshift({
      path: '/',
      title: '首頁'
    })
  }
  
  return items
})

// 處理用戶菜單命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
      
    case 'settings':
      ElMessage.info('系統設定功能開發中...')
      break
      
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '確定要登出嗎？',
          '確認登出',
          {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await authStore.logout()
        router.push('/auth/login')
      } catch (error) {
        // 用戶取消登出
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 24px;
  background: #fff;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .breadcrumb {
      :deep(.el-breadcrumb__inner) {
        color: #606266;
        
        &.is-link {
          color: #409eff;
          
          &:hover {
            color: #66b1ff;
          }
        }
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .username {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }
      
      .dropdown-icon {
        font-size: 12px;
        color: #909399;
        transition: transform 0.3s ease;
      }
      
      &:hover .dropdown-icon {
        transform: rotate(180deg);
      }
    }
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
    
    .header-left {
      gap: 8px;
      
      .breadcrumb {
        display: none;
      }
    }
    
    .header-right {
      gap: 8px;
      
      .user-info .username {
        display: none;
      }
    }
  }
}
</style>

"""
CBA個人資料搜集系統 - 第四階段系統整合測試

測試範圍：
1. 完整業務流程測試
2. 跨系統整合測試
3. 異常處理測試
4. 端到端功能驗證
"""

import pytest
import asyncio
import httpx
import time
import json
from typing import Dict, List, Any
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from main import app
from app.models.database import Base, get_db
from app.models.user import User
from app.models.personal_data import PersonalData
from app.models.audit_log import AuditLog
from app.core.config import settings
from app.utils.jwt_auth import create_access_token
from app.utils.encryption import encrypt_data, decrypt_data
from app.core.sso import get_sso_authenticator


class TestPhase4Integration:
    """第四階段系統整合測試"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """測試前設置"""
        # 設定測試資料庫
        self.engine = create_engine(
            "sqlite:///./test_phase4_integration.db",
            connect_args={"check_same_thread": False}
        )
        Base.metadata.create_all(bind=self.engine)
        
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        def override_get_db():
            db = TestingSessionLocal()
            try:
                yield db
            finally:
                db.close()
        
        app.dependency_overrides[get_db] = override_get_db
        self.client = TestClient(app)
        
        # 測試資料準備
        self.test_users = self._create_test_users()
        self.test_tokens = self._create_test_tokens()
        
    def _create_test_users(self) -> Dict[str, Dict]:
        """創建測試用戶資料"""
        return {
            "general_user": {
                "id": 1,
                "username": "test_general",
                "full_name": "測試一般用戶",
                "role": "一般員工",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA"]
            },
            "global_user": {
                "id": 2,
                "username": "test_global",
                "full_name": "測試全域用戶",
                "role": "全域員工",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA"]
            },
            "admin_user": {
                "id": 3,
                "username": "test_admin",
                "full_name": "測試管理員",
                "role": "管理員",
                "permissions": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA", "DELETE_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS", "VIEW_AUDIT_LOG"]
            }
        }
    
    def _create_test_tokens(self) -> Dict[str, str]:
        """創建測試JWT Token"""
        tokens = {}
        for user_type, user_data in self.test_users.items():
            token_data = {
                "sub": user_data["username"],
                "user_id": user_data["id"],
                "username": user_data["username"],
                "type": user_data["role"],
                "permissions": user_data["permissions"]
            }
            tokens[user_type] = create_access_token(data=token_data)
        return tokens
    
    # ===========================================
    # 1. 完整業務流程測試
    # ===========================================
    
    def test_01_complete_user_registration_flow(self):
        """測試1.1.1：完整用戶註冊登入流程"""
        print("\n=== 測試1.1.1：完整用戶註冊登入流程 ===")
        
        # 模擬SSO登入
        with patch.object(get_sso_authenticator(), 'authenticate') as mock_auth:
            mock_auth.return_value = {
                "success": True,
                "user_info": {
                    "帳號": "new_user",
                    "姓名": "新測試用戶",
                    "source_org_no": "屏東縣政府:001:0101",
                    "ssoToken1": "test_token_123"
                }
            }
            
            # 1. SSO登入
            response = self.client.get("/api/v1/auth/sso_login?ssoToken1=test_token_123")
            print(f"SSO登入回應: {response.status_code}")
            
            # 2. 驗證用戶建立
            if response.status_code == 200:
                # 3. 驗證用戶可正常使用系統
                # 取得用戶資訊
                auth_response = self.client.get("/api/v1/auth/me")
                print(f"用戶資訊回應: {auth_response.status_code}")
                
                if auth_response.status_code == 200:
                    user_info = auth_response.json()
                    print(f"用戶資訊: {user_info}")
                    
                    # 4. 驗證權限分配
                    assert "permissions" in user_info
                    assert len(user_info["permissions"]) > 0
                    print("✅ 完整用戶註冊登入流程測試通過")
                    return True
        
        print("❌ 完整用戶註冊登入流程測試失敗")
        return False
    
    def test_02_complete_personal_data_management_flow(self):
        """測試1.1.2：完整個人資料管理流程"""
        print("\n=== 測試1.1.2：完整個人資料管理流程 ===")
        
        # 使用一般用戶身份
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 1. 新增個人資料
        personal_data = {
            "name": "測試個人資料",
            "id_number": "A123456789",
            "address": "台北市信義區測試路123號",
            "notes": "第四階段測試資料"
        }
        
        create_response = self.client.post("/api/v1/personal-data", 
                                         json=personal_data, 
                                         headers=headers)
        print(f"新增資料回應: {create_response.status_code}")
        
        if create_response.status_code == 201:
            created_data = create_response.json()
            data_id = created_data["id"]
            
            # 2. 查詢個人資料
            read_response = self.client.get(f"/api/v1/personal-data/{data_id}", 
                                          headers=headers)
            print(f"查詢資料回應: {read_response.status_code}")
            
            if read_response.status_code == 200:
                read_data = read_response.json()
                
                # 3. 更新個人資料
                update_data = {
                    "name": "更新測試個人資料",
                    "address": "台北市中正區更新路456號",
                    "notes": "第四階段更新測試資料"
                }
                
                update_response = self.client.put(f"/api/v1/personal-data/{data_id}",
                                                json=update_data,
                                                headers=headers)
                print(f"更新資料回應: {update_response.status_code}")
                
                if update_response.status_code == 200:
                    # 4. 驗證資料正確儲存和加密
                    updated_data = update_response.json()
                    assert updated_data["name"] == "更新測試個人資料"
                    assert updated_data["address"] == "台北市中正區更新路456號"
                    
                    # 5. 刪除個人資料
                    delete_response = self.client.delete(f"/api/v1/personal-data/{data_id}",
                                                       headers=headers)
                    print(f"刪除資料回應: {delete_response.status_code}")
                    
                    if delete_response.status_code == 200:
                        print("✅ 完整個人資料管理流程測試通過")
                        return True
        
        print("❌ 完整個人資料管理流程測試失敗")
        return False
    
    def test_03_permission_level_switching_flow(self):
        """測試1.1.3：權限切換流程"""
        print("\n=== 測試1.1.3：權限切換流程 ===")
        
        # 準備測試資料
        admin_headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        global_headers = {"Authorization": f"Bearer {self.test_tokens['global_user']}"}
        general_headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 1. 一般用戶權限測試
        general_response = self.client.get("/api/v1/personal-data", headers=general_headers)
        print(f"一般用戶查詢權限: {general_response.status_code}")
        
        # 2. 全域用戶權限測試
        global_response = self.client.get("/api/v1/personal-data", headers=global_headers)
        print(f"全域用戶查詢權限: {global_response.status_code}")
        
        # 3. 管理者權限測試
        admin_response = self.client.get("/api/v1/admin/users", headers=admin_headers)
        print(f"管理者查詢權限: {admin_response.status_code}")
        
        # 4. 權限隔離測試
        # 一般用戶嘗試存取管理功能
        general_admin_response = self.client.get("/api/v1/admin/users", headers=general_headers)
        print(f"一般用戶存取管理功能: {general_admin_response.status_code}")
        
        # 驗證權限隔離
        if general_admin_response.status_code == 403:
            print("✅ 權限切換流程測試通過")
            return True
        
        print("❌ 權限切換流程測試失敗")
        return False
    
    # ===========================================
    # 2. 跨系統整合測試
    # ===========================================
    
    def test_04_sso_system_integration(self):
        """測試1.2.1：SSO系統整合"""
        print("\n=== 測試1.2.1：SSO系統整合 ===")
        
        # 測試SOAP SSO整合
        with patch.object(get_sso_authenticator(), 'authenticate') as mock_auth:
            # 模擬成功的SSO回應
            mock_auth.return_value = {
                "success": True,
                "user_info": {
                    "帳號": "sso_test_user",
                    "姓名": "SSO測試用戶",
                    "source_org_no": "屏東縣政府:001:0101",
                    "ssoToken1": "valid_sso_token"
                }
            }
            
            # 測試有效Token
            response = self.client.get("/api/v1/auth/sso_login?ssoToken1=valid_sso_token")
            print(f"有效SSO Token回應: {response.status_code}")
            
            if response.status_code == 200:
                # 測試無效Token
                mock_auth.return_value = {
                    "success": False,
                    "error": "INVALID_TOKEN",
                    "user_info": None
                }
                
                invalid_response = self.client.get("/api/v1/auth/sso_login?ssoToken1=invalid_token")
                print(f"無效SSO Token回應: {invalid_response.status_code}")
                
                if invalid_response.status_code in [302, 401]:
                    print("✅ SSO系統整合測試通過")
                    return True
        
        print("❌ SSO系統整合測試失敗")
        return False
    
    def test_05_database_integration(self):
        """測試1.2.2：資料庫整合"""
        print("\n=== 測試1.2.2：資料庫整合 ===")
        
        try:
            # 測試資料庫連接
            db_response = self.client.get("/health")
            print(f"資料庫健康檢查: {db_response.status_code}")
            
            if db_response.status_code == 200:
                health_data = db_response.json()
                
                # 測試事務處理
                headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
                
                # 開始事務測試
                personal_data = {
                    "name": "事務測試資料",
                    "id_number": "B123456789",
                    "address": "事務測試地址",
                    "notes": "資料庫整合測試"
                }
                
                create_response = self.client.post("/api/v1/personal-data", 
                                                 json=personal_data, 
                                                 headers=headers)
                print(f"事務測試建立: {create_response.status_code}")
                
                if create_response.status_code == 201:
                    # 測試資料一致性
                    created_data = create_response.json()
                    data_id = created_data["id"]
                    
                    # 查詢確認資料存在
                    read_response = self.client.get(f"/api/v1/personal-data/{data_id}", 
                                                  headers=headers)
                    
                    if read_response.status_code == 200:
                        print("✅ 資料庫整合測試通過")
                        return True
            
        except Exception as e:
            print(f"資料庫整合測試異常: {str(e)}")
        
        print("❌ 資料庫整合測試失敗")
        return False
    
    # ===========================================
    # 3. 異常處理測試
    # ===========================================
    
    def test_06_network_exception_handling(self):
        """測試1.3.1：網路異常處理"""
        print("\n=== 測試1.3.1：網路異常處理 ===")
        
        # 模擬網路異常
        with patch.object(get_sso_authenticator(), 'authenticate') as mock_auth:
            # 模擬網路超時
            mock_auth.side_effect = Exception("Network timeout")
            
            response = self.client.get("/api/v1/auth/sso_login?ssoToken1=test_token")
            print(f"網路異常回應: {response.status_code}")
            
            # 應該回傳適當的錯誤狀態
            if response.status_code in [500, 502, 503]:
                print("✅ 網路異常處理測試通過")
                return True
        
        print("❌ 網路異常處理測試失敗")
        return False
    
    def test_07_database_exception_handling(self):
        """測試1.3.2：資料庫異常處理"""
        print("\n=== 測試1.3.2：資料庫異常處理 ===")
        
        # 使用無效的資料庫連接測試
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 測試無效ID查詢
        invalid_response = self.client.get("/api/v1/personal-data/99999", headers=headers)
        print(f"無效ID查詢回應: {invalid_response.status_code}")
        
        if invalid_response.status_code == 404:
            # 測試資料格式錯誤
            invalid_data = {
                "name": "",  # 空名稱
                "id_number": "invalid_id",  # 無效身分證
                "address": "測試地址"
            }
            
            format_response = self.client.post("/api/v1/personal-data", 
                                             json=invalid_data, 
                                             headers=headers)
            print(f"格式錯誤回應: {format_response.status_code}")
            
            if format_response.status_code in [400, 422]:
                print("✅ 資料庫異常處理測試通過")
                return True
        
        print("❌ 資料庫異常處理測試失敗")
        return False
    
    # ===========================================
    # 4. 端到端功能驗證測試
    # ===========================================
    
    def test_08_end_to_end_business_process(self):
        """測試完整端到端業務流程"""
        print("\n=== 測試完整端到端業務流程 ===")
        
        # 完整業務流程：登入 -> 新增資料 -> 查詢 -> 更新 -> 審計檢查
        
        # 1. 管理者登入
        admin_headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        # 2. 新增個人資料
        personal_data = {
            "name": "端到端測試用戶",
            "id_number": "C123456789",
            "address": "端到端測試地址",
            "notes": "完整業務流程測試"
        }
        
        create_response = self.client.post("/api/v1/personal-data", 
                                         json=personal_data, 
                                         headers=admin_headers)
        print(f"新增資料: {create_response.status_code}")
        
        if create_response.status_code == 201:
            created_data = create_response.json()
            data_id = created_data["id"]
            
            # 3. 查詢資料
            read_response = self.client.get(f"/api/v1/personal-data/{data_id}", 
                                          headers=admin_headers)
            print(f"查詢資料: {read_response.status_code}")
            
            if read_response.status_code == 200:
                # 4. 更新資料
                update_data = {
                    "name": "更新端到端測試用戶",
                    "notes": "已更新的完整業務流程測試"
                }
                
                update_response = self.client.put(f"/api/v1/personal-data/{data_id}",
                                                json=update_data,
                                                headers=admin_headers)
                print(f"更新資料: {update_response.status_code}")
                
                if update_response.status_code == 200:
                    # 5. 檢查審計日誌
                    audit_response = self.client.get("/api/v1/audit/logs", 
                                                   headers=admin_headers)
                    print(f"審計日誌: {audit_response.status_code}")
                    
                    if audit_response.status_code == 200:
                        print("✅ 端到端業務流程測試通過")
                        return True
        
        print("❌ 端到端業務流程測試失敗")
        return False
    
    def test_09_concurrent_user_operations(self):
        """測試併發用戶操作"""
        print("\n=== 測試併發用戶操作 ===")
        
        # 模擬多個用戶同時操作
        import threading
        import time
        
        results = []
        
        def user_operation(user_type, operation_id):
            """用戶操作函數"""
            headers = {"Authorization": f"Bearer {self.test_tokens[user_type]}"}
            
            personal_data = {
                "name": f"併發測試用戶{operation_id}",
                "id_number": f"D{operation_id:08d}",
                "address": f"併發測試地址{operation_id}",
                "notes": f"併發測試{operation_id}"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=personal_data, 
                                      headers=headers)
            results.append(response.status_code)
        
        # 創建多個併發操作
        threads = []
        for i in range(5):
            user_type = ["general_user", "global_user", "admin_user"][i % 3]
            thread = threading.Thread(target=user_operation, args=(user_type, i))
            threads.append(thread)
            thread.start()
        
        # 等待所有操作完成
        for thread in threads:
            thread.join()
        
        # 檢查結果
        success_count = sum(1 for status in results if status == 201)
        print(f"併發操作成功數: {success_count}/{len(results)}")
        
        if success_count >= len(results) * 0.8:  # 80%成功率
            print("✅ 併發用戶操作測試通過")
            return True
        
        print("❌ 併發用戶操作測試失敗")
        return False
    
    def test_10_data_consistency_verification(self):
        """測試資料一致性驗證"""
        print("\n=== 測試資料一致性驗證 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        
        # 1. 新增多筆資料
        test_data = []
        for i in range(3):
            personal_data = {
                "name": f"一致性測試用戶{i}",
                "id_number": f"E{i:08d}",
                "address": f"一致性測試地址{i}",
                "notes": f"資料一致性測試{i}"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=personal_data, 
                                      headers=headers)
            if response.status_code == 201:
                test_data.append(response.json())
        
        print(f"新增資料數: {len(test_data)}")
        
        # 2. 查詢所有資料
        list_response = self.client.get("/api/v1/personal-data", headers=headers)
        print(f"查詢所有資料: {list_response.status_code}")
        
        if list_response.status_code == 200:
            all_data = list_response.json()
            
            # 3. 驗證資料一致性
            created_ids = {item["id"] for item in test_data}
            retrieved_ids = {item["id"] for item in all_data["items"]}
            
            # 檢查新增的資料是否都能查詢到
            missing_ids = created_ids - retrieved_ids
            
            if len(missing_ids) == 0:
                print("✅ 資料一致性驗證測試通過")
                return True
            else:
                print(f"遺失資料ID: {missing_ids}")
        
        print("❌ 資料一致性驗證測試失敗")
        return False
    
    # ===========================================
    # 5. 系統穩定性測試
    # ===========================================
    
    def test_11_system_stability_under_load(self):
        """測試系統負載下的穩定性"""
        print("\n=== 測試系統負載下的穩定性 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 執行連續操作測試
        operation_count = 10
        success_count = 0
        
        for i in range(operation_count):
            personal_data = {
                "name": f"穩定性測試用戶{i}",
                "id_number": f"F{i:08d}",
                "address": f"穩定性測試地址{i}",
                "notes": f"系統穩定性測試{i}"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=personal_data, 
                                      headers=headers)
            if response.status_code == 201:
                success_count += 1
            
            # 短暫延遲
            time.sleep(0.1)
        
        success_rate = success_count / operation_count
        print(f"操作成功率: {success_rate:.2%}")
        
        if success_rate >= 0.9:  # 90%成功率
            print("✅ 系統穩定性測試通過")
            return True
        
        print("❌ 系統穩定性測試失敗")
        return False
    
    def test_12_memory_leak_detection(self):
        """測試記憶體洩漏檢測"""
        print("\n=== 測試記憶體洩漏檢測 ===")
        
        import psutil
        import os
        
        # 取得當前進程
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 執行重複操作
        for i in range(50):
            personal_data = {
                "name": f"記憶體測試用戶{i}",
                "id_number": f"G{i:08d}",
                "address": f"記憶體測試地址{i}",
                "notes": f"記憶體洩漏測試{i}"
            }
            
            response = self.client.post("/api/v1/personal-data", 
                                      json=personal_data, 
                                      headers=headers)
            
            # 每10次檢查一次記憶體
            if i % 10 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_growth = current_memory - initial_memory
                print(f"第{i}次操作後記憶體使用: {current_memory:.2f} MB (增長: {memory_growth:.2f} MB)")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_growth = final_memory - initial_memory
        
        print(f"總記憶體增長: {total_growth:.2f} MB")
        
        # 如果記憶體增長少於100MB，視為通過
        if total_growth < 100:
            print("✅ 記憶體洩漏檢測測試通過")
            return True
        
        print("❌ 記憶體洩漏檢測測試失敗")
        return False
    
    def test_13_response_time_consistency(self):
        """測試回應時間一致性"""
        print("\n=== 測試回應時間一致性 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        response_times = []
        
        # 測試多次查詢的回應時間
        for i in range(10):
            start_time = time.time()
            response = self.client.get("/api/v1/personal-data", headers=headers)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 轉換為毫秒
            response_times.append(response_time)
            
            print(f"第{i+1}次查詢回應時間: {response_time:.2f}ms")
        
        # 計算統計數據
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        print(f"平均回應時間: {avg_response_time:.2f}ms")
        print(f"最大回應時間: {max_response_time:.2f}ms")
        print(f"最小回應時間: {min_response_time:.2f}ms")
        
        # 檢查回應時間一致性（最大值不超過平均值的3倍）
        if max_response_time <= avg_response_time * 3 and avg_response_time < 1000:
            print("✅ 回應時間一致性測試通過")
            return True
        
        print("❌ 回應時間一致性測試失敗")
        return False
    
    def test_14_error_recovery_mechanism(self):
        """測試錯誤恢復機制"""
        print("\n=== 測試錯誤恢復機制 ===")
        
        headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 1. 故意產生錯誤
        invalid_data = {
            "name": "",  # 空名稱
            "id_number": "invalid",  # 無效身分證
            "address": "測試地址"
        }
        
        error_response = self.client.post("/api/v1/personal-data", 
                                        json=invalid_data, 
                                        headers=headers)
        print(f"錯誤請求回應: {error_response.status_code}")
        
        if error_response.status_code in [400, 422]:
            # 2. 恢復正常操作
            valid_data = {
                "name": "恢復測試用戶",
                "id_number": "H123456789",
                "address": "恢復測試地址",
                "notes": "錯誤恢復測試"
            }
            
            recovery_response = self.client.post("/api/v1/personal-data", 
                                               json=valid_data, 
                                               headers=headers)
            print(f"恢復請求回應: {recovery_response.status_code}")
            
            if recovery_response.status_code == 201:
                print("✅ 錯誤恢復機制測試通過")
                return True
        
        print("❌ 錯誤恢復機制測試失敗")
        return False
    
    def test_15_audit_log_completeness(self):
        """測試審計日誌完整性"""
        print("\n=== 測試審計日誌完整性 ===")
        
        admin_headers = {"Authorization": f"Bearer {self.test_tokens['admin_user']}"}
        general_headers = {"Authorization": f"Bearer {self.test_tokens['general_user']}"}
        
        # 執行一系列操作
        operations = []
        
        # 1. 新增操作
        personal_data = {
            "name": "審計測試用戶",
            "id_number": "I123456789",
            "address": "審計測試地址",
            "notes": "審計日誌測試"
        }
        
        create_response = self.client.post("/api/v1/personal-data", 
                                         json=personal_data, 
                                         headers=general_headers)
        if create_response.status_code == 201:
            operations.append("CREATE")
            data_id = create_response.json()["id"]
            
            # 2. 更新操作
            update_data = {"notes": "已更新的審計日誌測試"}
            update_response = self.client.put(f"/api/v1/personal-data/{data_id}",
                                            json=update_data,
                                            headers=general_headers)
            if update_response.status_code == 200:
                operations.append("UPDATE")
                
                # 3. 查詢操作
                read_response = self.client.get(f"/api/v1/personal-data/{data_id}",
                                              headers=general_headers)
                if read_response.status_code == 200:
                    operations.append("READ")
        
        # 4. 檢查審計日誌
        audit_response = self.client.get("/api/v1/audit/logs", headers=admin_headers)
        print(f"審計日誌查詢回應: {audit_response.status_code}")
        
        if audit_response.status_code == 200:
            audit_logs = audit_response.json()
            
            # 檢查操作是否都被記錄
            logged_operations = [log["action"] for log in audit_logs.get("items", [])]
            
            print(f"執行操作: {operations}")
            print(f"記錄操作: {logged_operations}")
            
            # 檢查是否所有操作都被記錄
            recorded_count = sum(1 for op in operations if op in logged_operations)
            
            if recorded_count == len(operations):
                print("✅ 審計日誌完整性測試通過")
                return True
        
        print("❌ 審計日誌完整性測試失敗")
        return False
    
    # ===========================================
    # 測試執行主程式
    # ===========================================
    
    def run_all_integration_tests(self):
        """執行所有系統整合測試"""
        print("\n" + "="*60)
        print("🚀 開始執行第四階段系統整合測試")
        print("="*60)
        
        test_methods = [
            self.test_01_complete_user_registration_flow,
            self.test_02_complete_personal_data_management_flow,
            self.test_03_permission_level_switching_flow,
            self.test_04_sso_system_integration,
            self.test_05_database_integration,
            self.test_06_network_exception_handling,
            self.test_07_database_exception_handling,
            self.test_08_end_to_end_business_process,
            self.test_09_concurrent_user_operations,
            self.test_10_data_consistency_verification,
            self.test_11_system_stability_under_load,
            self.test_12_memory_leak_detection,
            self.test_13_response_time_consistency,
            self.test_14_error_recovery_mechanism,
            self.test_15_audit_log_completeness
        ]
        
        results = []
        for i, test_method in enumerate(test_methods, 1):
            try:
                result = test_method()
                results.append((f"測試{i:02d}", test_method.__name__, result))
            except Exception as e:
                print(f"測試{i:02d}執行異常: {str(e)}")
                results.append((f"測試{i:02d}", test_method.__name__, False))
        
        # 輸出測試結果摘要
        print("\n" + "="*60)
        print("📊 第四階段系統整合測試結果摘要")
        print("="*60)
        
        passed_count = 0
        failed_count = 0
        
        for test_id, test_name, result in results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_id}: {status} - {test_name}")
            
            if result:
                passed_count += 1
            else:
                failed_count += 1
        
        print(f"\n📈 測試統計:")
        print(f"通過: {passed_count}/{len(results)} ({passed_count/len(results)*100:.1f}%)")
        print(f"失敗: {failed_count}/{len(results)} ({failed_count/len(results)*100:.1f}%)")
        
        if passed_count == len(results):
            print("\n🎉 所有系統整合測試通過！")
        else:
            print(f"\n⚠️  {failed_count}項測試失敗，需要修正。")
        
        return passed_count == len(results)


# 如果直接執行此檔案，運行所有測試
if __name__ == "__main__":
    test_runner = TestPhase4Integration()
    test_runner.setup_method()
    test_runner.run_all_integration_tests() 
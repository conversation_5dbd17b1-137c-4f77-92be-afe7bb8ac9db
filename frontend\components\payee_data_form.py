"""
受款人資料輸入表單組件
"""

import streamlit as st
import re
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import sys
import os

# 將父目錄加入sys.path以便導入utils模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from utils.api_client import api_client
    from utils.auth_manager import AuthManager
except ImportError:
    # 如果導入失敗，建立模擬物件
    class MockApiClient:
        def validate_id_number(self, *args, **kwargs):
            return {"valid": True, "exists": False}
        def create_payee_data(self, *args, **kwargs):
            return {"id": 1, "message": "成功"}
    
    api_client = MockApiClient()
    
    class MockAuthManager:
        @staticmethod
        def require_authentication():
            pass
        @staticmethod
        def can_create_data():
            return True
    
    AuthManager = MockAuthManager()

def validate_id_number(id_number: str) -> Tuple[bool, str]:
    """
    驗證身分證字號格式
    
    Args:
        id_number: 身分證字號
        
    Returns:
        (is_valid, error_message)
    """
    if not id_number:
        return False, "身分證字號為必填欄位"
    
    # 移除空格
    id_number = id_number.strip().upper()
    
    # 檢查格式：第一位英文字母，後續9位數字
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False, "身分證字號格式錯誤，應為1位英文字母加9位數字"
    
    # 身分證字號檢核邏輯
    # 英文字母對應數字表
    letter_mapping = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    first_letter = id_number[0]
    if first_letter not in letter_mapping:
        return False, "身分證字號首位字母無效"
    
    # 計算檢核碼
    letter_value = letter_mapping[first_letter]
    total = (letter_value // 10) + (letter_value % 10) * 9
    
    for i in range(1, 9):
        total += int(id_number[i]) * (9 - i)
    
    check_digit = (10 - (total % 10)) % 10
    
    if check_digit != int(id_number[9]):
        return False, "身分證字號檢核碼錯誤"
    
    return True, ""

def validate_name(name: str) -> Tuple[bool, str]:
    """
    驗證姓名格式
    
    Args:
        name: 姓名
        
    Returns:
        (is_valid, error_message)
    """
    if not name or not name.strip():
        return False, "姓名為必填欄位"
    
    name = name.strip()
    
    # 檢查長度
    if len(name) < 2 or len(name) > 10:
        return False, "姓名長度應為2-10個字元"
    
    # 檢查字元（只允許中文、英文、數字）
    if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s]+$', name):
        return False, "姓名只能包含中文、英文字母、數字和空格"
    
    return True, ""

def render_payee_data_form():
    """渲染受款人資料輸入表單"""
    # 檢查認證和權限
    AuthManager.require_authentication()
    
    if not AuthManager.can_create_data():
        st.error("您沒有權限建立受款人資料")
        return
    
    st.title("📝 新增受款人資料")
    st.write("請填寫完整的受款人資料，帶 * 的欄位為必填項目")
    
    # 檢查是否有成功訊息要顯示
    if st.session_state.get('form_success', False):
        st.success("✅ 資料已成功儲存！")
        st.session_state.form_success = False
        # 清除表單資料
        if 'form_data' in st.session_state:
            del st.session_state.form_data
    
    # 建立表單
    with st.form("payee_data_form"):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 姓名欄位
            name = st.text_input(
                "姓名 *",
                placeholder="請輸入真實姓名",
                help="請輸入與身分證相符的姓名",
                max_chars=10,
                value=st.session_state.get('form_data', {}).get('name', '')
            )
            
            # 身分證字號欄位
            id_number = st.text_input(
                "身分證字號 *",
                placeholder="A123456789",
                help="請輸入10位身分證字號（1位英文字母+9位數字）",
                max_chars=10,
                value=st.session_state.get('form_data', {}).get('id_number', '')
            )
            
            # 地址欄位
            address = st.text_area(
                "地址 *",
                placeholder="請輸入詳細地址",
                help="請輸入完整的聯絡地址",
                height=80,
                max_chars=200,
                value=st.session_state.get('form_data', {}).get('address', '')
            )
            
            # 備註欄位
            notes = st.text_area(
                "備註",
                placeholder="其他相關資訊（選填）",
                help="可填寫其他需要記錄的資訊",
                height=80,
                max_chars=500,
                value=st.session_state.get('form_data', {}).get('notes', '')
            )
        
        with col2:
            st.subheader("📋 填寫說明")
            st.info("""
            **注意事項：**
            
            • 姓名請與身分證相符
            • 身分證字號格式：1位英文字母+9位數字
            • 地址建議填寫完整
            • 備註欄位為選填項目
            
            **資料安全：**
            
            • 身分證字號將加密儲存
            • 受款人資料受到嚴格保護
            • 符合個資法規範
            """)
            
            # 顯示當前用戶資訊
            current_user = AuthManager.get_current_user()
            if current_user:
                st.divider()
                st.write("**建立者資訊：**")
                st.write(f"👤 {current_user.get('full_name', '')}")
                st.write(f"🏢 {current_user.get('department', '')}")
        
        # 提交按鈕區域
        st.divider()
        
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col2:
            submitted = st.form_submit_button(
                "💾 儲存資料",
                use_container_width=True,
                type="primary"
            )
        
        # 表單驗證和提交邏輯
        if submitted:
            # 初始化錯誤訊息
            errors = []
            
            # 驗證姓名
            name_valid, name_error = validate_name(name)
            if not name_valid:
                errors.append(name_error)
            
            # 驗證身分證字號
            id_valid, id_error = validate_id_number(id_number)
            if not id_valid:
                errors.append(id_error)
            
            # 驗證地址
            if not address or not address.strip():
                errors.append("地址為必填欄位")
            
            # 如果有錯誤，顯示錯誤訊息
            if errors:
                for error in errors:
                    st.error(error)
                return
            
            # 檢查身分證字號是否已存在
            try:
                validation_result = api_client.validate_id_number(id_number)
                if validation_result.get("exists", False):
                    st.error("此身分證字號已存在於系統中")
                    return
            except Exception as e:
                st.error(f"驗證身分證字號時發生錯誤: {str(e)}")
                return
            
            # 準備資料
            data = {
                "name": name.strip(),
                "id_number": id_number.strip().upper(),
                "address": address.strip() if address else None,
                "notes": notes.strip() if notes else None
            }
            
            # 儲存資料
            try:
                with st.spinner("正在儲存資料..."):
                    result = api_client.create_payee_data(data)
                
                if result.get("id"):
                    st.success("✅ 資料儲存成功！")
                    st.info(f"📋 受款人資料已建立，編號：{result.get('id')}")
                    # 設定成功標記，避免重複執行
                    st.session_state.form_success = True
                    # 延遲後清空表單
                    st.balloons()
                    st.rerun()
            except Exception as e:
                st.error(f"儲存資料時發生錯誤: {str(e)}")

def render_payee_data_input_page():
    """渲染受款人資料輸入頁面"""
    # 檢查認證
    AuthManager.require_authentication()
    
    st.title("📝 受款人資料管理")
    
    # 建立頁籤，分離新增和編輯功能
    tab1, tab2 = st.tabs(["新增受款人資料", "編輯受款人資料"])
    
    with tab1:
        render_payee_data_form()
    
    with tab2:
        render_payee_data_edit_form()

def render_payee_data_edit_form():
    """渲染受款人資料編輯表單"""
    if not AuthManager.can_update_data():
        st.warning("您沒有權限編輯受款人資料")
        return
    
    st.subheader("✏️ 編輯受款人資料")
    
    # 搜尋要編輯的資料
    search_col1, search_col2 = st.columns([3, 1])
    with search_col1:
        search_term = st.text_input("搜尋受款人", placeholder="輸入姓名或ID進行搜尋")
    
    with search_col2:
        search_button = st.button("🔍 搜尋", use_container_width=True)
    
    if search_term and search_button:
        with st.spinner("搜尋中..."):
            # 呼叫API搜尋資料
            result = api_client.get_payee_data_list(search=search_term, size=10)
            
            if result.get("items"):
                st.success(f"找到 {len(result['items'])} 筆資料")
                
                # 顯示搜尋結果供選擇
                selected_id = st.selectbox(
                    "選擇要編輯的資料",
                    options=[item["id"] for item in result["items"]],
                    format_func=lambda x: f"ID: {x} - {next((item['name'] for item in result['items'] if item['id'] == x), '')}"
                )
                
                if selected_id:
                    # 取得選定的資料
                    selected_data = next((item for item in result["items"] if item["id"] == selected_id), None)
                    
                    if selected_data:
                        # 顯示編輯表單
                        with st.form("edit_payee_data_form"):
                            st.write("編輯受款人資料，帶 * 的欄位為必填項目")
                            
                            col1, col2 = st.columns([2, 1])
                            with col1:
                                name = st.text_input("姓名 *", value=selected_data.get("name", ""))
                                id_number = st.text_input("身分證字號 *", value=selected_data.get("id_number", ""))
                                address = st.text_area("地址 *", value=selected_data.get("address", ""), height=100)
                            
                            with col2:
                                notes = st.text_area("備註", value=selected_data.get("notes", ""), height=150)
                            
                            submit = st.form_submit_button("💾 儲存變更", use_container_width=True, type="primary")
                            
                            if submit:
                                if not name or not id_number or not address:
                                    st.error("請填寫所有必填欄位")
                                else:
                                    # 準備更新資料
                                    update_data = {
                                        "name": name,
                                        "id_number": id_number,
                                        "address": address,
                                        "notes": notes
                                    }
                                    
                                    # 呼叫API更新資料
                                    try:
                                        api_client.update_payee_data(selected_id, update_data)
                                        st.success("✅ 資料已成功更新！")
                                        time.sleep(1)
                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"更新失敗: {str(e)}")
            else:
                st.info("未找到符合條件的資料")

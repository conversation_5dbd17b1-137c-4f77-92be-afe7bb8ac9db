/**
 * 受款人相關API
 */
import { http } from '@/utils/request'
import type { 
  Payee, 
  PayeeCreateRequest, 
  PayeeUpdateRequest, 
  PayeeQueryParams, 
  PayeeListResponse,
  PayeeStatistics 
} from '@/types/payee'

export const payeeApi = {
  /**
   * 獲取受款人列表
   */
  getPayees(params?: PayeeQueryParams) {
    return http.get<PayeeListResponse>('/api/v1/payee-data/', params)
  },
  
  /**
   * 獲取受款人詳情
   */
  getPayee(id: string) {
    return http.get<Payee>(`/api/v1/payee-data/${id}`)
  },
  
  /**
   * 創建受款人
   */
  createPayee(data: PayeeCreateRequest) {
    return http.post<Payee>('/api/v1/payee-data/', data)
  },
  
  /**
   * 更新受款人
   */
  updatePayee(id: string, data: PayeeUpdateRequest) {
    return http.put<Payee>(`/api/v1/payee-data/${id}`, data)
  },
  
  /**
   * 刪除受款人
   */
  deletePayee(id: string) {
    return http.delete(`/api/v1/payee-data/${id}`)
  },
  
  /**
   * 批量刪除受款人
   */
  batchDeletePayees(ids: string[]) {
    return http.post('/api/v1/payee-data/batch-delete', { ids })
  },
  
  /**
   * 啟用/停用受款人
   */
  togglePayeeStatus(id: string, isActive: boolean) {
    return http.patch<Payee>(`/api/v1/payee-data/${id}/status`, { 
      is_active: isActive 
    })
  },
  
  /**
   * 搜尋受款人
   */
  searchPayees(query: string, params?: Partial<PayeeQueryParams>) {
    return http.get<PayeeListResponse>('/api/v1/payee-data/search', {
      q: query,
      ...params
    })
  },
  
  /**
   * 匯出受款人資料
   */
  exportPayees(params?: {
    format?: 'excel' | 'csv'
    filters?: PayeeQueryParams
  }) {
    return http.get('/api/v1/payee-data/export', params)
  },
  
  /**
   * 匯入受款人資料
   */
  importPayees(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return http.upload('/api/v1/payee-data/import', formData)
  },
  
  /**
   * 獲取受款人統計資料
   */
  getPayeeStatistics() {
    return http.get<PayeeStatistics>('/api/v1/payee-data/statistics')
  },
  
  /**
   * 驗證身分證號
   */
  validateIdNumber(idNumber: string) {
    return http.post<{ is_valid: boolean; message?: string }>('/api/v1/payee-data/validate-id', {
      id_number: idNumber
    })
  },
  
  /**
   * 檢查受款人是否重複
   */
  checkDuplicate(data: { id_number?: string; account_number?: string }) {
    return http.post<{ is_duplicate: boolean; existing_payee?: Payee }>('/api/v1/payee-data/check-duplicate', data)
  }
}

"""
API客戶端服務
處理與後端FastAPI的所有通信
"""

import os
import requests
import streamlit as st
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

class APIClient:
    """API客戶端類別"""
    
    def __init__(self):
        self.base_url = os.getenv("API_BASE_URL", "http://localhost:8080")
        self.api_url = f"{self.base_url}/api/v1"
        self.timeout = 30
        self.session = requests.Session()
    
    def _get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """取得請求標頭"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        if include_auth and hasattr(st.session_state, 'access_token') and st.session_state.access_token:
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """處理API回應"""
        try:
            # 檢查HTTP狀態碼
            if response.status_code == 401:
                # 未授權，清除session並要求重新登入
                st.session_state.logged_in = False
                st.session_state.access_token = None
                st.session_state.user_info = None
                st.error("登入已過期，請重新登入")
                st.rerun()
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"API請求失敗: {response.status_code}"
            try:
                error_detail = response.json().get("detail", str(e))
                error_msg = f"{error_msg} - {error_detail}"
            except:
                error_msg = f"{error_msg} - {str(e)}"
            
            st.error(error_msg)
            raise Exception(error_msg)
        
        except requests.exceptions.RequestException as e:
            error_msg = f"網路連接錯誤: {str(e)}"
            st.error(error_msg)
            raise Exception(error_msg)
    
    # 認證相關API
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用戶登入"""
        url = f"{self.api_url}/auth/login-json"
        data = {
            "username": username,
            "password": password
        }
        
        try:
            response = self.session.post(
                url,
                json=data,
                headers=self._get_headers(include_auth=False),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"登入失敗: {str(e)}")
            raise
    
    def logout(self) -> Dict[str, Any]:
        """用戶登出"""
        url = f"{self.api_url}/auth/logout"
        
        try:
            response = self.session.post(
                url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"登出失敗: {str(e)}")
            raise
    
    def verify_token(self) -> Dict[str, Any]:
        """驗證Token"""
        url = f"{self.api_url}/auth/verify"
        
        try:
            response = self.session.post(
                url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            # Token驗證失敗時靜默處理
            return {"valid": False}
    
    def get_current_user(self) -> Dict[str, Any]:
        """取得當前用戶資訊"""
        url = f"{self.api_url}/auth/me"
        
        try:
            response = self.session.get(
                url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"取得用戶資訊失敗: {str(e)}")
            raise
    
    def get_sso_login_url(self, redirect_url: Optional[str] = None) -> str:
        """取得SSO登入URL"""
        url = f"{self.api_url}/auth/sso_login"
        if redirect_url:
            url += f"?redirect_url={redirect_url}"
        return url
    
    # 受款人資料相關API
    def create_payee_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """建立受款人資料"""
        url = f"{self.api_url}/payee-data/"
        
        try:
            response = self.session.post(
                url,
                json=data,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"建立受款人資料失敗: {str(e)}")
            raise
    
    def get_payee_data_list(self, 
                              search: Optional[str] = None,
                              name: Optional[str] = None,
                              address: Optional[str] = None,
                              department: Optional[str] = None,
                              page: int = 1,
                              size: int = 20) -> Dict[str, Any]:
        """取得受款人資料列表"""
        url = f"{self.api_url}/payee-data/"
        
        params = {"page": page, "size": size}
        if search:
            params["search"] = search
        if name:
            params["name"] = name
        if address:
            params["address"] = address
        if department:
            params["department"] = department
        
        try:
            response = self.session.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"查詢受款人資料失敗: {str(e)}")
            raise
    
    def get_payee_data(self, data_id: int) -> Dict[str, Any]:
        """取得特定受款人資料"""
        url = f"{self.api_url}/payee-data/{data_id}"
        
        try:
            response = self.session.get(
                url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"取得受款人資料失敗: {str(e)}")
            raise
    
    def update_payee_data(self, data_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新受款人資料"""
        url = f"{self.api_url}/payee-data/{data_id}"
        
        try:
            response = self.session.put(
                url,
                json=data,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新受款人資料失敗: {str(e)}")
            raise
    
    def delete_payee_data(self, data_id: int) -> Dict[str, Any]:
        """刪除受款人資料"""
        url = f"{self.api_url}/payee-data/{data_id}"
        
        try:
            response = self.session.delete(
                url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"刪除受款人資料失敗: {str(e)}")
            raise
    
    def validate_id_number(self, id_number: str, exclude_id: Optional[int] = None) -> Dict[str, Any]:
        """驗證身分證字號"""
        url = f"{self.api_url}/payee-data/validation/id-number/{id_number}"
        
        params = {}
        if exclude_id:
            params["exclude_id"] = exclude_id
        
        try:
            response = self.session.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            result = self._handle_response(response)
            
            # 適配後端回傳格式，添加前端期望的 valid 字段
            return {
                "valid": True,  # 如果API呼叫成功，表示格式有效
                "exists": result.get("exists", False),
                "message": result.get("message", ""),
                "id_number": result.get("id_number", id_number)
            }
        except Exception as e:
            # 格式無效或其他錯誤
            return {
                "valid": False,
                "exists": False,
                "message": str(e),
                "id_number": id_number
            }
    
    # 統計相關API
    def get_statistics_overview(self) -> Dict[str, Any]:
        """取得統計概覽"""
        url = f"{self.api_url}/payee-data/statistics/overview"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)
    
    def get_statistics_charts(self, chart_type: str, period: int = 12) -> Dict[str, Any]:
        """取得統計圖表資料"""
        url = f"{self.api_url}/payee-data/statistics/charts"
        params = {
            "chart_type": chart_type,
            "period": period
        }
        response = self.session.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)
    
    # 審計日誌相關API
    def get_audit_logs(self,
                      table_name: Optional[str] = None,
                      action: Optional[str] = None,
                      user_id: Optional[int] = None,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      page: int = 1,
                      size: int = 50) -> Dict[str, Any]:
        """取得審計日誌"""
        url = f"{self.api_url}/audit/logs"
        
        params = {"page": page, "size": size}
        if table_name:
            params["table_name"] = table_name
        if action:
            params["action"] = action
        if user_id:
            params["user_id"] = user_id
        if start_date:
            params["start_date"] = start_date.isoformat()
        if end_date:
            params["end_date"] = end_date.isoformat()
        
        try:
            response = self.session.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"取得審計日誌失敗: {str(e)}")
            raise
    
    # 健康檢查
    def health_check(self) -> Dict[str, Any]:
        """系統健康檢查"""
        url = f"{self.base_url}/health"
        
        try:
            response = self.session.get(url, timeout=5)
            result = response.json()
            
            # 適配後端回傳格式
            if result.get("success") and result.get("data"):
                return {"status": result["data"].get("status", "unknown")}
            else:
                return {"status": result.get("status", "unknown")}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    def get_users(self, page: int = 1, size: int = 20, role: str = None, status: bool = None) -> Dict[str, Any]:
        """取得用戶列表"""
        url = f"{self.api_url}/users/"
        params = {"page": page, "size": size}
        if role:
            params["role"] = role
        if status is not None:
            params["status"] = status
        response = self.session.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)

    def update_user_role(self, user_id: int, role_id: int) -> Dict[str, Any]:
        """更新用戶角色"""
        url = f"{self.api_url}/users/{user_id}/role"
        data = {"role_id": role_id}
        response = self.session.put(url, json=data, headers=self._get_headers())
        return self._handle_response(response)

    def update_user_status(self, user_id: int, is_active: bool) -> Dict[str, Any]:
        """更新用戶狀態"""
        url = f"{self.api_url}/users/{user_id}/status"
        data = {"is_active": is_active}
        
        try:
            response = self.session.put(
                url,
                json=data,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新用戶狀態失敗: {str(e)}")
            raise
    
    def update_user_name(self, user_id: int, name: str) -> Dict[str, Any]:
        """更新用戶名稱"""
        url = f"{self.api_url}/users/{user_id}/name"
        data = {"name": name}
        
        try:
            response = self.session.put(
                url,
                json=data,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            st.error(f"更新用戶名稱失敗: {str(e)}")
            raise
    
    def update_user_department(self, user_id: int, department_id: int) -> Dict[str, Any]:
        """更新用戶部門"""
        url = f"{self.api_url}/users/{user_id}/department"
        data = {"department_id": department_id}
        response = self.session.put(
            url,
            json=data,
            headers=self._get_headers(),
            timeout=self.timeout
        )
        return self._handle_response(response)

    def get_roles(self) -> list:
        url = f"{self.api_url}/users/roles"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)

    def get_permissions(self) -> list:
        url = f"{self.api_url}/users/permissions"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)

    def get_role_permissions(self, role_id: int) -> Dict[str, Any]:
        """取得角色擁有的權限"""
        url = f"{self.api_url}/users/roles/{role_id}/permissions"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)

    def update_role_permissions(self, role_id: int, permission_ids: list) -> Dict[str, Any]:
        """調整角色權限"""
        url = f"{self.api_url}/users/roles/{role_id}/permissions"
        data = {"permission_ids": permission_ids}
        response = self.session.put(url, json=data, headers=self._get_headers())
        return self._handle_response(response)

    def create_role(self, name: str, description: str = None) -> Dict[str, Any]:
        url = f"{self.api_url}/users/roles"
        data = {"name": name, "description": description}
        response = self.session.post(url, json=data, headers=self._get_headers())
        return self._handle_response(response)

    def update_role_name(self, role_id: int, name: str, description: str = None) -> Dict[str, Any]:
        url = f"{self.api_url}/users/roles/{role_id}"
        data = {"name": name, "description": description}
        response = self.session.put(url, json=data, headers=self._get_headers())
        return self._handle_response(response)

    def delete_role(self, role_id: int) -> Dict[str, Any]:
        url = f"{self.api_url}/users/roles/{role_id}"
        response = self.session.delete(url, headers=self._get_headers(), timeout=self.timeout)
        return self._handle_response(response)

    def get_departments(self) -> List[Dict[str, Any]]:
        """取得部門列表"""
        url = f"{self.api_url}/departments/"
        response = self.session.get(
            url,
            headers=self._get_headers(),
            timeout=self.timeout
        )
        return self._handle_response(response)

    def create_department(self, code: str, name: str, description: Optional[str] = None) -> Dict[str, Any]:
        """新增部門"""
        url = f"{self.api_url}/departments/"
        data = {"code": code, "name": name, "description": description}
        response = self.session.post(
            url,
            json=data,
            headers=self._get_headers(),
            timeout=self.timeout
        )
        return self._handle_response(response)

    def update_department(self, department_id: int, code: str, name: str, description: Optional[str], is_active: bool) -> Dict[str, Any]:
        """更新部門"""
        url = f"{self.api_url}/departments/{department_id}"
        data = {"code": code, "name": name, "description": description, "is_active": is_active}
        response = self.session.put(
            url,
            json=data,
            headers=self._get_headers(),
            timeout=self.timeout
        )
        return self._handle_response(response)

    def delete_department(self, department_id: int) -> Dict[str, Any]:
        """刪除部門"""
        url = f"{self.api_url}/departments/{department_id}"
        response = self.session.delete(
            url,
            headers=self._get_headers(),
            timeout=self.timeout
        )
        return self._handle_response(response)

# 建立全域API客戶端實例
api_client = APIClient() 
#!/bin/bash

# CBA系統 - 前後端整合建構和部署腳本
# 統一使用80埠提供服務

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查依賴
check_dependencies() {
    log_info "檢查系統依賴..."
    
    # 檢查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安裝，請先安裝Node.js 18或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本過低，需要18或更高版本"
        exit 1
    fi
    
    # 檢查Python和uv
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安裝"
        exit 1
    fi
    
    if ! command -v uv &> /dev/null; then
        log_warning "uv未安裝，嘗試安裝..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source ~/.bashrc
        
        if ! command -v uv &> /dev/null; then
            log_error "uv安裝失敗，請手動安裝"
            exit 1
        fi
    fi
    
    log_success "依賴檢查通過"
}

# 安裝前端依賴
install_frontend_deps() {
    log_info "安裝前端依賴..."
    
    if [ ! -d "frontend-vue" ]; then
        log_error "frontend-vue目錄不存在"
        exit 1
    fi
    
    cd frontend-vue
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    cd ..
    log_success "前端依賴安裝完成"
}

# 安裝後端依賴
install_backend_deps() {
    log_info "安裝後端依賴..."
    
    if [ ! -d "backend" ]; then
        log_error "backend目錄不存在"
        exit 1
    fi
    
    cd backend
    uv sync
    cd ..
    
    log_success "後端依賴安裝完成"
}

# 建構前端
build_frontend() {
    log_info "建構Vue.js前端..."
    
    cd frontend-vue
    
    # 設定生產環境變數
    export NODE_ENV=production
    export VITE_API_BASE_URL=/api
    
    # 執行建構
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "前端建構失敗，dist目錄不存在"
        exit 1
    fi
    
    # 檢查建構檔案
    if [ ! -f "dist/index.html" ]; then
        log_error "前端建構失敗，index.html不存在"
        exit 1
    fi
    
    cd ..
    log_success "前端建構完成"
}

# 準備整合環境
prepare_integration() {
    log_info "準備整合環境..."
    
    # 創建環境配置檔案
    cat > .env.integrated << EOF
# 整合環境配置
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=80

# 資料庫設定
DATABASE_URL=sqlite:///./cba_system.db

# JWT設定
JWT_SECRET_KEY=your-super-secure-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480

# 加密設定
ENCRYPTION_KEY=your-super-secure-encryption-key-32-chars

# 前端設定
FRONTEND_URL=http://localhost

# 審計設定
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365

# API限制
API_RATE_LIMIT=100
MAX_QUERY_RESULTS=1000
EOF
    
    log_success "整合環境配置完成"
}

# 啟動整合服務
start_integrated_service() {
    log_info "啟動整合服務..."
    
    # 檢查80埠是否被佔用
    if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "80埠已被佔用，嘗試停止佔用進程..."
        
        # 詢問用戶是否要停止佔用進程
        read -p "是否要停止佔用80埠的進程？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo lsof -ti:80 | xargs sudo kill -9 2>/dev/null || true
            sleep 2
        else
            log_error "80埠被佔用，無法啟動服務"
            exit 1
        fi
    fi
    
    # 設定環境變數
    export $(cat .env.integrated | xargs)
    
    cd backend
    
    # 檢查是否需要sudo權限（80埠通常需要）
    if [ "$PORT" -eq 80 ] && [ "$EUID" -ne 0 ]; then
        log_info "80埠需要管理員權限，使用sudo啟動..."
        sudo -E uv run uvicorn main:app --host $HOST --port $PORT --workers 1
    else
        uv run uvicorn main:app --host $HOST --port $PORT --workers 1
    fi
}

# 啟動開發模式
start_dev_mode() {
    log_info "啟動開發模式..."
    
    # 設定開發環境變數
    export ENVIRONMENT=development
    export DEBUG=true
    export HOST=0.0.0.0
    export PORT=80
    
    cd backend
    
    if [ "$PORT" -eq 80 ] && [ "$EUID" -ne 0 ]; then
        log_info "開發模式使用80埠，需要管理員權限..."
        sudo -E uv run uvicorn main:app --host $HOST --port $PORT --reload
    else
        uv run uvicorn main:app --host $HOST --port $PORT --reload
    fi
}

# 停止服務
stop_service() {
    log_info "停止整合服務..."
    
    # 停止佔用80埠的進程
    sudo lsof -ti:80 | xargs sudo kill -9 2>/dev/null || true
    
    log_success "服務已停止"
}

# 檢查服務狀態
check_status() {
    log_info "檢查服務狀態..."
    
    # 檢查80埠
    if curl -f -s http://localhost/health > /dev/null; then
        log_success "整合服務正常運行在 http://localhost"
    else
        log_error "整合服務無法存取"
        return 1
    fi
    
    # 檢查API
    if curl -f -s http://localhost/api/v1/auth/health > /dev/null 2>&1; then
        log_success "API服務正常"
    else
        log_warning "API服務可能有問題"
    fi
    
    # 檢查前端
    if curl -f -s http://localhost/ | grep -q "CBA" 2>/dev/null; then
        log_success "前端服務正常"
    else
        log_warning "前端服務可能有問題"
    fi
}

# 顯示幫助資訊
show_help() {
    echo "CBA系統 - 前後端整合部署腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  build     建構前端並準備整合環境"
    echo "  start     啟動整合服務 (生產模式)"
    echo "  dev       啟動開發模式"
    echo "  stop      停止服務"
    echo "  status    檢查服務狀態"
    echo "  full      完整部署 (build + start)"
    echo "  help      顯示此幫助資訊"
    echo ""
    echo "範例:"
    echo "  $0 full       # 完整部署"
    echo "  $0 build      # 僅建構"
    echo "  $0 start      # 僅啟動"
    echo "  $0 dev        # 開發模式"
}

# 主函數
main() {
    case "${1:-full}" in
        "build")
            check_dependencies
            install_frontend_deps
            install_backend_deps
            build_frontend
            prepare_integration
            ;;
        "start")
            start_integrated_service
            ;;
        "dev")
            check_dependencies
            install_backend_deps
            start_dev_mode
            ;;
        "stop")
            stop_service
            ;;
        "status")
            check_status
            ;;
        "full")
            echo "🚀 CBA系統 - 前後端整合部署"
            echo "=============================="
            check_dependencies
            install_frontend_deps
            install_backend_deps
            build_frontend
            prepare_integration
            echo ""
            log_success "建構完成，準備啟動服務..."
            echo ""
            start_integrated_service
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"

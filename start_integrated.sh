#!/bin/bash

# CBA系統 - 快速啟動腳本 (80埠整合版)

set -e

# 顏色定義
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 CBA受款人資料搜集系統 - 整合啟動${NC}"
echo "========================================"

# 檢查是否為root用戶
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️ 檢測到root用戶，將直接啟動服務${NC}"
    SUDO_CMD=""
else
    echo -e "${YELLOW}⚠️ 80埠需要管理員權限，將使用sudo啟動${NC}"
    SUDO_CMD="sudo -E"
fi

# 檢查靜態檔案目錄
if [ ! -d "backend/app/frontend/static" ]; then
    echo -e "${YELLOW}📦 創建靜態檔案目錄...${NC}"
    mkdir -p backend/app/frontend/static/css
    mkdir -p backend/app/frontend/static/js
    echo -e "${GREEN}✅ 靜態檔案目錄已創建${NC}"
fi

# 檢查後端依賴
if [ ! -d "backend/.venv" ] && [ ! -f "backend/uv.lock" ]; then
    echo -e "${YELLOW}📦 安裝後端依賴...${NC}"
    cd backend
    uv sync
    cd ..
    echo -e "${GREEN}✅ 後端依賴安裝完成${NC}"
fi

# 檢查80埠是否被佔用
if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}❌ 80埠已被佔用${NC}"
    echo "佔用進程："
    lsof -Pi :80 -sTCP:LISTEN
    echo ""
    read -p "是否要停止佔用進程並繼續？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "停止佔用進程..."
        sudo lsof -ti:80 | xargs sudo kill -9 2>/dev/null || true
        sleep 2
    else
        echo "取消啟動"
        exit 1
    fi
fi

# 設定環境變數
export ENVIRONMENT=production
export DEBUG=false
export HOST=0.0.0.0
export PORT=80
export DATABASE_URL=sqlite:///./cba_system.db

echo -e "${BLUE}🔧 啟動整合服務...${NC}"
echo "服務地址: http://localhost"
echo "API文檔: http://localhost/docs"
echo "健康檢查: http://localhost/health"
echo ""
echo -e "${YELLOW}按 Ctrl+C 停止服務${NC}"
echo ""

# 啟動服務
cd backend
$SUDO_CMD uv run uvicorn main:app --host $HOST --port $PORT

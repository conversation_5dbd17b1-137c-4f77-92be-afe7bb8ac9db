# CBA受款人資料搜集系統 - 整合部署指南

## 📋 概述

本指南說明如何將CBA受款人資料搜集系統的前端、後端和SSO服務整合到80埠運行。

## 🏗️ 系統架構

整合後的系統架構：

```
用戶 (瀏覽器)
    ↓ http://localhost:80
nginx反向代理 (80埠)
    ├── / → Streamlit前端 (8501埠)
    ├── /api/ → FastAPI後端 (8080埠)
    ├── /sso/ → SSO服務 (8000埠)
    └── /health → 健康檢查
```

## 🔧 服務配置

### 服務埠分配
- **SSO服務**: 8000埠 - 政府單一簽入認證
- **後端API**: 8080埠 - FastAPI業務邏輯
- **前端UI**: 8501埠 - Streamlit用戶介面
- **整合入口**: 80埠 - nginx反向代理

### 路由規劃
- `/` - 前端Streamlit應用
- `/api/v1/*` - 後端API端點
- `/sso/*` - SSO認證服務
- `/health` - 系統健康檢查

## 🚀 快速啟動

### 方法一：使用整合啟動腳本（推薦）

#### Windows:
```cmd
start_system.bat
```

#### Linux/macOS:
```bash
./start_system.sh
```

### 方法二：手動啟動各服務

1. **啟動SSO服務**:
```cmd
cd odc_sso
uv run uvicorn sso_service:app --host 0.0.0.0 --port 8000
```

2. **啟動後端API**:
```cmd
cd backend
uv run uvicorn main:app --host 0.0.0.0 --port 8080
```

3. **啟動前端UI**:
```cmd
cd frontend
streamlit run main.py --server.port 8501 --server.address 0.0.0.0 --server.headless true
```

## 🌐 nginx配置（80埠整合）

### 安裝nginx

#### Windows:
1. 下載nginx: https://nginx.org/en/download.html
2. 解壓到 `C:\nginx`
3. 複製 `nginx-integrated.conf` 到 `C:\nginx\conf\`

#### Linux/Ubuntu:
```bash
sudo apt update
sudo apt install nginx
```

#### macOS:
```bash
brew install nginx
```

### 配置nginx

1. **複製配置檔案**:
```bash
# Linux/macOS
sudo cp nginx-integrated.conf /etc/nginx/sites-available/cba-system
sudo ln -s /etc/nginx/sites-available/cba-system /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Windows
copy nginx-integrated.conf C:\nginx\conf\nginx.conf
```

2. **測試配置**:
```bash
# Linux/macOS
sudo nginx -t

# Windows
C:\nginx\nginx.exe -t
```

3. **啟動nginx**:
```bash
# Linux/macOS
sudo systemctl start nginx
sudo systemctl enable nginx

# Windows
C:\nginx\nginx.exe
```

## 📊 服務監控

### 健康檢查端點

- **整合服務**: http://localhost:80/health
- **後端API**: http://localhost:8080/health
- **前端UI**: http://localhost:8501
- **SSO服務**: http://localhost:8000

### 日誌位置

- **nginx日誌**: `/var/log/nginx/cba-system.*.log`
- **應用日誌**: 各服務控制台輸出

## 🔒 安全性配置

### 防火牆設定

只開放80埠對外，內部服務埠僅限本機存取：

```bash
# Linux (ufw)
sudo ufw allow 80
sudo ufw deny 8000
sudo ufw deny 8080
sudo ufw deny 8501
```

### 環境變數

建議設定以下環境變數：

```bash
# API基礎URL
API_BASE_URL=http://localhost:8080

# 前端URL
FRONTEND_URL=http://localhost:8501

# 生產環境設定
ENVIRONMENT=production
DEBUG=false
```

## 🛠️ 故障排除

### 常見問題

1. **埠被佔用**:
```bash
# 檢查埠佔用
netstat -ano | findstr :80
netstat -ano | findstr :8000
netstat -ano | findstr :8080
netstat -ano | findstr :8501
```

2. **服務無法啟動**:
- 檢查Python和uv是否正確安裝
- 確認工作目錄存在
- 檢查依賴套件是否安裝

3. **nginx代理失敗**:
- 檢查nginx配置語法
- 確認上游服務正在運行
- 檢查防火牆設定

### 日誌檢查

```bash
# nginx錯誤日誌
tail -f /var/log/nginx/cba-system.error.log

# 服務狀態
curl http://localhost:80/health
curl http://localhost:8080/health
curl http://localhost:8501
curl http://localhost:8000
```

## 📈 效能優化

### nginx優化

在 `nginx-integrated.conf` 中已包含：
- 靜態檔案快取
- 連接保持
- 壓縮設定
- 安全標頭

### 應用優化

- 使用生產環境配置
- 啟用日誌輪替
- 設定適當的工作進程數

## 🔄 更新部署

1. **停止服務**:
```bash
# 使用整合腳本時按 Ctrl+C
# 或手動停止各服務
```

2. **更新程式碼**:
```bash
git pull origin main
```

3. **重新啟動**:
```bash
./start_system.sh
```

## 📞 支援

如遇問題，請檢查：
1. 系統需求是否滿足
2. 依賴套件是否正確安裝
3. 配置檔案是否正確
4. 防火牆和網路設定

---

**注意**: 本指南適用於開發和測試環境。生產環境部署請參考 `backend/PRODUCTION_DEPLOYMENT.md`。

/**
 * 受款人狀態管理
 */
import { defineStore } from 'pinia'
import { payeeApi } from '@/api/payee'
import type { 
  Payee, 
  PayeeCreateRequest, 
  PayeeUpdateRequest, 
  PayeeQueryParams,
  PayeeStatistics 
} from '@/types/payee'
import type { LoadingState } from '@/types/common'
import { ElMessage } from 'element-plus'

interface PayeeState {
  payees: Payee[]
  currentPayee: Payee | null
  statistics: PayeeStatistics | null
  loading: LoadingState
  pagination: {
    page: number
    size: number
    total: number
    pages: number
  }
  queryParams: PayeeQueryParams
}

export const usePayeeStore = defineStore('payee', {
  state: (): PayeeState => ({
    payees: [],
    currentPayee: null,
    statistics: null,
    loading: 'idle',
    pagination: {
      page: 1,
      size: 20,
      total: 0,
      pages: 0
    },
    queryParams: {
      page: 1,
      size: 20
    }
  }),
  
  getters: {
    // 活躍的受款人
    activePayees: (state) => state.payees.filter(p => p.is_active),
    
    // 非活躍的受款人
    inactivePayees: (state) => state.payees.filter(p => !p.is_active),
    
    // 是否正在載入
    isLoading: (state) => state.loading === 'loading',
    
    // 是否有資料
    hasData: (state) => state.payees.length > 0,
    
    // 總頁數
    totalPages: (state) => state.pagination.pages
  },
  
  actions: {
    /**
     * 獲取受款人列表
     */
    async fetchPayees(params?: PayeeQueryParams) {
      try {
        this.loading = 'loading'
        
        // 合併查詢參數
        const queryParams = { ...this.queryParams, ...params }
        this.queryParams = queryParams
        
        const response = await payeeApi.getPayees(queryParams)
        
        if (response.success) {
          this.payees = response.data.data
          this.pagination = {
            page: response.data.page,
            size: response.data.size,
            total: response.data.total,
            pages: response.data.pages
          }
          this.loading = 'success'
        }
      } catch (error) {
        console.error('獲取受款人列表失敗:', error)
        this.loading = 'error'
        throw error
      }
    },
    
    /**
     * 獲取受款人詳情
     */
    async fetchPayee(id: string) {
      try {
        this.loading = 'loading'
        const response = await payeeApi.getPayee(id)
        
        if (response.success) {
          this.currentPayee = response.data
          this.loading = 'success'
          return response.data
        }
      } catch (error) {
        console.error('獲取受款人詳情失敗:', error)
        this.loading = 'error'
        throw error
      }
    },
    
    /**
     * 創建受款人
     */
    async createPayee(data: PayeeCreateRequest) {
      try {
        const response = await payeeApi.createPayee(data)
        
        if (response.success) {
          // 重新載入列表
          await this.fetchPayees()
          ElMessage.success('受款人創建成功')
          return response.data
        }
      } catch (error: any) {
        console.error('創建受款人失敗:', error)
        ElMessage.error(error.message || '創建受款人失敗')
        throw error
      }
    },
    
    /**
     * 更新受款人
     */
    async updatePayee(id: string, data: PayeeUpdateRequest) {
      try {
        const response = await payeeApi.updatePayee(id, data)
        
        if (response.success) {
          // 更新本地資料
          const index = this.payees.findIndex(p => p.id === id)
          if (index !== -1) {
            this.payees[index] = response.data
          }
          
          // 更新當前受款人
          if (this.currentPayee?.id === id) {
            this.currentPayee = response.data
          }
          
          ElMessage.success('受款人更新成功')
          return response.data
        }
      } catch (error: any) {
        console.error('更新受款人失敗:', error)
        ElMessage.error(error.message || '更新受款人失敗')
        throw error
      }
    },
    
    /**
     * 刪除受款人
     */
    async deletePayee(id: string) {
      try {
        const response = await payeeApi.deletePayee(id)
        
        if (response.success) {
          // 從本地列表中移除
          this.payees = this.payees.filter(p => p.id !== id)
          
          // 清除當前受款人（如果是被刪除的）
          if (this.currentPayee?.id === id) {
            this.currentPayee = null
          }
          
          ElMessage.success('受款人刪除成功')
        }
      } catch (error: any) {
        console.error('刪除受款人失敗:', error)
        ElMessage.error(error.message || '刪除受款人失敗')
        throw error
      }
    },
    
    /**
     * 批量刪除受款人
     */
    async batchDeletePayees(ids: string[]) {
      try {
        const response = await payeeApi.batchDeletePayees(ids)
        
        if (response.success) {
          // 從本地列表中移除
          this.payees = this.payees.filter(p => !ids.includes(p.id))
          
          ElMessage.success(`成功刪除 ${ids.length} 個受款人`)
        }
      } catch (error: any) {
        console.error('批量刪除受款人失敗:', error)
        ElMessage.error(error.message || '批量刪除失敗')
        throw error
      }
    },
    
    /**
     * 切換受款人狀態
     */
    async togglePayeeStatus(id: string, isActive: boolean) {
      try {
        const response = await payeeApi.togglePayeeStatus(id, isActive)
        
        if (response.success) {
          // 更新本地資料
          const index = this.payees.findIndex(p => p.id === id)
          if (index !== -1) {
            this.payees[index] = response.data
          }
          
          // 更新當前受款人
          if (this.currentPayee?.id === id) {
            this.currentPayee = response.data
          }
          
          ElMessage.success(`受款人已${isActive ? '啟用' : '停用'}`)
        }
      } catch (error: any) {
        console.error('切換受款人狀態失敗:', error)
        ElMessage.error(error.message || '狀態切換失敗')
        throw error
      }
    },
    
    /**
     * 搜尋受款人
     */
    async searchPayees(query: string, params?: Partial<PayeeQueryParams>) {
      try {
        this.loading = 'loading'
        const response = await payeeApi.searchPayees(query, params)
        
        if (response.success) {
          this.payees = response.data.data
          this.pagination = {
            page: response.data.page,
            size: response.data.size,
            total: response.data.total,
            pages: response.data.pages
          }
          this.loading = 'success'
        }
      } catch (error) {
        console.error('搜尋受款人失敗:', error)
        this.loading = 'error'
        throw error
      }
    },
    
    /**
     * 獲取統計資料
     */
    async fetchStatistics() {
      try {
        const response = await payeeApi.getPayeeStatistics()
        
        if (response.success) {
          this.statistics = response.data
        }
      } catch (error) {
        console.error('獲取統計資料失敗:', error)
        throw error
      }
    },
    
    /**
     * 重設狀態
     */
    resetState() {
      this.payees = []
      this.currentPayee = null
      this.statistics = null
      this.loading = 'idle'
      this.pagination = {
        page: 1,
        size: 20,
        total: 0,
        pages: 0
      }
      this.queryParams = {
        page: 1,
        size: 20
      }
    },
    
    /**
     * 設定查詢參數
     */
    setQueryParams(params: Partial<PayeeQueryParams>) {
      this.queryParams = { ...this.queryParams, ...params }
    },
    
    /**
     * 重設查詢參數
     */
    resetQueryParams() {
      this.queryParams = {
        page: 1,
        size: 20
      }
    }
  }
})

"""
受款人資料管理服務層
"""
from typing import List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.exc import IntegrityError

from app.models.payee_data import PayeeData
from app.models.user import User, Department
from app.schemas.payee_data import (
    PayeeDataCreate, PayeeDataUpdate, PayeeDataQuery,
    PayeeDataResponse, PayeeDataListResponse
)
from app.utils.encryption import encrypt_data, decrypt_data
from app.utils.audit import log_operation
from app.core.exceptions import (
    PayeeDataNotFoundError, DuplicateIdNumberError,
    PermissionDeniedError, ValidationError
)


class PayeeDataService:
    """受款人資料管理服務"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_payee_data(
        self, 
        data: PayeeDataCreate, 
        current_user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> PayeeDataResponse:
        """建立受款人資料"""
        
        # 檢查權限
        if not current_user.has_permission("CREATE_PAYEE_DATA"):
            raise PermissionDeniedError("您沒有權限建立受款人資料")
        
        # 檢查身分證字號是否已存在
        if self.check_id_number_exists(data.id_number):
            raise DuplicateIdNumberError("身分證字號已存在")
        
        # 加密身分證字號
        encrypted_id_number = encrypt_data(data.id_number)
        
        # 建立資料
        db_payee_data = PayeeData(
            name=data.name,
            encrypted_id_number=encrypted_id_number,
            address=data.address,
            notes=data.notes,
            created_by_user_id=current_user.id,
            created_by_department=data.created_by_department or current_user.department_id,
            created_at=datetime.utcnow(),
            is_active=True
        )
        
        try:
            self.db.add(db_payee_data)
            self.db.commit()
            self.db.refresh(db_payee_data)
            
            # 記錄審計日誌
            log_operation(
                db=self.db,
                user_id=current_user.id,
                action="CREATE",
                table_name="payee_data",
                record_id=db_payee_data.id,
                new_values={
                    "name": data.name,
                    "id_number": "[ENCRYPTED]",
                    "address": data.address,
                    "notes": data.notes,
                    "created_by_department": db_payee_data.created_by_department
                },
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return self._to_response_model(db_payee_data)
            
        except IntegrityError:
            self.db.rollback()
            raise DuplicateIdNumberError("身分證字號已存在")
    
    def get_payee_data(
        self, 
        data_id: int, 
        current_user: User
    ) -> PayeeDataResponse:
        """取得受款人資料"""
        
        # 檢查基本權限
        if not (current_user.has_permission("READ_PAYEE_DATA") or current_user.has_permission("READ_ALL_DATA")):
            raise PermissionDeniedError("您沒有權限查詢受款人資料")
        
        db_payee_data = self.db.query(PayeeData).options(
            joinedload(PayeeData.created_by_dept),
            joinedload(PayeeData.created_by_user)
        ).filter(
            PayeeData.id == data_id,
            PayeeData.is_active == True
        ).first()
        
        if not db_payee_data:
            raise PayeeDataNotFoundError("找不到指定的受款人資料")
        
        # 資料層級權限檢查
        if not self._check_read_permission(db_payee_data, current_user):
            raise PermissionDeniedError("沒有權限存取此資料")
        
        return self._to_response_model(db_payee_data)
    
    def get_payee_data_list(
        self, 
        query: PayeeDataQuery, 
        current_user: User
    ) -> PayeeDataListResponse:
        """取得受款人資料列表"""
        
        # 檢查權限
        if not (current_user.has_permission("READ_PAYEE_DATA") or current_user.has_permission("READ_ALL_DATA")):
            raise PermissionDeniedError("您沒有權限查詢受款人資料")
        
        # 建立基本查詢
        base_query = self.db.query(PayeeData).filter(PayeeData.is_active == True)
        
        # 權限過濾
        if not self._has_global_permission(current_user):
            # 一般用戶只能看到自己建立的資料
            base_query = base_query.filter(
                PayeeData.created_by_user_id == current_user.id
            )
        
        # 搜尋過濾
        if query.search:
            search_term = f"%{query.search}%"
            base_query = base_query.filter(
                or_(
                    PayeeData.name.ilike(search_term),
                    PayeeData.address.ilike(search_term),
                    PayeeData.notes.ilike(search_term)
                )
            )
        
        if query.name:
            base_query = base_query.filter(PayeeData.name.ilike(f"%{query.name}%"))
        
        if query.address:
            base_query = base_query.filter(PayeeData.address.ilike(f"%{query.address}%"))
        
        if query.department:
            base_query = base_query.join(Department, PayeeData.created_by_department == Department.id).filter(
                Department.name.ilike(f"%{query.department}%")
            )
        
        # 計算總數
        total = base_query.count()
        
        # 分頁和排序
        offset = (query.page - 1) * query.size
        items = base_query.options(
            joinedload(PayeeData.created_by_dept),
            joinedload(PayeeData.created_by_user)
        ).order_by(PayeeData.id.asc()).offset(offset).limit(query.size).all()
        
        # 轉換為回應模型
        response_items = [self._to_response_model(item) for item in items]
        
        # 計算頁數
        pages = (total + query.size - 1) // query.size
        
        return PayeeDataListResponse(
            items=response_items,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )
    
    def update_payee_data(
        self,
        data_id: int,
        data: PayeeDataUpdate,
        current_user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> PayeeDataResponse:
        """更新受款人資料"""
        
        # 檢查基本權限 - 所有用戶都可以修改受款人資料
        if not current_user.has_permission("UPDATE_PAYEE_DATA"):
            raise PermissionDeniedError("您沒有權限修改受款人資料")
        
        db_payee_data = self.db.query(PayeeData).options(
            joinedload(PayeeData.created_by_dept),
            joinedload(PayeeData.created_by_user)
        ).filter(
            PayeeData.id == data_id,
            PayeeData.is_active == True
        ).first()
        
        if not db_payee_data:
            raise PayeeDataNotFoundError("找不到指定的受款人資料")
        
        # 新的權限控制：所有用戶都可以修改受款人資料（不限於建立者）
        # 只需要有基本的UPDATE_PAYEE_DATA權限即可
        
        # 記錄舊值
        old_values = {
            "name": db_payee_data.name,
            "address": db_payee_data.address,
            "notes": db_payee_data.notes
        }
        
        # 更新資料
        update_data = data.model_dump(exclude_unset=True)
        new_values = {}
        
        for field, value in update_data.items():
            if hasattr(db_payee_data, field):
                setattr(db_payee_data, field, value)
                new_values[field] = value
        
        db_payee_data.updated_at = datetime.utcnow()
        
        try:
            self.db.commit()
            self.db.refresh(db_payee_data)
            
            # 記錄審計日誌
            log_operation(
                db=self.db,
                user_id=current_user.id,
                action="UPDATE",
                table_name="payee_data",
                record_id=db_payee_data.id,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return self._to_response_model(db_payee_data)
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def delete_payee_data(
        self,
        data_id: int,
        current_user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """刪除受款人資料（軟刪除）"""
        
        # 檢查基本權限 - 所有用戶都可以刪除受款人資料
        if not current_user.has_permission("DELETE_PAYEE_DATA"):
            raise PermissionDeniedError("您沒有權限刪除受款人資料")
        
        db_payee_data = self.db.query(PayeeData).options(
            joinedload(PayeeData.created_by_dept),
            joinedload(PayeeData.created_by_user)
        ).filter(
            PayeeData.id == data_id,
            PayeeData.is_active == True
        ).first()
        
        if not db_payee_data:
            raise PayeeDataNotFoundError("找不到指定的受款人資料")
        
        # 新的權限控制：所有用戶都可以刪除受款人資料（不限於建立者）
        # 只需要有基本的DELETE_PAYEE_DATA權限即可
        
        # 軟刪除
        old_values = {
            "name": db_payee_data.name,
            "address": db_payee_data.address,
            "notes": db_payee_data.notes,
            "is_active": True
        }
        
        db_payee_data.is_active = False
        db_payee_data.updated_at = datetime.utcnow()
        
        try:
            self.db.commit()
            
            # 記錄審計日誌
            log_operation(
                db=self.db,
                user_id=current_user.id,
                action="DELETE",
                table_name="payee_data",
                record_id=db_payee_data.id,
                old_values=old_values,
                new_values={"is_active": False},
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def check_id_number_exists(self, id_number: str, exclude_id: Optional[int] = None) -> bool:
        """檢查身分證字號是否已存在"""
        query = self.db.query(PayeeData).filter(PayeeData.is_active == True)
        
        if exclude_id:
            query = query.filter(PayeeData.id != exclude_id)
        
        # 取得所有活躍記錄，逐一解密比較
        existing_records = query.all()
        for record in existing_records:
            try:
                decrypted_id = decrypt_data(record.encrypted_id_number)
                if decrypted_id == id_number:
                    return True
            except:
                # 如果解密失敗，跳過該記錄
                continue
        
        return False
    
    def _to_response_model(self, db_payee_data: PayeeData) -> PayeeDataResponse:
        """轉換為回應模型"""
        decrypted_id_number = decrypt_data(db_payee_data.encrypted_id_number)
        
        # 建立遮蔽後的身分證字號（前4位+****+後2位）
        masked_id = f"{decrypted_id_number[:4]}****{decrypted_id_number[-2:]}" if len(decrypted_id_number) >= 6 else decrypted_id_number
        
        # 取得部門名稱
        department_name = None
        if db_payee_data.created_by_dept:
            department_name = db_payee_data.created_by_dept.name
        
        # 取得建立者姓名
        created_by_name = None
        if db_payee_data.created_by_user:
            created_by_name = db_payee_data.created_by_user.full_name or db_payee_data.created_by_user.username
        
        return PayeeDataResponse(
            id=db_payee_data.id,
            name=db_payee_data.name,
            id_number=decrypted_id_number,
            id_number_masked=masked_id,
            address=db_payee_data.address,
            notes=db_payee_data.notes,
            created_by=created_by_name,
            created_by_department=department_name,
            created_at=db_payee_data.created_at,
            updated_at=db_payee_data.updated_at
        )
    
    def _check_read_permission(self, payee_data: PayeeData, user: User) -> bool:
        """檢查讀取權限"""
        # 管理者和全域用戶可以讀取所有資料
        if self._has_global_permission(user):
            return True
        
        # 一般用戶只能讀取自己建立的資料
        return payee_data.created_by_user_id == user.id
    
    def _check_write_permission(self, payee_data: PayeeData, user: User) -> bool:
        """檢查寫入權限 - 已修改為允許所有用戶修改"""
        # 新邏輯：所有用戶都可以修改受款人資料
        # 只需要有基本權限即可，不限於建立者
        return True
    
    def _has_global_permission(self, user: User) -> bool:
        """檢查是否有全域權限"""
        return user.has_permission("READ_ALL_DATA")
    
    def _has_admin_permission(self, user: User) -> bool:
        """檢查是否有管理者權限"""
        return user.has_permission("MANAGE_USERS") or user.has_permission("MANAGE_ADMIN") 
#!/usr/bin/env python3
"""
CBA受款人資料搜集系統 - 整合測試腳本
驗證所有服務是否正常運行和通訊
"""

import requests
import time
import sys
import json
from typing import Dict, Any

class IntegrationTester:
    """整合測試器"""
    
    def __init__(self):
        self.base_url = "http://localhost"
        self.services = {
            "integrated": {"port": 80, "name": "整合服務"},
            "backend": {"port": 8080, "name": "後端API"},
            "frontend": {"port": 8501, "name": "前端UI"},
            "sso": {"port": 8000, "name": "SSO服務"}
        }
        self.test_results = {}
    
    def test_service_health(self, service_name: str, port: int) -> Dict[str, Any]:
        """測試服務健康狀態"""
        url = f"{self.base_url}:{port}"
        
        # 嘗試不同的健康檢查端點
        health_endpoints = ["/health", "/", ""]
        
        for endpoint in health_endpoints:
            test_url = f"{url}{endpoint}"
            try:
                response = requests.get(test_url, timeout=10)
                if response.status_code == 200:
                    return {
                        "status": "healthy",
                        "url": test_url,
                        "response_time": response.elapsed.total_seconds(),
                        "status_code": response.status_code
                    }
            except requests.exceptions.RequestException as e:
                continue
        
        return {
            "status": "unhealthy",
            "url": url,
            "error": "無法連接到服務"
        }
    
    def test_api_endpoints(self) -> Dict[str, Any]:
        """測試API端點"""
        results = {}
        
        # 測試後端API健康檢查
        try:
            response = requests.get(f"{self.base_url}:8080/health", timeout=10)
            results["backend_health"] = {
                "status": "success" if response.status_code == 200 else "failed",
                "status_code": response.status_code,
                "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text[:200]
            }
        except Exception as e:
            results["backend_health"] = {"status": "failed", "error": str(e)}
        
        # 測試整合服務API代理
        try:
            response = requests.get(f"{self.base_url}:80/api/v1/health", timeout=10)
            results["integrated_api_proxy"] = {
                "status": "success" if response.status_code == 200 else "failed",
                "status_code": response.status_code,
                "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text[:200]
            }
        except Exception as e:
            results["integrated_api_proxy"] = {"status": "failed", "error": str(e)}
        
        return results
    
    def test_frontend_access(self) -> Dict[str, Any]:
        """測試前端存取"""
        results = {}
        
        # 測試直接存取前端
        try:
            response = requests.get(f"{self.base_url}:8501", timeout=10)
            results["frontend_direct"] = {
                "status": "success" if response.status_code == 200 else "failed",
                "status_code": response.status_code,
                "content_type": response.headers.get('content-type', ''),
                "content_length": len(response.content)
            }
        except Exception as e:
            results["frontend_direct"] = {"status": "failed", "error": str(e)}
        
        # 測試整合服務前端代理
        try:
            response = requests.get(f"{self.base_url}:80/", timeout=10)
            results["integrated_frontend_proxy"] = {
                "status": "success" if response.status_code == 200 else "failed",
                "status_code": response.status_code,
                "content_type": response.headers.get('content-type', ''),
                "content_length": len(response.content)
            }
        except Exception as e:
            results["integrated_frontend_proxy"] = {"status": "failed", "error": str(e)}
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """執行所有測試"""
        print("🧪 開始整合測試...")
        print("=" * 50)
        
        all_results = {
            "timestamp": time.time(),
            "service_health": {},
            "api_tests": {},
            "frontend_tests": {},
            "summary": {}
        }
        
        # 測試各服務健康狀態
        print("\n📊 測試服務健康狀態:")
        for service_name, config in self.services.items():
            print(f"  測試 {config['name']} (埠 {config['port']})...", end=" ")
            result = self.test_service_health(service_name, config['port'])
            all_results["service_health"][service_name] = result
            
            if result["status"] == "healthy":
                print(f"✅ 正常 ({result.get('response_time', 0):.2f}s)")
            else:
                print(f"❌ 異常 - {result.get('error', '未知錯誤')}")
        
        # 測試API端點
        print("\n🔌 測試API端點:")
        api_results = self.test_api_endpoints()
        all_results["api_tests"] = api_results
        
        for test_name, result in api_results.items():
            print(f"  {test_name}: ", end="")
            if result["status"] == "success":
                print(f"✅ 成功 (HTTP {result.get('status_code', 'N/A')})")
            else:
                print(f"❌ 失敗 - {result.get('error', '未知錯誤')}")
        
        # 測試前端存取
        print("\n🖥️ 測試前端存取:")
        frontend_results = self.test_frontend_access()
        all_results["frontend_tests"] = frontend_results
        
        for test_name, result in frontend_results.items():
            print(f"  {test_name}: ", end="")
            if result["status"] == "success":
                print(f"✅ 成功 (HTTP {result.get('status_code', 'N/A')}, {result.get('content_length', 0)} bytes)")
            else:
                print(f"❌ 失敗 - {result.get('error', '未知錯誤')}")
        
        # 生成摘要
        total_tests = 0
        passed_tests = 0
        
        for category in ["service_health", "api_tests", "frontend_tests"]:
            for test_name, result in all_results[category].items():
                total_tests += 1
                if result.get("status") in ["healthy", "success"]:
                    passed_tests += 1
        
        all_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
        }
        
        # 顯示摘要
        print("\n📋 測試摘要:")
        print(f"  總測試數: {total_tests}")
        print(f"  通過測試: {passed_tests}")
        print(f"  失敗測試: {total_tests - passed_tests}")
        print(f"  成功率: {all_results['summary']['success_rate']:.1f}%")
        
        if all_results['summary']['success_rate'] >= 80:
            print("\n🎉 整合測試大致成功！")
        else:
            print("\n⚠️ 整合測試發現問題，請檢查失敗的項目")
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], filename: str = "integration_test_results.json"):
        """儲存測試結果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 測試結果已儲存到 {filename}")
        except Exception as e:
            print(f"\n❌ 儲存測試結果失敗: {e}")

def main():
    """主函數"""
    print("🏢 CBA受款人資料搜集系統 - 整合測試")
    print("=" * 50)
    print("請確保所有服務已啟動後再執行此測試")
    print()
    
    # 等待用戶確認
    input("按 Enter 鍵開始測試...")
    
    tester = IntegrationTester()
    results = tester.run_all_tests()
    tester.save_results(results)
    
    # 根據測試結果設定退出碼
    if results['summary']['success_rate'] >= 80:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()

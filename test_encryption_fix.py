#!/usr/bin/env python3
"""
加密功能修正驗證測試
"""

import os
import sys
from dotenv import load_dotenv

def test_encryption():
    """測試加密功能"""
    print("🔍 測試加密功能修正")
    print("=" * 40)
    
    # 載入環境變數
    load_dotenv('./backend/.env')
    
    print("1️⃣ 檢查環境變數...")
    encryption_key = os.getenv('ENCRYPTION_KEY')
    if not encryption_key:
        print("❌ ENCRYPTION_KEY 環境變數未設定")
        return False
    
    print(f"✅ ENCRYPTION_KEY 已設定 (長度: {len(encryption_key)})")
    print(f"📋 金鑰前10字元: {encryption_key[:10]}...")
    
    print("\n2️⃣ 測試加密模組...")
    try:
        # 添加backend路徑到sys.path
        sys.path.insert(0, './backend')
        
        from app.utils.encryption import encrypt_id_number, decrypt_id_number
        
        # 測試身分證字號
        test_id = "A123456789"
        print(f"📝 測試身分證字號: {test_id}")
        
        # 加密
        encrypted = encrypt_id_number(test_id)
        print(f"🔐 加密結果 (前20字元): {encrypted[:20]}...")
        
        # 解密
        decrypted = decrypt_id_number(encrypted)
        print(f"🔓 解密結果: {decrypted}")
        
        # 驗證
        if decrypted == test_id:
            print("✅ 加密解密測試通過！")
            return True
        else:
            print("❌ 加密解密結果不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 加密測試失敗: {e}")
        return False

def main():
    """主函數"""
    success = test_encryption()
    
    if success:
        print("\n🎉 加密功能修正成功！")
        print("📋 下一步：重新啟動後端服務並測試個人資料建立")
    else:
        print("\n❌ 加密功能仍有問題，請檢查配置")
    
    return success

if __name__ == "__main__":
    main() 
"""
權限控制功能測試
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch

# 確保可以導入主應用
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestPermissionLogic:
    """權限邏輯測試"""
    
    def test_general_user_permissions(self, sample_user_general):
        """測試一般用戶權限"""
        user = sample_user_general
        
        # 應該具備的權限
        assert "CREATE_PERSONAL_DATA" in user["permissions"]
        assert "READ_OWN_DATA" in user["permissions"]
        assert "UPDATE_OWN_DATA" in user["permissions"]
        
        # 不應該具備的權限
        assert "READ_ALL_DATA" not in user["permissions"]
        assert "EXPORT_DATA" not in user["permissions"]
        assert "MANAGE_USERS" not in user["permissions"]
        assert "MANAGE_ROLES" not in user["permissions"]
        assert "VIEW_AUDIT_LOG" not in user["permissions"]
    
    def test_global_user_permissions(self, sample_user_global):
        """測試全域用戶權限"""
        user = sample_user_global
        
        # 應該具備基本權限
        assert "CREATE_PERSONAL_DATA" in user["permissions"]
        assert "READ_OWN_DATA" in user["permissions"]
        assert "UPDATE_OWN_DATA" in user["permissions"]
        
        # 應該具備額外權限
        assert "READ_ALL_DATA" in user["permissions"]
        assert "EXPORT_DATA" in user["permissions"]
        
        # 不應該具備管理權限
        assert "MANAGE_USERS" not in user["permissions"]
        assert "MANAGE_ROLES" not in user["permissions"]
        assert "VIEW_AUDIT_LOG" not in user["permissions"]
    
    def test_admin_permissions(self, sample_user_admin):
        """測試管理者權限"""
        user = sample_user_admin
        
        # 應該具備所有權限
        expected_permissions = [
            "CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA",
            "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_USERS", 
            "MANAGE_ROLES", "VIEW_AUDIT_LOG"
        ]
        
        for permission in expected_permissions:
            assert permission in user["permissions"], f"管理者應該具備 {permission} 權限"

class TestRoleBasedAccess:
    """角色權限存取測試"""
    
    @patch('streamlit.session_state')
    def test_has_permission_function(self, mock_session_state, sample_user_general):
        """測試has_permission函數"""
        # 模擬session_state
        mock_session_state.user_info = sample_user_general
        
        # 導入函數進行測試
        from main import has_permission
        
        # 測試有權限的情況
        assert has_permission("CREATE_PERSONAL_DATA") == True
        assert has_permission("READ_OWN_DATA") == True
        
        # 測試無權限的情況
        assert has_permission("READ_ALL_DATA") == False
        assert has_permission("MANAGE_USERS") == False
    
    @patch('streamlit.session_state')
    def test_has_role_function(self, mock_session_state, sample_user_global):
        """測試has_role函數"""
        # 模擬session_state
        mock_session_state.user_info = sample_user_global
        
        # 導入函數進行測試
        from main import has_role
        
        # 測試角色檢查
        assert has_role("global_user") == True
        assert has_role("admin") == False
        assert has_role("general_user") == False
    
    @patch('streamlit.session_state')
    def test_empty_user_info(self, mock_session_state):
        """測試空用戶資訊情況"""
        # 模擬沒有用戶資訊的情況
        mock_session_state.user_info = None
        
        from main import has_permission, has_role
        
        # 沒有用戶資訊時應該返回False
        assert has_permission("CREATE_PERSONAL_DATA") == False
        assert has_role("general_user") == False

class TestDataAccessControl:
    """資料存取控制測試"""
    
    def test_data_filtering_by_role(self, sample_personal_data):
        """測試根據角色篩選資料"""
        all_data = sample_personal_data
        
        # 模擬一般用戶只能看自己的資料
        def filter_data_for_user(data, username, roles):
            if 'admin' not in roles and 'global_user' not in roles:
                return [item for item in data if item['created_by'] == username]
            return data
        
        # 測試一般用戶
        general_user_data = filter_data_for_user(all_data, "general_user", ["general_user"])
        assert len(general_user_data) == 1
        assert general_user_data[0]["created_by"] == "general_user"
        
        # 測試全域用戶
        global_user_data = filter_data_for_user(all_data, "global_user", ["global_user"])
        assert len(global_user_data) == 2  # 可以看到所有資料
        
        # 測試管理者
        admin_data = filter_data_for_user(all_data, "admin", ["admin"])
        assert len(admin_data) == 2  # 可以看到所有資料
    
    def test_data_masking(self, sample_personal_data):
        """測試資料遮罩功能"""
        data = sample_personal_data[0]
        
        # 檢查身分證字號是否已遮罩
        id_number_masked = data["id_number_masked"]
        assert "****" in id_number_masked, "身分證字號應該包含遮罩"
        assert len(id_number_masked) == 10, "遮罩後長度應該正確"
        assert id_number_masked.startswith("A123"), "應該顯示前四位"
        assert id_number_masked.endswith("89"), "應該顯示後兩位"

class TestPermissionValidation:
    """權限驗證測試"""
    
    def test_page_access_control(self):
        """測試頁面存取控制邏輯"""
        # 模擬頁面存取檢查
        def check_page_access(user_roles, page_name):
            page_permissions = {
                "新增資料": ["general_user", "global_user", "admin"],
                "查詢資料": ["general_user", "global_user", "admin"],
                "統計報表": ["global_user", "admin"],
                "資料匯出": ["global_user", "admin"],
                "用戶管理": ["admin"],
                "角色權限": ["admin"],
                "審計日誌": ["admin"]
            }
            
            required_roles = page_permissions.get(page_name, [])
            return any(role in user_roles for role in required_roles)
        
        # 測試一般用戶
        general_roles = ["general_user"]
        assert check_page_access(general_roles, "新增資料") == True
        assert check_page_access(general_roles, "查詢資料") == True
        assert check_page_access(general_roles, "統計報表") == False
        assert check_page_access(general_roles, "用戶管理") == False
        
        # 測試全域用戶
        global_roles = ["global_user"]
        assert check_page_access(global_roles, "新增資料") == True
        assert check_page_access(global_roles, "統計報表") == True
        assert check_page_access(global_roles, "資料匯出") == True
        assert check_page_access(global_roles, "用戶管理") == False
        
        # 測試管理者
        admin_roles = ["admin"]
        assert check_page_access(admin_roles, "用戶管理") == True
        assert check_page_access(admin_roles, "角色權限") == True
        assert check_page_access(admin_roles, "審計日誌") == True
    
    def test_export_permission(self):
        """測試匯出權限檢查"""
        def can_export(user_roles):
            return "global_user" in user_roles or "admin" in user_roles
        
        # 測試不同角色的匯出權限
        assert can_export(["general_user"]) == False
        assert can_export(["global_user"]) == True
        assert can_export(["admin"]) == True
        assert can_export(["global_user", "admin"]) == True
    
    def test_management_permission(self):
        """測試管理權限檢查"""
        def can_manage_users(user_roles):
            return "admin" in user_roles
        
        def can_view_audit_log(user_roles):
            return "admin" in user_roles
        
        # 測試管理權限
        assert can_manage_users(["general_user"]) == False
        assert can_manage_users(["global_user"]) == False
        assert can_manage_users(["admin"]) == True
        
        assert can_view_audit_log(["general_user"]) == False
        assert can_view_audit_log(["global_user"]) == False
        assert can_view_audit_log(["admin"]) == True

class TestPermissionInheritance:
    """權限繼承測試"""
    
    def test_role_hierarchy(self):
        """測試角色層級關係"""
        # 定義角色層級
        role_hierarchy = {
            "general_user": 1,
            "global_user": 2,
            "admin": 3
        }
        
        # 測試層級比較
        assert role_hierarchy["admin"] > role_hierarchy["global_user"]
        assert role_hierarchy["global_user"] > role_hierarchy["general_user"]
        assert role_hierarchy["admin"] > role_hierarchy["general_user"]
    
    def test_permission_inheritance(self):
        """測試權限繼承邏輯"""
        # 基礎權限
        base_permissions = ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "UPDATE_OWN_DATA"]
        
        # 全域權限（繼承基礎權限）
        global_permissions = base_permissions + ["READ_ALL_DATA", "EXPORT_DATA"]
        
        # 管理權限（繼承全域權限）
        admin_permissions = global_permissions + ["MANAGE_USERS", "MANAGE_ROLES", "VIEW_AUDIT_LOG"]
        
        # 驗證權限繼承
        for perm in base_permissions:
            assert perm in global_permissions, f"全域用戶應該繼承基礎權限: {perm}"
            assert perm in admin_permissions, f"管理者應該繼承基礎權限: {perm}"
        
        for perm in global_permissions:
            assert perm in admin_permissions, f"管理者應該繼承全域權限: {perm}" 
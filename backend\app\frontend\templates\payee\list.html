{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題和操作 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="bi bi-people"></i> 受款人管理
            </h1>
            <p class="text-muted">管理和查看所有受款人資料</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="/payee/create" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> 新增受款人
            </a>
        </div>
    </div>
    
    <!-- 搜尋和篩選 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" action="/payee" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" name="search" 
                                       placeholder="搜尋姓名、身分證號、電話..." 
                                       value="{{ search }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="bi bi-search"></i> 搜尋
                                </button>
                                <a href="/payee" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> 重設
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 受款人列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        受款人列表 (共 {{ pagination.total }} 筆)
                    </h6>
                </div>
                <div class="card-body">
                    {% if payees %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>姓名</th>
                                    <th>身分證號</th>
                                    <th>電話</th>
                                    <th>銀行</th>
                                    <th>帳號</th>
                                    <th>部門</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payee in payees %}
                                <tr>
                                    <td>
                                        <strong>{{ payee.name }}</strong>
                                    </td>
                                    <td>
                                        {% if payee.id_number != "解密失敗" %}
                                            <code>{{ payee.id_number }}</code>
                                        {% else %}
                                            <span class="text-warning">
                                                <i class="fas fa-exclamation-triangle"></i> 解密失敗
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ payee.phone or '-' }}</td>
                                    <td>{{ payee.bank_name or '-' }}</td>
                                    <td>
                                        {% if payee.account_number %}
                                            <code>{{ payee.account_number }}</code>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ payee.department or '-' }}</td>
                                    <td>
                                        {% if payee.is_active %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> 啟用
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-x-circle"></i> 停用
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/payee/{{ payee.id }}" class="btn btn-outline-info" title="查看詳情">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="/payee/{{ payee.id }}/edit" class="btn btn-outline-primary" title="編輯">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmDelete('{{ payee.id }}', '{{ payee.name }}')" title="刪除">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分頁 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="分頁導航">
                        <ul class="pagination justify-content-center">
                            {% if pagination.page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.page - 1 }}{% if search %}&search={{ search }}{% endif %}">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in range(1, pagination.pages + 1) %}
                                {% if page_num == pagination.page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% elif page_num <= 3 or page_num > pagination.pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_num }}{% if search %}&search={{ search }}{% endif %}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% elif page_num == 4 or page_num == pagination.pages - 3 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.page < pagination.pages %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.page + 1 }}{% if search %}&search={{ search }}{% endif %}">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">沒有找到受款人</h4>
                        <p class="text-muted">
                            {% if search %}
                                沒有符合搜尋條件的受款人，請嘗試其他關鍵字。
                            {% else %}
                                系統中還沒有任何受款人資料。
                            {% endif %}
                        </p>
                        <a href="/payee/create" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> 新增第一個受款人
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 刪除確認模態框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning"></i> 確認刪除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您確定要刪除受款人 <strong id="deletePayeeName"></strong> 嗎？</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i> 
                    此操作無法復原，請謹慎操作。
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> 確認刪除
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(payeeId, payeeName) {
    document.getElementById('deletePayeeName').textContent = payeeName;
    document.getElementById('deleteForm').action = '/payee/' + payeeId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}

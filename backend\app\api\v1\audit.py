"""
審計日誌API路由
"""
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.database import get_db
from app.models.user import User
from app.models.audit_log import AuditLog
from app.schemas.audit import AuditLogResponse, AuditLogListResponse, AuditLogQuery
from app.core.dependencies import get_current_active_user, require_roles
from app.core.exceptions import CBAException


router = APIRouter(prefix="/audit", tags=["審計日誌"])


def parse_json_values(value: Optional[str]) -> Optional[Dict[str, Any]]:
    """解析JSON字符串為字典"""
    if not value:
        return None
    try:
        return json.loads(value)
    except (json.JSONDecodeError, TypeError):
        return None


@router.get("/logs", response_model=AuditLogListResponse)
async def get_audit_logs(
    user_id: Optional[int] = Query(None, description="用戶ID篩選"),
    action: Optional[str] = Query(None, description="操作類型篩選"),
    table_name: Optional[str] = Query(None, description="表名篩選"),
    start_date: Optional[datetime] = Query(None, description="開始日期"),
    end_date: Optional[datetime] = Query(None, description="結束日期"),
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(20, ge=1, le=100, description="每頁筆數"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles("admin", "global"))
):
    """
    取得審計日誌列表
    
    需要權限：管理者或全域用戶
    """
    try:
        # 建立基本查詢
        query = db.query(AuditLog).join(User)
        
        # 應用篩選條件
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if action:
            query = query.filter(AuditLog.action.ilike(f"%{action}%"))
        
        if table_name:
            query = query.filter(AuditLog.table_name == table_name)
        
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)
        
        # 計算總數
        total = query.count()
        
        # 分頁和排序
        offset = (page - 1) * size
        logs = query.order_by(desc(AuditLog.timestamp)).offset(offset).limit(size).all()
        
        # 轉換為回應模型
        response_items = []
        for log in logs:
            response_items.append(AuditLogResponse(
                id=log.id,
                user_id=log.user_id,
                username=log.user.full_name if log.user else "未知用戶",
                action=log.action,
                table_name=log.table_name,
                record_id=log.record_id,
                old_values=parse_json_values(log.old_values),
                new_values=parse_json_values(log.new_values),
                ip_address=log.ip_address,
                user_agent=log.user_agent,
                timestamp=log.timestamp
            ))
        
        # 計算頁數
        pages = (total + size - 1) // size
        
        return AuditLogListResponse(
            items=response_items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/logs/{log_id}", response_model=AuditLogResponse)
async def get_audit_log(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles("admin", "global"))
):
    """
    取得特定審計日誌
    
    需要權限：管理者或全域用戶
    """
    try:
        log = db.query(AuditLog).join(User).filter(AuditLog.id == log_id).first()
        
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="找不到指定的審計日誌"
            )
        
        return AuditLogResponse(
            id=log.id,
            user_id=log.user_id,
            username=log.user.full_name if log.user else "未知用戶",
            action=log.action,
            table_name=log.table_name,
            record_id=log.record_id,
            old_values=parse_json_values(log.old_values),
            new_values=parse_json_values(log.new_values),
            ip_address=log.ip_address,
            user_agent=log.user_agent,
            timestamp=log.timestamp
        )
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/actions", response_model=List[str])
async def get_audit_actions(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles("admin", "global"))
):
    """
    取得所有操作類型
    
    需要權限：管理者或全域用戶
    """
    try:
        actions = db.query(AuditLog.action).distinct().all()
        return [action[0] for action in actions]
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/tables", response_model=List[str])
async def get_audit_tables(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles("admin", "global"))
):
    """
    取得所有表名
    
    需要權限：管理者或全域用戶
    """
    try:
        tables = db.query(AuditLog.table_name).distinct().all()
        return [table[0] for table in tables]
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/users/{user_id}/logs", response_model=AuditLogListResponse)
async def get_user_audit_logs(
    user_id: int,
    action: Optional[str] = Query(None, description="操作類型篩選"),
    table_name: Optional[str] = Query(None, description="表名篩選"),
    start_date: Optional[datetime] = Query(None, description="開始日期"),
    end_date: Optional[datetime] = Query(None, description="結束日期"),
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(20, ge=1, le=100, description="每頁筆數"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles("admin", "global"))
):
    """
    取得特定用戶的審計日誌
    
    需要權限：管理者或全域用戶
    """
    try:
        # 建立基本查詢
        query = db.query(AuditLog).join(User).filter(AuditLog.user_id == user_id)
        
        # 應用篩選條件
        if action:
            query = query.filter(AuditLog.action.ilike(f"%{action}%"))
        
        if table_name:
            query = query.filter(AuditLog.table_name == table_name)
        
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)
        
        # 計算總數
        total = query.count()
        
        # 分頁和排序
        offset = (page - 1) * size
        logs = query.order_by(desc(AuditLog.timestamp)).offset(offset).limit(size).all()
        
        # 轉換為回應模型
        response_items = []
        for log in logs:
            response_items.append(AuditLogResponse(
                id=log.id,
                user_id=log.user_id,
                username=log.user.full_name if log.user else "未知用戶",
                action=log.action,
                table_name=log.table_name,
                record_id=log.record_id,
                old_values=parse_json_values(log.old_values),
                new_values=parse_json_values(log.new_values),
                ip_address=log.ip_address,
                user_agent=log.user_agent,
                timestamp=log.timestamp
            ))
        
        # 計算頁數
        pages = (total + size - 1) // size
        
        return AuditLogListResponse(
            items=response_items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        ) 
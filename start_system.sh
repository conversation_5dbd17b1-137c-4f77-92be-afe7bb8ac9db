#!/bin/bash

# CBA受款人資料搜集系統 - 整合啟動器 (Linux/macOS)

echo "========================================"
echo "CBA受款人資料搜集系統 - 整合啟動器"
echo "========================================"
echo

# 檢查Python是否安裝
if ! command -v python3 &> /dev/null; then
    echo "❌ 錯誤: 未找到Python3，請先安裝Python 3.9或更高版本"
    exit 1
fi

# 檢查uv是否安裝
if ! command -v uv &> /dev/null; then
    echo "❌ 錯誤: 未找到uv包管理器，請先安裝uv"
    echo "安裝命令: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "✅ 環境檢查通過"
echo

# 設定環境變數
export API_BASE_URL=http://localhost:8080
export FRONTEND_URL=http://localhost:8501

echo "🚀 啟動整合系統..."
echo

# 使用Python啟動腳本
python3 start_integrated_system.py

echo
echo "系統已停止"

<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-header">
        <div class="logo">
          <img src="/logo.png" alt="CBA Logo" class="logo-img" />
          <h1 class="logo-text">{{ appTitle }}</h1>
        </div>
      </div>
      
      <div class="auth-content">
        <router-view />
      </div>
      
      <div class="auth-footer">
        <p>&copy; {{ currentYear }} CBA受款人資料搜集系統. All rights reserved.</p>
      </div>
    </div>
    
    <!-- 背景裝飾 -->
    <div class="auth-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const appTitle = import.meta.env.VITE_APP_TITLE
const currentYear = new Date().getFullYear()
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  
  .auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 480px;
    position: relative;
    z-index: 2;
    
    .auth-header {
      text-align: center;
      margin-bottom: 32px;
      
      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        
        .logo-img {
          width: 48px;
          height: 48px;
          border-radius: 8px;
        }
        
        .logo-text {
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }
      }
    }
    
    .auth-content {
      margin-bottom: 24px;
    }
    
    .auth-footer {
      text-align: center;
      
      p {
        color: #6b7280;
        font-size: 14px;
        margin: 0;
      }
    }
  }
  
  .auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    
    .bg-shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
      
      &.shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }
      
      &.shape-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 10%;
        animation-delay: 2s;
      }
      
      &.shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 響應式設計
@media (max-width: 768px) {
  .auth-layout {
    padding: 20px;
    
    .auth-container {
      padding: 24px;
      
      .auth-header .logo .logo-text {
        font-size: 20px;
      }
    }
  }
}
</style>

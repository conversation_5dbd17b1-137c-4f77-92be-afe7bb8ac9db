/**
 * 認證相關類型定義
 */

export interface User {
  id: string
  username: string
  email?: string
  full_name?: string
  department?: string
  role: UserRole
  permissions: string[]
  is_active: boolean
  created_at: string
  last_login?: string
}

export type UserRole = 'admin' | 'global' | 'user'

export interface LoginCredentials {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface SSOLoginRequest {
  sso_token: string
  redirect_url?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  permissions: string[]
  loading: boolean
}

export interface Permission {
  id: string
  name: string
  description: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
}

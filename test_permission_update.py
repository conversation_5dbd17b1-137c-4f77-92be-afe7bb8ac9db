#!/usr/bin/env python3
"""
測試權限更新 - 一般人員不需資料概覽功能
"""

import sys
import os

def test_navigation_permissions():
    """測試導覽權限設定"""
    print("🔒 測試導覽權限設定...")
    
    try:
        # 添加路徑
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        # 模擬不同角色的AuthManager
        class MockAuthManager:
            def __init__(self, is_admin=False, roles=None):
                self._is_admin = is_admin
                self._roles = roles or []
            
            def is_admin(self):
                return self._is_admin
            
            def has_role(self, role):
                return role in self._roles
            
            def get_navigation_pages(self):
                """模擬get_navigation_pages邏輯"""
                pages = [
                    {
                        "key": "create_data",
                        "title": "💰 受款人管理",
                        "requires_role": None
                    }
                ]
                
                # 管理員和全域用戶可見的頁面
                if self.is_admin() or self.has_role("global"):
                    pages.insert(0, {
                        "key": "dashboard",
                        "title": "📊 資料概覽",
                        "requires_role": ["admin", "global"]
                    })
                
                # 管理員專用頁面
                if self.is_admin():
                    pages.extend([
                        {
                            "key": "statistics",
                            "title": "📈 統計報表",
                            "requires_role": ["admin", "global"]
                        },
                        {
                            "key": "data_export",
                            "title": "💾 資料匯出",
                            "requires_role": ["admin", "global"]
                        },
                        {
                            "key": "user_management",
                            "title": "👥 用戶管理",
                            "requires_role": ["admin", "global"]
                        },
                        {
                            "key": "role_management",
                            "title": "🔒 角色管理",
                            "requires_role": ["admin"]
                        },
                        {
                            "key": "department_management",
                            "title": "🏢 部門管理",
                            "requires_role": ["admin"]
                        },
                        {
                            "key": "audit_log",
                            "title": "📋 審計日誌",
                            "requires_role": ["admin"]
                        }
                    ])
                
                return pages
        
        # 測試不同角色的導覽頁面
        test_cases = [
            {
                "name": "一般用戶",
                "is_admin": False,
                "roles": [],
                "expected_pages": ["create_data"],
                "should_see_dashboard": False
            },
            {
                "name": "全域用戶",
                "is_admin": False,
                "roles": ["global"],
                "expected_pages": ["dashboard", "create_data"],
                "should_see_dashboard": True
            },
            {
                "name": "管理員",
                "is_admin": True,
                "roles": ["admin"],
                "expected_pages": ["dashboard", "create_data", "statistics", "data_export", "user_management", "role_management", "department_management", "audit_log"],
                "should_see_dashboard": True
            }
        ]
        
        all_passed = True
        
        for case in test_cases:
            print(f"\n📋 測試 {case['name']}...")
            
            auth_manager = MockAuthManager(
                is_admin=case["is_admin"],
                roles=case["roles"]
            )
            
            pages = auth_manager.get_navigation_pages()
            page_keys = [page["key"] for page in pages]
            
            print(f"   可見頁面: {page_keys}")
            
            # 檢查預期頁面
            for expected_page in case["expected_pages"]:
                if expected_page in page_keys:
                    print(f"   ✅ {expected_page} - 正確顯示")
                else:
                    print(f"   ❌ {expected_page} - 應該顯示但未顯示")
                    all_passed = False
            
            # 檢查資料概覽權限
            has_dashboard = "dashboard" in page_keys
            if has_dashboard == case["should_see_dashboard"]:
                status = "✅" if case["should_see_dashboard"] else "✅"
                action = "可以" if case["should_see_dashboard"] else "不能"
                print(f"   {status} 資料概覽 - {action}查看（正確）")
            else:
                status = "❌"
                expected_action = "可以" if case["should_see_dashboard"] else "不能"
                actual_action = "可以" if has_dashboard else "不能"
                print(f"   {status} 資料概覽 - 期望{expected_action}查看，實際{actual_action}查看")
                all_passed = False
        
        if all_passed:
            print("\n✅ 所有權限測試通過！")
        else:
            print("\n❌ 部分權限測試失敗！")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 權限測試失敗: {str(e)}")
        return False

def test_default_page_logic():
    """測試預設頁面邏輯"""
    print("\n🏠 測試預設頁面邏輯...")
    
    try:
        # 模擬預設頁面邏輯
        def get_default_page(is_admin, has_global_role):
            return 'dashboard' if (is_admin or has_global_role) else 'create_data'
        
        test_cases = [
            {
                "name": "一般用戶",
                "is_admin": False,
                "has_global": False,
                "expected": "create_data"
            },
            {
                "name": "全域用戶",
                "is_admin": False,
                "has_global": True,
                "expected": "dashboard"
            },
            {
                "name": "管理員",
                "is_admin": True,
                "has_global": False,
                "expected": "dashboard"
            }
        ]
        
        all_passed = True
        
        for case in test_cases:
            default_page = get_default_page(case["is_admin"], case["has_global"])
            
            if default_page == case["expected"]:
                print(f"   ✅ {case['name']}: 預設頁面 {default_page}")
            else:
                print(f"   ❌ {case['name']}: 期望 {case['expected']}，實際 {default_page}")
                all_passed = False
        
        if all_passed:
            print("✅ 預設頁面邏輯測試通過！")
        else:
            print("❌ 預設頁面邏輯測試失敗！")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 預設頁面測試失敗: {str(e)}")
        return False

def test_permission_summary():
    """測試權限總結"""
    print("\n📊 權限設定總結...")
    
    permissions = {
        "一般用戶": {
            "可見頁面": ["💰 受款人管理"],
            "預設頁面": "💰 受款人管理",
            "資料概覽": "❌ 不可見"
        },
        "全域用戶": {
            "可見頁面": ["📊 資料概覽", "💰 受款人管理"],
            "預設頁面": "📊 資料概覽",
            "資料概覽": "✅ 可見"
        },
        "管理員": {
            "可見頁面": ["📊 資料概覽", "💰 受款人管理", "📈 統計報表", "💾 資料匯出", "👥 用戶管理", "🔒 角色管理", "🏢 部門管理", "📋 審計日誌"],
            "預設頁面": "📊 資料概覽",
            "資料概覽": "✅ 可見"
        }
    }
    
    for role, perms in permissions.items():
        print(f"\n👤 {role}:")
        print(f"   📄 可見頁面: {', '.join(perms['可見頁面'])}")
        print(f"   🏠 預設頁面: {perms['預設頁面']}")
        print(f"   📊 資料概覽: {perms['資料概覽']}")
    
    print("\n✅ 權限設定符合需求：一般人員不需資料概覽功能")
    return True

def main():
    """主測試函數"""
    print("🧪 開始測試權限更新...\n")
    
    results = []
    
    # 執行各項測試
    results.append(test_navigation_permissions())
    results.append(test_default_page_logic())
    results.append(test_permission_summary())
    
    # 總結測試結果
    print(f"\n📊 測試結果總結:")
    print(f"✅ 通過測試: {sum(results)}")
    print(f"❌ 失敗測試: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有權限更新測試通過！")
        print("\n💡 權限更新效果：")
        print("   • 一般用戶無法看到資料概覽")
        print("   • 一般用戶預設進入受款人管理")
        print("   • 全域用戶和管理員保持原有權限")
        print("   • 權限控制更加精確")
        return 0
    else:
        print("\n⚠️ 部分測試失敗，請檢查權限設定")
        return 1

if __name__ == "__main__":
    sys.exit(main())

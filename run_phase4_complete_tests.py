#!/usr/bin/env python3
"""
CBA個人資料搜集系統 - 第四階段完整測試執行器

執行順序：
1. 系統整合測試
2. 安全測試
3. 效能測試
4. 用戶接受測試
5. 部署準備測試
6. 最終整合測試
"""

import sys
import os
import subprocess
import time
from datetime import datetime, timedelta
from pathlib import Path
import json
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "backend"))
# 將工作目錄切換到backend目錄以確保正確的模組導入
os.chdir(str(Path(__file__).parent / "backend"))

class Phase4CompleteTestRunner:
    """第四階段完整測試執行器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.test_results = {}
        self.overall_results = []
        
    def run_integration_tests(self):
        """執行系統整合測試"""
        print("\n" + "="*80)
        print("🔄 第1階段：系統整合測試")
        print("="*80)
        
        try:
            # 執行整合測試
            from tests.test_phase4_integration import TestPhase4Integration
            
            integration_test = TestPhase4Integration()
            integration_test.setup_method()
            result = integration_test.run_all_integration_tests()
            
            self.test_results['integration'] = {
                'passed': result,
                'timestamp': datetime.now(),
                'test_count': 15,
                'category': '系統整合測試'
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 系統整合測試執行失敗: {str(e)}")
            self.test_results['integration'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '系統整合測試'
            }
            return False
    
    def run_security_tests(self):
        """執行安全測試"""
        print("\n" + "="*80)
        print("🔒 第2階段：安全測試")
        print("="*80)
        
        try:
            # 執行安全測試
            from tests.test_phase4_security import TestPhase4Security
            
            security_test = TestPhase4Security()
            security_test.setup_method()
            result = security_test.run_all_security_tests()
            
            self.test_results['security'] = {
                'passed': result,
                'timestamp': datetime.now(),
                'test_count': 12,
                'category': '安全測試'
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 安全測試執行失敗: {str(e)}")
            self.test_results['security'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '安全測試'
            }
            return False
    
    def run_performance_tests(self):
        """執行效能測試"""
        print("\n" + "="*80)
        print("⚡ 第3階段：效能測試")
        print("="*80)
        
        try:
            # 執行效能測試
            sys.path.insert(0, str(Path(__file__).parent.parent))
            from test_phase4_performance import PerformanceTestRunner
            
            performance_test = PerformanceTestRunner()
            result = performance_test.run_all_performance_tests()
            
            self.test_results['performance'] = {
                'passed': result,
                'timestamp': datetime.now(),
                'test_count': 8,
                'category': '效能測試'
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 效能測試執行失敗: {str(e)}")
            self.test_results['performance'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '效能測試'
            }
            return False
    
    def run_user_acceptance_tests(self):
        """執行用戶接受測試"""
        print("\n" + "="*80)
        print("👥 第4階段：用戶接受測試")
        print("="*80)
        
        try:
            # 執行用戶接受測試
            sys.path.insert(0, str(Path(__file__).parent.parent))
            from test_phase4_user_acceptance import UserAcceptanceTestRunner
            
            user_test = UserAcceptanceTestRunner()
            result = user_test.run_all_user_acceptance_tests()
            
            self.test_results['user_acceptance'] = {
                'passed': result,
                'timestamp': datetime.now(),
                'test_count': 10,
                'category': '用戶接受測試'
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 用戶接受測試執行失敗: {str(e)}")
            self.test_results['user_acceptance'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '用戶接受測試'
            }
            return False
    
    def run_deployment_tests(self):
        """執行部署準備測試"""
        print("\n" + "="*80)
        print("🚀 第5階段：部署準備測試")
        print("="*80)
        
        try:
            # 執行部署準備測試
            sys.path.insert(0, str(Path(__file__).parent.parent))
            from test_phase4_deployment import DeploymentTestRunner
            
            deployment_test = DeploymentTestRunner()
            result = deployment_test.run_all_deployment_tests()
            
            self.test_results['deployment'] = {
                'passed': result,
                'timestamp': datetime.now(),
                'test_count': 6,
                'category': '部署準備測試'
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 部署準備測試執行失敗: {str(e)}")
            self.test_results['deployment'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '部署準備測試'
            }
            return False
    
    def run_final_integration_test(self):
        """執行最終整合測試"""
        print("\n" + "="*80)
        print("🔗 第6階段：最終整合測試")
        print("="*80)
        
        try:
            # 最終整合測試
            print("📋 執行最終系統驗證...")
            
            # 1. 檢查所有前置測試是否通過
            critical_tests = ['integration', 'security']
            failed_critical = [test for test in critical_tests 
                             if not self.test_results.get(test, {}).get('passed', False)]
            
            if failed_critical:
                print(f"❌ 關鍵測試失敗: {failed_critical}")
                return False
            
            # 2. 執行端到端驗證
            print("🔍 執行端到端系統驗證...")
            e2e_result = self._run_end_to_end_verification()
            
            # 3. 執行系統穩定性測試
            print("⚖️  執行系統穩定性測試...")
            stability_result = self._run_stability_test()
            
            # 4. 執行最終安全掃描
            print("🛡️  執行最終安全掃描...")
            security_scan_result = self._run_final_security_scan()
            
            final_result = e2e_result and stability_result and security_scan_result
            
            self.test_results['final_integration'] = {
                'passed': final_result,
                'timestamp': datetime.now(),
                'test_count': 3,
                'category': '最終整合測試',
                'details': {
                    'e2e_verification': e2e_result,
                    'stability_test': stability_result,
                    'security_scan': security_scan_result
                }
            }
            
            return final_result
            
        except Exception as e:
            print(f"❌ 最終整合測試執行失敗: {str(e)}")
            self.test_results['final_integration'] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now(),
                'category': '最終整合測試'
            }
            return False
    
    def _run_end_to_end_verification(self):
        """執行端到端驗證"""
        try:
            import requests
            
            # 模擬完整的用戶流程
            base_url = "http://localhost:8000"
            
            # 1. 健康檢查
            health_response = requests.get(f"{base_url}/health", timeout=10)
            if health_response.status_code != 200:
                print("❌ 健康檢查失敗")
                return False
            
            # 2. 認證流程
            auth_response = requests.get(f"{base_url}/api/v1/auth/sso_login?ssoToken1=test_token", timeout=10)
            if auth_response.status_code not in [200, 302, 401]:  # 401也是正常的
                print("❌ 認證流程失敗")
                return False
            
            # 3. API可達性測試
            headers = {"Authorization": "Bearer test_token"}
            api_response = requests.get(f"{base_url}/api/v1/personal-data", headers=headers, timeout=10)
            if api_response.status_code not in [200, 401]:  # 401也是正常的
                print("❌ API可達性測試失敗")
                return False
            
            print("✅ 端到端驗證通過")
            return True
            
        except Exception as e:
            print(f"❌ 端到端驗證失敗: {str(e)}")
            return False
    
    def _run_stability_test(self):
        """執行系統穩定性測試"""
        try:
            import requests
            import time
            
            base_url = "http://localhost:8000"
            
            # 連續進行多次請求測試系統穩定性
            stable_requests = 0
            total_requests = 10
            
            for i in range(total_requests):
                try:
                    response = requests.get(f"{base_url}/health", timeout=5)
                    if response.status_code == 200:
                        stable_requests += 1
                    time.sleep(1)  # 間隔1秒
                except:
                    pass
            
            stability_rate = stable_requests / total_requests
            print(f"系統穩定性: {stability_rate:.1%}")
            
            if stability_rate >= 0.8:
                print("✅ 系統穩定性測試通過")
                return True
            else:
                print("❌ 系統穩定性測試失敗")
                return False
            
        except Exception as e:
            print(f"❌ 穩定性測試失敗: {str(e)}")
            return False
    
    def _run_final_security_scan(self):
        """執行最終安全掃描"""
        try:
            import requests
            
            base_url = "http://localhost:8000"
            
            # 檢查安全標頭
            response = requests.get(f"{base_url}/health", timeout=5)
            
            security_headers = [
                "X-Content-Type-Options",
                "X-Frame-Options",
                "X-XSS-Protection"
            ]
            
            present_headers = sum(1 for header in security_headers if header in response.headers)
            security_score = present_headers / len(security_headers)
            
            print(f"安全標頭覆蓋率: {security_score:.1%}")
            
            # 檢查未授權存取防護
            unauth_response = requests.get(f"{base_url}/api/v1/personal-data", timeout=5)
            auth_protection = unauth_response.status_code == 401
            
            print(f"未授權存取防護: {'✅ 啟用' if auth_protection else '❌ 未啟用'}")
            
            final_security_ok = security_score >= 0.5 and auth_protection
            
            if final_security_ok:
                print("✅ 最終安全掃描通過")
                return True
            else:
                print("❌ 最終安全掃描失敗")
                return False
            
        except Exception as e:
            print(f"❌ 最終安全掃描失敗: {str(e)}")
            return False
    
    def generate_final_report(self):
        """生成最終測試報告"""
        print("\n" + "="*80)
        print("📊 生成第四階段測試總結報告")
        print("="*80)
        
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        # 計算整體統計
        total_tests = sum(result.get('test_count', 0) for result in self.test_results.values())
        passed_categories = sum(1 for result in self.test_results.values() if result.get('passed', False))
        total_categories = len(self.test_results)
        
        overall_success_rate = passed_categories / total_categories if total_categories > 0 else 0
        
        # 生成報告
        report = {
            "測試概要": {
                "測試開始時間": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "測試結束時間": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "總測試時間": str(total_duration),
                "總測試案例數": total_tests,
                "通過類別數": passed_categories,
                "總類別數": total_categories,
                "整體成功率": f"{overall_success_rate:.1%}"
            },
            "詳細結果": {}
        }
        
        # 填入詳細結果
        for category, result in self.test_results.items():
            report["詳細結果"][result.get('category', category)] = {
                "狀態": "✅ 通過" if result.get('passed', False) else "❌ 失敗",
                "測試案例數": result.get('test_count', 0),
                "執行時間": result.get('timestamp', '').strftime("%H:%M:%S") if result.get('timestamp') else '',
                "錯誤訊息": result.get('error', '') if not result.get('passed', False) else ''
            }
        
        # 輸出控制台報告
        print("\n📋 測試結果摘要:")
        print(f"測試時間: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
        print(f"總測試時間: {total_duration}")
        print(f"總測試案例: {total_tests}")
        print(f"整體成功率: {overall_success_rate:.1%}")
        
        print("\n📊 各類別測試結果:")
        for category, result in self.test_results.items():
            status = "✅ 通過" if result.get('passed', False) else "❌ 失敗"
            test_count = result.get('test_count', 0)
            category_name = result.get('category', category)
            print(f"  {category_name}: {status} ({test_count}項測試)")
            
            if not result.get('passed', False) and result.get('error'):
                print(f"    錯誤: {result['error']}")
        
        # 評估結果
        print("\n🎯 第四階段測試評估:")
        if overall_success_rate >= 0.9:
            assessment = "🎉 優秀！所有測試類別通過，系統可安全上線。"
            recommendation = "建議立即部署到生產環境。"
        elif overall_success_rate >= 0.8:
            assessment = "✅ 良好！大部分測試通過，系統基本可上線。"
            recommendation = "建議修正失敗項目後部署。"
        elif overall_success_rate >= 0.6:
            assessment = "⚠️  普通！部分重要測試失敗，需要改善。"
            recommendation = "建議修正所有失敗項目後重新測試。"
        else:
            assessment = "🚨 需要改善！多項重要測試失敗，不建議上線。"
            recommendation = "必須修正所有關鍵問題後重新進行完整測試。"
        
        print(f"評估結果: {assessment}")
        print(f"建議行動: {recommendation}")
        
        # 保存詳細報告到文件
        report_file = f"第四階段測試完整報告_{end_time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 詳細報告已保存至: {report_file}")
        
        return overall_success_rate >= 0.8
    
    def run_complete_test_suite(self):
        """執行完整的第四階段測試套件"""
        print("🚀 CBA個人資料搜集系統 - 第四階段完整測試")
        print("="*80)
        print(f"測試開始時間: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 執行各階段測試
        test_stages = [
            ("系統整合測試", self.run_integration_tests),
            ("安全測試", self.run_security_tests),
            ("效能測試", self.run_performance_tests),
            ("用戶接受測試", self.run_user_acceptance_tests),
            ("部署準備測試", self.run_deployment_tests),
            ("最終整合測試", self.run_final_integration_test)
        ]
        
        for stage_name, test_function in test_stages:
            print(f"\n⏰ 開始執行: {stage_name}")
            stage_start = time.time()
            
            try:
                result = test_function()
                stage_duration = time.time() - stage_start
                
                status = "✅ 通過" if result else "❌ 失敗"
                print(f"📊 {stage_name}: {status} (耗時: {stage_duration:.1f}秒)")
                
                self.overall_results.append((stage_name, result, stage_duration))
                
                # 如果關鍵測試失敗，詢問是否繼續
                if not result and stage_name in ["系統整合測試", "安全測試"]:
                    print(f"\n⚠️  關鍵測試 '{stage_name}' 失敗！")
                    continue_test = input("是否繼續執行後續測試？(y/N): ").lower().strip()
                    if continue_test != 'y':
                        print("測試中止。")
                        break
                
            except Exception as e:
                print(f"❌ {stage_name} 執行異常: {str(e)}")
                self.overall_results.append((stage_name, False, 0))
        
        # 生成最終報告
        final_success = self.generate_final_report()
        
        return final_success


def main():
    """主程式入口"""
    # 檢查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更新版本")
        return False
    
    # 檢查必要的套件
    required_packages = [
        "fastapi",
        "sqlalchemy", 
        "requests",
        "pytest"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要套件: {', '.join(missing_packages)}")
        print(f"請執行: pip install {' '.join(missing_packages)}")
        return False
    
    # 執行完整測試
    test_runner = Phase4CompleteTestRunner()
    success = test_runner.run_complete_test_suite()
    
    if success:
        print("\n🎉 第四階段測試完成！系統準備就緒。")
        return True
    else:
        print("\n⚠️  第四階段測試完成，但存在需要改善的項目。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
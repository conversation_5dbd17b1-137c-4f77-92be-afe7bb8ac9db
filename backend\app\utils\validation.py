"""
資料驗證工具
"""

import re
from typing import List, Optional


class ValidationError(Exception):
    """驗證錯誤"""
    pass


def validate_id_number(id_number: str) -> bool:
    """
    驗證台灣身分證字號格式
    
    Args:
        id_number: 身分證字號
        
    Returns:
        True如果格式正確，False否則
    """
    if not id_number:
        return False
    
    # 移除空白字元並轉為大寫
    id_number = id_number.strip().upper()
    
    # 檢查基本格式：第一碼為英文字母，後9碼為數字
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False
    
    # 英文字母對應的數值表
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    # 取得英文字母對應的數值
    letter = id_number[0]
    if letter not in letter_values:
        return False
    
    letter_value = letter_values[letter]
    
    # 計算檢查碼
    # 英文字母的十位數 * 1 + 個位數 * 9
    checksum = (letter_value // 10) * 1 + (letter_value % 10) * 9
    
    # 第2到第9碼分別乘以8,7,6,5,4,3,2,1後加總
    for i in range(1, 9):
        checksum += int(id_number[i]) * (9 - i)
    
    # 加上第10碼（檢查碼）
    checksum += int(id_number[9])
    
    # 檢查是否能被10整除
    return checksum % 10 == 0


def validate_name(name: str) -> bool:
    """
    驗證姓名格式
    
    Args:
        name: 姓名
        
    Returns:
        True如果格式正確，False否則
    """
    if not name:
        return False
    
    name = name.strip()
    
    # 檢查長度（1-50字元）
    if len(name) < 1 or len(name) > 50:
        return False
    
    # 檢查是否包含中文、英文字母、數字、空格、點號、中間點
    pattern = r'^[\u4e00-\u9fff\u3400-\u4dbfa-zA-Z0-9\s\.\·]+$'
    return re.match(pattern, name) is not None


def validate_username(username: str) -> bool:
    """
    驗證用戶名格式
    
    Args:
        username: 用戶名
        
    Returns:
        True如果格式正確，False否則
    """
    if not username:
        return False
    
    username = username.strip()
    
    # 用戶名長度3-20字元，只能包含英文字母、數字、底線
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    return re.match(pattern, username) is not None


def validate_department_name(department_name: str) -> bool:
    """
    驗證部門名稱格式
    
    Args:
        department_name: 部門名稱
        
    Returns:
        True如果格式正確，False否則
    """
    if not department_name:
        return False
    
    department_name = department_name.strip()
    
    # 部門名稱長度2-100字元
    if len(department_name) < 2 or len(department_name) > 100:
        return False
    
    # 可包含中文、英文字母、數字、空格、常用標點符號
    pattern = r'^[\u4e00-\u9fff\u3400-\u4dbf\ua-zA-Z0-9\s\.\(\)\-\&]+$'
    return re.match(pattern, department_name) is not None


def sanitize_input(text: str) -> str:
    """
    清理輸入文字，移除潛在的危險字元
    
    Args:
        text: 輸入文字
        
    Returns:
        清理後的文字
    """
    if not text:
        return ""
    
    # 移除前後空白
    text = text.strip()
    
    # 移除控制字元（除了常用的換行符和製表符）
    text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
    
    # 限制長度（防止過長輸入）
    if len(text) > 10000:
        text = text[:10000]
    
    return text


def validate_id_number_list(id_numbers: List[str]) -> List[str]:
    """
    批次驗證身分證字號列表
    
    Args:
        id_numbers: 身分證字號列表
        
    Returns:
        無效的身分證字號列表
    """
    invalid_ids = []
    for id_number in id_numbers:
        if not validate_id_number(id_number):
            invalid_ids.append(id_number)
    return invalid_ids


def get_id_number_errors(id_number: str) -> List[str]:
    """
    取得身分證字號驗證錯誤詳細信息
    
    Args:
        id_number: 身分證字號
        
    Returns:
        錯誤信息列表
    """
    errors = []
    
    if not id_number:
        errors.append("身分證字號不能為空")
        return errors
    
    id_number = id_number.strip().upper()
    
    if len(id_number) != 10:
        errors.append("身分證字號必須為10位數")
    
    if not re.match(r'^[A-Z]', id_number):
        errors.append("第一位必須為英文字母")
    
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        errors.append("格式錯誤：第一位為英文字母，後9位為數字")
    
    # 如果基本格式正確，檢查檢查碼
    if len(errors) == 0 and not validate_id_number(id_number):
        errors.append("檢查碼驗證失敗")
    
    return errors 
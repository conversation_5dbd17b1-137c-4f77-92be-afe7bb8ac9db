#!/usr/bin/env python3
"""
CBA受款人資料搜集系統 - 快速啟動腳本
一鍵啟動整合系統並進行基本驗證
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def check_dependencies():
    """檢查依賴"""
    print("🔍 檢查系統依賴...")
    
    # 檢查Python
    try:
        import sys
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("❌ Python版本過低，需要Python 3.8或更高版本")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"❌ Python檢查失敗: {e}")
        return False
    
    # 檢查uv
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ uv {result.stdout.strip()}")
        else:
            print("❌ uv未安裝，請執行: pip install uv")
            return False
    except FileNotFoundError:
        print("❌ uv未安裝，請執行: pip install uv")
        return False
    
    # 檢查streamlit
    try:
        import streamlit
        print(f"✅ Streamlit {streamlit.__version__}")
    except ImportError:
        print("❌ Streamlit未安裝，請執行: pip install streamlit")
        return False
    
    # 檢查aiohttp（用於整合服務器）
    try:
        import aiohttp
        print(f"✅ aiohttp {aiohttp.__version__}")
    except ImportError:
        print("⚠️ aiohttp未安裝，將無法使用純Python整合服務器")
        print("  可執行: pip install aiohttp")
    
    return True

def check_project_structure():
    """檢查專案結構"""
    print("\n📁 檢查專案結構...")
    
    required_dirs = [
        "backend",
        "frontend"
    ]

    required_files = [
        "backend/main.py",
        "frontend/main.py"
    ]
    
    base_dir = Path(__file__).parent
    
    for dir_name in required_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ 目錄不存在")
            return False
    
    for file_name in required_files:
        file_path = base_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} 檔案不存在")
            return False
    
    return True

def install_dependencies():
    """安裝依賴套件"""
    print("\n📦 安裝依賴套件...")
    
    base_dir = Path(__file__).parent
    
    # 安裝後端依賴
    backend_dir = base_dir / "backend"
    if (backend_dir / "requirements.txt").exists():
        print("  安裝後端依賴...")
        try:
            subprocess.run(["uv", "sync"], cwd=backend_dir, check=True)
            print("  ✅ 後端依賴安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 後端依賴安裝失敗: {e}")
            return False
    
    # 安裝前端依賴
    frontend_dir = base_dir / "frontend"
    if (frontend_dir / "requirements.txt").exists():
        print("  安裝前端依賴...")
        try:
            subprocess.run(["pip", "install", "-r", "requirements.txt"], cwd=frontend_dir, check=True)
            print("  ✅ 前端依賴安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 前端依賴安裝失敗: {e}")
            return False
    
    # 安裝整合服務器依賴
    try:
        subprocess.run(["pip", "install", "aiohttp"], check=True)
        print("  ✅ 整合服務器依賴安裝完成")
    except subprocess.CalledProcessError as e:
        print(f"  ❌ 整合服務器依賴安裝失敗: {e}")
    
    return True

def start_integrated_system():
    """啟動整合系統"""
    print("\n🚀 啟動整合系統...")
    
    base_dir = Path(__file__).parent
    integrated_server_path = base_dir / "integrated_server.py"
    
    if not integrated_server_path.exists():
        print("❌ integrated_server.py 不存在")
        return False
    
    try:
        # 啟動整合服務器
        print("  啟動純Python整合服務器...")
        process = subprocess.Popen([sys.executable, str(integrated_server_path)])
        
        # 等待服務啟動
        print("  等待服務啟動...")
        time.sleep(10)
        
        # 檢查進程是否還在運行
        if process.poll() is not None:
            print("❌ 整合服務器啟動失敗")
            return False
        
        print("✅ 整合服務器已啟動")
        print("\n🌐 服務地址:")
        print("  • 主要入口: http://localhost:80")
        print("  • 前端UI: http://localhost:80/")
        print("  • 後端API: http://localhost:80/api/")
        print("  • SSO服務: http://localhost:80/sso/")
        print("  • 健康檢查: http://localhost:80/health")
        
        print("\n📝 注意事項:")
        print("  • 首次啟動可能需要較長時間")
        print("  • 如果80埠被佔用，請先停止其他服務")
        print("  • 按 Ctrl+C 停止服務")
        
        # 等待用戶中斷
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服務...")
            process.terminate()
            process.wait()
            print("✅ 服務已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 啟動整合系統失敗: {e}")
        return False

def main():
    """主函數"""
    print("🏢 CBA受款人資料搜集系統 - 快速啟動")
    print("=" * 50)
    
    # 檢查依賴
    if not check_dependencies():
        print("\n❌ 依賴檢查失敗，請解決上述問題後重試")
        sys.exit(1)
    
    # 檢查專案結構
    if not check_project_structure():
        print("\n❌ 專案結構檢查失敗，請確認在正確的專案目錄中執行")
        sys.exit(1)
    
    # 詢問是否安裝依賴
    install_deps = input("\n❓ 是否需要安裝/更新依賴套件？(y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("\n❌ 依賴安裝失敗")
            sys.exit(1)
    
    # 啟動系統
    print("\n" + "=" * 50)
    if start_integrated_system():
        print("\n🎉 系統啟動成功！")
    else:
        print("\n❌ 系統啟動失敗")
        sys.exit(1)

if __name__ == "__main__":
    main()

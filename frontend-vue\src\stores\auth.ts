/**
 * 認證狀態管理
 */
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import type { User, LoginCredentials, SSOLoginRequest, AuthState } from '@/types/auth'
import { ElMessage } from 'element-plus'

const TOKEN_KEY = 'access_token'
const USER_KEY = 'user_info'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: localStorage.getItem(TOKEN_KEY),
    isAuthenticated: false,
    permissions: [],
    loading: false
  }),
  
  getters: {
    userRole: (state) => state.user?.role,
    userName: (state) => state.user?.full_name || state.user?.username,
    userDepartment: (state) => state.user?.department,
    
    // 權限檢查
    hasPermission: (state) => (permission: string) => {
      if (state.user?.role === 'admin') return true
      return state.permissions.includes(permission)
    },
    
    // 角色檢查
    hasRole: (state) => (role: string) => {
      return state.user?.role === role
    },
    
    // 是否為管理員
    isAdmin: (state) => state.user?.role === 'admin',
    
    // 是否為全域用戶
    isGlobal: (state) => state.user?.role === 'global',
    
    // 是否為一般用戶
    isUser: (state) => state.user?.role === 'user'
  },
  
  actions: {
    /**
     * 初始化認證狀態
     */
    async initAuth() {
      const token = localStorage.getItem(TOKEN_KEY)
      const userInfo = localStorage.getItem(USER_KEY)
      
      if (token && userInfo) {
        try {
          this.token = token
          this.user = JSON.parse(userInfo)
          this.permissions = this.user?.permissions || []
          this.isAuthenticated = true
          
          // 驗證token是否仍然有效
          await this.getCurrentUser()
        } catch (error) {
          console.error('初始化認證狀態失敗:', error)
          this.logout()
        }
      }
    },
    
    /**
     * 用戶登入
     */
    async login(credentials: LoginCredentials) {
      try {
        this.loading = true
        const response = await authApi.login(credentials)
        
        if (response.success) {
          const { access_token, user } = response.data
          
          this.setAuthData(access_token, user)
          ElMessage.success('登入成功')
          
          return { success: true }
        } else {
          throw new Error(response.message || '登入失敗')
        }
      } catch (error: any) {
        console.error('登入失敗:', error)
        ElMessage.error(error.message || '登入失敗')
        return { success: false, error: error.message }
      } finally {
        this.loading = false
      }
    },
    
    /**
     * SSO登入
     */
    async ssoLogin(request: SSOLoginRequest) {
      try {
        this.loading = true
        const response = await authApi.ssoLogin(request)
        
        if (response.success) {
          const { access_token, user } = response.data
          
          this.setAuthData(access_token, user)
          ElMessage.success('SSO登入成功')
          
          return { success: true }
        } else {
          throw new Error(response.message || 'SSO登入失敗')
        }
      } catch (error: any) {
        console.error('SSO登入失敗:', error)
        ElMessage.error(error.message || 'SSO登入失敗')
        return { success: false, error: error.message }
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 用戶登出
     */
    async logout() {
      try {
        // 呼叫後端登出API
        await authApi.logout()
      } catch (error) {
        console.error('登出API呼叫失敗:', error)
      } finally {
        // 無論API是否成功，都清除本地狀態
        this.clearAuthData()
        ElMessage.success('已登出')
      }
    },
    
    /**
     * 獲取當前用戶資訊
     */
    async getCurrentUser() {
      try {
        const response = await authApi.getCurrentUser()
        
        if (response.success) {
          this.user = response.data
          this.permissions = response.data.permissions || []
          this.isAuthenticated = true
          
          // 更新本地存儲
          localStorage.setItem(USER_KEY, JSON.stringify(response.data))
        }
      } catch (error) {
        console.error('獲取用戶資訊失敗:', error)
        this.logout()
        throw error
      }
    },
    
    /**
     * 更新用戶資料
     */
    async updateProfile(data: Partial<User>) {
      try {
        const response = await authApi.updateProfile(data)
        
        if (response.success) {
          this.user = response.data
          localStorage.setItem(USER_KEY, JSON.stringify(response.data))
          ElMessage.success('資料更新成功')
          return { success: true }
        }
      } catch (error: any) {
        console.error('更新用戶資料失敗:', error)
        ElMessage.error(error.message || '資料更新失敗')
        return { success: false, error: error.message }
      }
    },
    
    /**
     * 修改密碼
     */
    async changePassword(data: {
      current_password: string
      new_password: string
      confirm_password: string
    }) {
      try {
        const response = await authApi.changePassword(data)
        
        if (response.success) {
          ElMessage.success('密碼修改成功')
          return { success: true }
        }
      } catch (error: any) {
        console.error('修改密碼失敗:', error)
        ElMessage.error(error.message || '密碼修改失敗')
        return { success: false, error: error.message }
      }
    },
    
    /**
     * 設定認證資料
     */
    setAuthData(token: string, user: User) {
      this.token = token
      this.user = user
      this.permissions = user.permissions || []
      this.isAuthenticated = true
      
      // 存儲到本地
      localStorage.setItem(TOKEN_KEY, token)
      localStorage.setItem(USER_KEY, JSON.stringify(user))
    },
    
    /**
     * 清除認證資料
     */
    clearAuthData() {
      this.token = null
      this.user = null
      this.permissions = []
      this.isAuthenticated = false
      
      // 清除本地存儲
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(USER_KEY)
    },
    
    /**
     * 檢查權限
     */
    checkPermission(permission: string): boolean {
      if (this.user?.role === 'admin') return true
      return this.permissions.includes(permission)
    }
  }
})

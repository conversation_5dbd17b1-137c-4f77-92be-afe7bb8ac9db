/*
 * CBA受款人資料搜集系統 - 主要JavaScript文件
 * 提供互動功能和用戶體驗增強
 */

// 等待DOM載入完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 CBA系統已載入');
    
    // 初始化所有功能
    initializeApp();
});

/**
 * 初始化應用程式
 */
function initializeApp() {
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化表單驗證
    initializeFormValidation();
    
    // 初始化動畫效果
    initializeAnimations();
    
    // 初始化導航功能
    initializeNavigation();
    
    console.log('✅ 所有功能已初始化');
}

/**
 * 初始化Bootstrap工具提示
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化表單驗證
 */
function initializeFormValidation() {
    // 獲取所有需要驗證的表單
    const forms = document.querySelectorAll('.needs-validation');
    
    // 為每個表單添加驗證事件
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 初始化動畫效果
 */
function initializeAnimations() {
    // 為卡片添加進入動畫
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('card-enter');
    });
    
    // 為按鈕添加點擊效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // 創建漣漪效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            // 移除漣漪效果
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 初始化導航功能
 */
function initializeNavigation() {
    // 高亮當前頁面的導航項目
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
        }
    });
}

/**
 * 顯示成功訊息
 */
function showSuccessMessage(message) {
    showToast(message, 'success');
}

/**
 * 顯示錯誤訊息
 */
function showErrorMessage(message) {
    showToast(message, 'error');
}

/**
 * 顯示資訊訊息
 */
function showInfoMessage(message) {
    showToast(message, 'info');
}

/**
 * 顯示Toast訊息
 */
function showToast(message, type = 'info') {
    // 創建toast容器（如果不存在）
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }
    
    // 創建toast元素
    const toastId = 'toast-' + Date.now();
    const iconClass = {
        'success': 'bi-check-circle-fill text-success',
        'error': 'bi-exclamation-triangle-fill text-danger',
        'warning': 'bi-exclamation-triangle-fill text-warning',
        'info': 'bi-info-circle-fill text-info'
    }[type] || 'bi-info-circle-fill text-info';
    
    const toastHTML = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi ${iconClass} me-2"></i>
                <strong class="me-auto">系統訊息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="關閉"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    // 顯示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });
    
    toast.show();
    
    // 自動移除toast元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * 確認對話框
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 格式化數字
 */
function formatNumber(num) {
    return new Intl.NumberFormat('zh-TW').format(num);
}

/**
 * 格式化日期
 */
function formatDate(date) {
    return new Intl.DateTimeFormat('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

/**
 * 防抖函數
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 節流函數
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 全域錯誤處理
window.addEventListener('error', function(e) {
    console.error('JavaScript錯誤:', e.error);
    showErrorMessage('發生未預期的錯誤，請重新整理頁面');
});

// 全域未處理的Promise拒絕
window.addEventListener('unhandledrejection', function(e) {
    console.error('未處理的Promise拒絕:', e.reason);
    showErrorMessage('操作失敗，請稍後再試');
});

// 導出常用函數到全域
window.CBA = {
    showSuccessMessage,
    showErrorMessage,
    showInfoMessage,
    confirmAction,
    formatNumber,
    formatDate,
    debounce,
    throttle
};

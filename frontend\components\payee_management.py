"""
優化的受款人管理介面 - 簡便易用的設計
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date
from typing import Dict, Any, List, Optional
import io
import sys
import os
import pytz
import re

# 設定台北時區
taipei_tz = pytz.timezone('Asia/Taipei')

# 將父目錄加入sys.path以便導入utils模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from utils.api_client import api_client
    from utils.auth_manager import AuthManager
except ImportError:
    # 如果導入失敗，建立模擬物件
    class MockApiClient:
        def get_payee_data_list(self, *args, **kwargs):
            return {"items": [], "total": 0, "page": 1, "pages": 1}
        def create_payee_data(self, *args, **kwargs):
            return {"id": 1, "message": "成功"}
        def update_payee_data(self, *args, **kwargs):
            return {"id": 1, "message": "成功"}
        def delete_payee_data(self, *args, **kwargs):
            return {"success": True}
        def validate_id_number(self, *args, **kwargs):
            return {"valid": True, "exists": False}
    
    api_client = MockApiClient()
    
    class MockAuthManager:
        @staticmethod
        def require_authentication():
            pass
        @staticmethod
        def can_create_data():
            return True
        @staticmethod
        def can_read_all_data():
            return True
        @staticmethod
        def is_admin():
            return True
        @staticmethod
        def get_current_user():
            return {"username": "test", "full_name": "測試用戶", "department": "測試部門"}
    
    AuthManager = MockAuthManager()

def validate_id_number(id_number: str) -> tuple[bool, str]:
    """驗證身分證字號格式"""
    if not id_number:
        return False, "身分證字號為必填欄位"
    
    id_number = id_number.strip().upper()
    
    if len(id_number) != 10:
        return False, "身分證字號必須為10位"
    
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False, "格式錯誤：應為1位英文字母+9位數字"
    
    # 身分證字號檢核邏輯
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17,
        'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22, 'O': 35, 'P': 23,
        'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28, 'V': 29, 'W': 32, 'X': 30,
        'Y': 31, 'Z': 33
    }
    
    first_letter = id_number[0]
    if first_letter not in letter_values:
        return False, "首位字母無效"
    
    # 計算檢核碼
    letter_value = letter_values[first_letter]
    total = (letter_value // 10) + (letter_value % 10) * 9

    for i, digit in enumerate(id_number[1:9]):
        total += int(digit) * (8 - i)

    total += int(id_number[9])

    if total % 10 != 0:
        return False, "身分證字號檢核碼錯誤"
    
    return True, ""

def render_payee_management_page():
    """渲染優化的受款人管理頁面"""
    AuthManager.require_authentication()
    
    st.title("💰 受款人管理")
    
    # 初始化會話狀態
    if 'current_tab' not in st.session_state:
        st.session_state.current_tab = "list"
    if 'selected_record' not in st.session_state:
        st.session_state.selected_record = None
    if 'search_results' not in st.session_state:
        st.session_state.search_results = None
    
    # 頂部操作欄
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
    
    with col1:
        # 快速搜尋
        search_term = st.text_input(
            "🔍 快速搜尋",
            placeholder="輸入姓名、身分證字號或地址關鍵字...",
            key="quick_search"
        )
    
    with col2:
        if st.button("🔍 搜尋", use_container_width=True, type="primary"):
            if search_term:
                perform_search(search_term)
            else:
                st.session_state.search_results = None
                st.rerun()
    
    with col3:
        if st.button("➕ 新增", use_container_width=True):
            st.session_state.current_tab = "create"
            st.session_state.selected_record = None
            st.rerun()
    
    with col4:
        if st.button("🔄 重新整理", use_container_width=True):
            st.session_state.search_results = None
            st.session_state.selected_record = None
            st.rerun()
    
    st.divider()
    
    # 主要內容區域
    if st.session_state.current_tab == "create":
        render_create_form()
    elif st.session_state.current_tab == "edit":
        render_edit_form()
    else:
        render_data_list()

def perform_search(search_term: str):
    """執行搜尋"""
    try:
        with st.spinner("搜尋中..."):
            result = api_client.get_payee_data_list(
                search=search_term,
                page=1,
                size=50
            )
            st.session_state.search_results = result
            st.success(f"找到 {result.get('total', 0)} 筆資料")
    except Exception as e:
        st.error(f"搜尋失敗：{str(e)}")

def render_data_list():
    """渲染資料列表"""
    st.subheader("📋 受款人資料列表")
    
    # 取得資料
    try:
        if st.session_state.search_results:
            result = st.session_state.search_results
        else:
            result = api_client.get_payee_data_list(page=1, size=20)
        
        data = result.get("items", [])
        total = result.get("total", 0)
        
        if not data:
            st.info("📄 目前沒有資料，點擊「新增」按鈕開始建立受款人資料")
            return
        
        st.write(f"共 {total} 筆資料")
        
        # 資料卡片顯示
        for item in data:
            render_data_card(item)
            
    except Exception as e:
        st.error(f"載入資料失敗：{str(e)}")

def render_data_card(item: Dict[str, Any]):
    """渲染資料卡片"""
    with st.container():
        col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
        
        with col1:
            st.write(f"**👤 {item.get('name', '未知')}**")
            st.write(f"🆔 {item.get('id_number_masked', '***')}")
        
        with col2:
            st.write(f"📍 {item.get('address', '')[:30]}{'...' if len(item.get('address', '')) > 30 else ''}")
            if item.get('notes'):
                st.write(f"📝 {item.get('notes', '')[:20]}{'...' if len(item.get('notes', '')) > 20 else ''}")
        
        with col3:
            st.write(f"👤 {item.get('created_by', '')}")
            created_at = item.get('created_at', '')
            if created_at:
                try:
                    if 'T' in created_at and ('+' in created_at or 'Z' in created_at):
                        timestamp = datetime.fromisoformat(created_at.replace('Z', '+00:00')).astimezone(taipei_tz)
                    elif 'T' in created_at:
                        timestamp = datetime.fromisoformat(created_at).replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                    else:
                        timestamp = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                    st.write(f"🕒 {timestamp.strftime('%Y-%m-%d %H:%M')}")
                except:
                    st.write(f"🕒 {created_at}")
        
        with col4:
            if st.button("✏️", key=f"edit_{item['id']}", help="編輯"):
                st.session_state.current_tab = "edit"
                st.session_state.selected_record = item
                st.rerun()
            
            if AuthManager.is_admin():
                if st.button("🗑️", key=f"delete_{item['id']}", help="刪除", type="secondary"):
                    delete_record(item['id'], item['name'])
        
        st.divider()

def render_create_form():
    """渲染新增表單"""
    st.subheader("➕ 新增受款人資料")
    
    # 返回按鈕
    if st.button("← 返回列表"):
        st.session_state.current_tab = "list"
        st.rerun()
    
    with st.form("create_payee_form"):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            name = st.text_input(
                "姓名 *",
                placeholder="請輸入真實姓名",
                max_chars=10
            )
            
            id_number = st.text_input(
                "身分證字號 *",
                placeholder="A123456789",
                max_chars=10
            )
            
            address = st.text_area(
                "地址 *",
                placeholder="請輸入詳細地址",
                height=100,
                max_chars=200
            )
            
            notes = st.text_area(
                "備註",
                placeholder="其他相關資訊（選填）",
                height=80,
                max_chars=500
            )
        
        with col2:
            st.info("""
            **填寫說明**
            
            • 姓名請與身分證相符
            • 身分證字號將加密儲存
            • 地址建議填寫完整
            • 備註欄位為選填項目
            """)
            
            current_user = AuthManager.get_current_user()
            if current_user:
                st.write("**建立者**")
                st.write(f"👤 {current_user.get('full_name', '')}")
                st.write(f"🏢 {current_user.get('department', '')}")
        
        # 提交按鈕
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            submitted = st.form_submit_button("💾 儲存", use_container_width=True, type="primary")
        
        if submitted:
            create_payee_data(name, id_number, address, notes)

def render_edit_form():
    """渲染編輯表單"""
    if not st.session_state.selected_record:
        st.error("未選擇要編輯的記錄")
        return
    
    record = st.session_state.selected_record
    st.subheader(f"✏️ 編輯受款人資料 - {record.get('name', '')}")
    
    # 返回按鈕
    if st.button("← 返回列表"):
        st.session_state.current_tab = "list"
        st.session_state.selected_record = None
        st.rerun()
    
    with st.form("edit_payee_form"):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            name = st.text_input(
                "姓名 *",
                value=record.get('name', ''),
                max_chars=10
            )
            
            # 身分證字號顯示但不可編輯
            st.text_input(
                "身分證字號",
                value=record.get('id_number_masked', ''),
                disabled=True,
                help="身分證字號不可修改"
            )
            
            address = st.text_area(
                "地址 *",
                value=record.get('address', ''),
                height=100,
                max_chars=200
            )
            
            notes = st.text_area(
                "備註",
                value=record.get('notes', ''),
                height=80,
                max_chars=500
            )
        
        with col2:
            st.info("""
            **編輯說明**
            
            • 身分證字號不可修改
            • 其他欄位可以更新
            • 修改後請點擊儲存
            """)
            
            st.write("**原始資料**")
            st.write(f"👤 建立者：{record.get('created_by', '')}")
            created_at = record.get('created_at', '')
            if created_at:
                st.write(f"🕒 建立時間：{created_at}")
        
        # 提交按鈕
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            submitted = st.form_submit_button("💾 更新", use_container_width=True, type="primary")
        
        if submitted:
            update_payee_data(record['id'], name, address, notes)

def create_payee_data(name: str, id_number: str, address: str, notes: str):
    """建立受款人資料"""
    # 驗證輸入
    if not name.strip():
        st.error("請輸入姓名")
        return
    
    if not id_number.strip():
        st.error("請輸入身分證字號")
        return
    
    if not address.strip():
        st.error("請輸入地址")
        return
    
    # 驗證身分證字號
    is_valid, error_msg = validate_id_number(id_number.strip())
    if not is_valid:
        st.error(f"身分證字號錯誤：{error_msg}")
        return
    
    try:
        # 檢查身分證字號是否已存在
        validation_result = api_client.validate_id_number(id_number.strip())
        if validation_result.get("exists", False):
            st.error("此身分證字號已存在於系統中")
            return
        
        # 建立資料
        data = {
            "name": name.strip(),
            "id_number": id_number.strip().upper(),
            "address": address.strip(),
            "notes": notes.strip() if notes else ""
        }
        
        with st.spinner("儲存中..."):
            result = api_client.create_payee_data(data)
            
        st.success("✅ 受款人資料已成功建立！")
        st.session_state.current_tab = "list"
        st.session_state.search_results = None
        st.rerun()
        
    except Exception as e:
        st.error(f"建立失敗：{str(e)}")

def update_payee_data(data_id: int, name: str, address: str, notes: str):
    """更新受款人資料"""
    # 驗證輸入
    if not name.strip():
        st.error("請輸入姓名")
        return
    
    if not address.strip():
        st.error("請輸入地址")
        return
    
    try:
        data = {
            "name": name.strip(),
            "address": address.strip(),
            "notes": notes.strip() if notes else ""
        }
        
        with st.spinner("更新中..."):
            result = api_client.update_payee_data(data_id, data)
            
        st.success("✅ 受款人資料已成功更新！")
        st.session_state.current_tab = "list"
        st.session_state.selected_record = None
        st.session_state.search_results = None
        st.rerun()
        
    except Exception as e:
        st.error(f"更新失敗：{str(e)}")

def delete_record(data_id: int, name: str):
    """刪除記錄"""
    if st.session_state.get(f'confirm_delete_{data_id}', False):
        try:
            with st.spinner("刪除中..."):
                api_client.delete_payee_data(data_id)
            st.success(f"✅ 已刪除 {name} 的資料")
            st.session_state.search_results = None
            del st.session_state[f'confirm_delete_{data_id}']
            st.rerun()
        except Exception as e:
            st.error(f"刪除失敗：{str(e)}")
    else:
        st.warning(f"⚠️ 確定要刪除 {name} 的資料嗎？此操作無法復原！")
        if st.button(f"確認刪除 {name}", key=f"confirm_delete_btn_{data_id}", type="secondary"):
            st.session_state[f'confirm_delete_{data_id}'] = True
            st.rerun()

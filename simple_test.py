import re

def validate_id_number(id_number: str) -> bool:
    if not id_number:
        return False
    
    id_number = id_number.strip().upper()
    
    if not re.match(r'^[A-Z][0-9]{9}$', id_number):
        return False
    
    letter_values = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
        'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
        'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
        'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    }
    
    letter = id_number[0]
    if letter not in letter_values:
        return False
    
    letter_value = letter_values[letter]
    checksum = (letter_value // 10) * 1 + (letter_value % 10) * 9
    
    for i in range(1, 9):
        checksum += int(id_number[i]) * (9 - i)
    
    checksum += int(id_number[9])
    
    return checksum % 10 == 0

# 測試
print("身分證字號驗證測試")
print("=" * 30)

# 測試一些常見的身分證字號
test_ids = [
    "A123456789",
    "B234567890",
    "C345678901",
    "D456789012",
    "E567890123",
    "F678901234",
    "G789012345",
    "H890123456",
    "J901234567",
    "K012345678",
    "L101234567",
    "M201234567",
    "N301234567",
    "P401234567",
    "Q501234567",
    "R601234567",
    "S701234567",
    "T801234567",
    "U901234567",
    "V012345678",
    "W123456789",
    "X234567890",
    "Y345678901",
    "Z456789012"
]

for id_num in test_ids:
    result = validate_id_number(id_num)
    print(f"{id_num}: {'OK' if result else 'FAIL'}")

print("\n無效格式測試:")
invalid_tests = [
    "",
    "A12345678",
    "A1234567890",
    "123456789A",
    "a123456789",
    "A123-45678",
    "A123 45678"
]

for id_num in invalid_tests:
    result = validate_id_number(id_num)
    print(f"{id_num!r}: {'FAIL' if not result else 'UNEXPECTED PASS'}")

print("\n測試完成!") 
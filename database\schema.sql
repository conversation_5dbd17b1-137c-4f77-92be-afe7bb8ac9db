-- CBA受款人資料搜集系統資料庫表結構
-- 建立日期：2024年12月19日

-- 啟用外鍵約束
PRAGMA foreign_keys = ON;

-- 部門表
CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 用戶表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    hashed_password TEXT NOT NULL, -- 雜湊後的密碼
    full_name TEXT NOT NULL,
    department_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (department_id) REFERENCES departments(id)
);

-- 權限表
CREATE TABLE IF NOT EXISTS permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    category TEXT, -- 權限分類
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT, -- JSON格式存儲權限清單（向後兼容）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 角色權限關聯表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INTEGER,
    permission_id INTEGER,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 用戶角色關聯表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INTEGER,
    role_id INTEGER,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- 受款人資料表
CREATE TABLE IF NOT EXISTS payee_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    encrypted_id_number TEXT NOT NULL UNIQUE, -- 加密後的身分證字號
    address TEXT,
    notes TEXT,
    created_by_user_id INTEGER NOT NULL, -- 建立者用戶ID
    created_by_department INTEGER NOT NULL, -- 建立者所屬部門
    is_active BOOLEAN DEFAULT TRUE, -- 軟刪除標記
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    FOREIGN KEY (created_by_department) REFERENCES departments(id)
);

-- 審計日誌表
CREATE TABLE IF NOT EXISTS audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    table_name TEXT,
    record_id INTEGER,
    old_values TEXT, -- JSON格式
    new_values TEXT, -- JSON格式
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 建立索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_payee_data_created_by ON payee_data(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_payee_data_department ON payee_data(created_by_department);
CREATE INDEX IF NOT EXISTS idx_payee_data_created_at ON payee_data(created_at);
CREATE INDEX IF NOT EXISTS idx_payee_data_is_active ON payee_data(is_active);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON audit_log(table_name, record_id);

-- 插入預設權限資料
INSERT OR IGNORE INTO permissions (name, description, category) VALUES 
('CREATE_PAYEE_DATA', '新增受款人資料', 'payee_data'),
('READ_PAYEE_DATA', '查詢受款人資料', 'payee_data'),
('UPDATE_PAYEE_DATA', '修改受款人資料', 'payee_data'),
('DELETE_PAYEE_DATA', '刪除受款人資料', 'payee_data'),
('READ_ALL_DATA', '查詢所有資料', 'global'),
('EXPORT_DATA', '匯出資料', 'global'),
('MANAGE_ADMIN', '管理管理者帳號', 'admin'),
('MANAGE_GLOBAL', '管理全域帳號', 'admin'),
('MANAGE_USERS', '管理用戶', 'admin'),
('VIEW_AUDIT_LOG', '查看審計日誌', 'admin');

-- 插入預設角色資料
INSERT OR IGNORE INTO roles (name, description, permissions) VALUES 
('general', '一般帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA"]'),
('global', '全域帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA"]'),
('admin', '管理者帳號', '["CREATE_PAYEE_DATA", "READ_PAYEE_DATA", "UPDATE_PAYEE_DATA", "DELETE_PAYEE_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_ADMIN", "MANAGE_GLOBAL", "MANAGE_USERS", "VIEW_AUDIT_LOG"]');

-- 插入預設部門資料
INSERT OR IGNORE INTO departments (name, description) VALUES 
('資訊部', '負責系統維護與開發'),
('人事部', '負責人員管理'),
('行政部', '負責行政事務');

-- 建立觸發器以自動更新 updated_at 欄位
-- users表觸發器
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- departments表觸發器
CREATE TRIGGER IF NOT EXISTS update_departments_timestamp 
    AFTER UPDATE ON departments
    FOR EACH ROW
BEGIN
    UPDATE departments SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- payee_data表觸發器
CREATE TRIGGER IF NOT EXISTS update_payee_data_timestamp 
    AFTER UPDATE ON payee_data
    FOR EACH ROW
BEGIN
    UPDATE payee_data SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END; 
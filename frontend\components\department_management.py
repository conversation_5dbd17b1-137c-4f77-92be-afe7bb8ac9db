import streamlit as st
import pandas as pd
from utils.api_client import api_client
from utils.auth_manager import AuthManager
import time

def render_department_management_page():
    """渲染部門管理頁面"""
    # 檢查認證和權限
    AuthManager.require_authentication()
    
    if not AuthManager.is_admin():
        st.error("您沒有權限管理部門")
        return
    
    st.title("🏢 部門管理")
    
    # 建立頁籤，分離不同功能
    tab1, tab2, tab3 = st.tabs(["部門列表", "新增部門", "編輯部門"])
    
    with tab1:
        render_department_list()
    
    with tab2:
        render_add_department_form()
    
    with tab3:
        render_edit_department_form()

def render_department_list():
    """渲染部門列表"""
    st.subheader("📋 部門列表")
    
    # 搜尋和篩選
    search_term = st.text_input("搜尋部門", placeholder="輸入部門名稱或代碼")
    
    # 取得部門列表
    try:
        departments = api_client.get_departments(search=search_term)
        
        if not departments:
            st.info("暫無部門資料")
            return
        
        # 轉換為DataFrame以便顯示
        df_data = []
        for dept in departments:
            df_data.append({
                "部門ID": dept.get("id"),
                "部門代碼": dept.get("code"),
                "部門名稱": dept.get("name"),
                "上級部門": dept.get("parent_name", "-"),
                "建立時間": dept.get("created_at"),
                "狀態": "啟用" if dept.get("is_active") else "停用"
            })
        
        df = pd.DataFrame(df_data)
        
        # 顯示部門表格
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "部門ID": st.column_config.NumberColumn("部門ID", width="small"),
                "部門代碼": st.column_config.TextColumn("部門代碼", width="medium"),
                "部門名稱": st.column_config.TextColumn("部門名稱", width="medium"),
                "上級部門": st.column_config.TextColumn("上級部門", width="medium"),
                "建立時間": st.column_config.DatetimeColumn("建立時間", width="medium"),
                "狀態": st.column_config.TextColumn("狀態", width="small")
            }
        )
        
        # 刪除部門功能
        st.divider()
        delete_col1, delete_col2 = st.columns([3, 1])
        
        with delete_col1:
            dept_to_delete = st.selectbox(
                "選擇要刪除的部門",
                options=[0] + [dept["id"] for dept in departments],
                format_func=lambda x: "請選擇..." if x == 0 else f"{next((dept['name'] for dept in departments if dept['id'] == x), '')}"
            )
        
        with delete_col2:
            if dept_to_delete != 0:
                if st.button("🗑️ 刪除部門", use_container_width=True, type="secondary"):
                    try:
                        api_client.delete_department(dept_to_delete)
                        st.success("部門已成功刪除")
                        time.sleep(1)
                        st.rerun()
                    except Exception as e:
                        st.error(f"刪除失敗: {str(e)}")
    
    except Exception as e:
        st.error(f"載入部門資料失敗: {str(e)}")

def render_add_department_form():
    """渲染新增部門表單"""
    st.subheader("➕ 新增部門")
    
    with st.form("add_department_form"):
        code = st.text_input("部門代碼 *", placeholder="例如: IT001")
        name = st.text_input("部門名稱 *", placeholder="例如: 資訊部")
        
        # 取得現有部門作為上級部門選項
        try:
            departments = api_client.get_departments()
            parent_options = [{"id": 0, "name": "無上級部門"}] + departments
            parent_id = st.selectbox(
                "上級部門",
                options=[dept["id"] for dept in parent_options],
                format_func=lambda x: next((dept["name"] for dept in parent_options if dept["id"] == x), "")
            )
            
            # 如果選擇"無上級部門"，則設為None
            if parent_id == 0:
                parent_id = None
        except:
            st.warning("無法載入上級部門選項")
            parent_id = None
        
        submit = st.form_submit_button("✅ 新增部門", use_container_width=True, type="primary")
        
        if submit:
            if not code or not name:
                st.error("請填寫所有必填欄位")
            else:
                try:
                    # 準備部門資料
                    dept_data = {
                        "code": code,
                        "name": name,
                        "parent_id": parent_id
                    }
                    
                    # 呼叫API新增部門
                    api_client.create_department(dept_data)
                    st.success("✅ 部門已成功新增！")
                    time.sleep(1)
                    st.rerun()
                except Exception as e:
                    st.error(f"新增失敗: {str(e)}")

def render_edit_department_form():
    """渲染編輯部門表單"""
    st.subheader("✏️ 編輯部門")
    
    # 取得部門列表
    try:
        departments = api_client.get_departments()
        
        if not departments:
            st.info("暫無部門資料可編輯")
            return
        
        # 選擇要編輯的部門
        dept_to_edit = st.selectbox(
            "選擇要編輯的部門",
            options=[0] + [dept["id"] for dept in departments],
            format_func=lambda x: "請選擇..." if x == 0 else f"{next((dept['name'] for dept in departments if dept['id'] == x), '')}"
        )
        
        if dept_to_edit != 0:
            # 取得選定的部門資料
            selected_dept = next((dept for dept in departments if dept["id"] == dept_to_edit), None)
            
            if selected_dept:
                with st.form("edit_department_form"):
                    code = st.text_input("部門代碼 *", value=selected_dept.get("code", ""))
                    name = st.text_input("部門名稱 *", value=selected_dept.get("name", ""))
                    
                    # 上級部門選項
                    parent_options = [{"id": 0, "name": "無上級部門"}] + [d for d in departments if d["id"] != dept_to_edit]
                    current_parent_id = selected_dept.get("parent_id", 0) or 0
                    
                    parent_id = st.selectbox(
                        "上級部門",
                        options=[dept["id"] for dept in parent_options],
                        index=next((i for i, dept in enumerate(parent_options) if dept["id"] == current_parent_id), 0),
                        format_func=lambda x: next((dept["name"] for dept in parent_options if dept["id"] == x), "")
                    )
                    
                    # 如果選擇"無上級部門"，則設為None
                    if parent_id == 0:
                        parent_id = None
                    
                    is_active = st.checkbox("啟用", value=selected_dept.get("is_active", True))
                    
                    submit = st.form_submit_button("💾 儲存變更", use_container_width=True, type="primary")
                    
                    if submit:
                        if not code or not name:
                            st.error("請填寫所有必填欄位")
                        else:
                            try:
                                # 準備更新資料
                                update_data = {
                                    "code": code,
                                    "name": name,
                                    "parent_id": parent_id,
                                    "is_active": is_active
                                }
                                
                                # 呼叫API更新部門
                                api_client.update_department(dept_to_edit, update_data)
                                st.success("✅ 部門資料已成功更新！")
                                time.sleep(1)
                                st.rerun()
                            except Exception as e:
                                st.error(f"更新失敗: {str(e)}")
    
    except Exception as e:
        st.error(f"載入部門資料失敗: {str(e)}")
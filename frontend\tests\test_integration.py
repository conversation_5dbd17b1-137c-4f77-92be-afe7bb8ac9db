"""
整合測試 - 端到端功能測試
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import json

# 確保可以導入模組
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestEndToEndWorkflow:
    """端到端工作流程測試"""
    
    @patch('streamlit.session_state')
    @patch('requests.post')
    @patch('requests.get')
    def test_complete_data_entry_workflow(self, mock_get, mock_post, mock_session_state, 
                                        sample_user_general):
        """測試完整的資料新增流程"""
        # 設定用戶會話
        mock_session_state.logged_in = True
        mock_session_state.user_info = sample_user_general
        mock_session_state.access_token = "fake-token"
        
        # 模擬新增資料API回應
        mock_post_response = Mock()
        mock_post_response.status_code = 201
        mock_post_response.json.return_value = {"message": "新增成功", "id": 1}
        mock_post.return_value = mock_post_response
        
        # 模擬查詢資料API回應
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            "data": [{
                "id": 1,
                "name": "張三",
                "id_number_masked": "A123****89",
                "address": "台北市信義區信義路100號",
                "notes": "測試資料",
                "created_by": "general_user",
                "created_at": "2024-12-19 10:30:00"
            }],
            "total": 1,
            "page": 1,
            "per_page": 10
        }
        mock_get.return_value = mock_get_response
        
        # 測試工作流程
        from components.personal_data_form import validate_id_number
        from components.data_query import filter_data_by_permission
        
        # 1. 驗證身分證字號
        is_valid, error_msg = validate_id_number("A123456789")
        assert is_valid == True
        assert error_msg == ""
        
        # 2. 模擬提交資料
        form_data = {
            "name": "張三",
            "id_number": "A123456789",
            "address": "台北市信義區信義路100號",
            "notes": "測試資料"
        }
        
        # 驗證API被正確調用
        expected_headers = {
            "Authorization": "Bearer fake-token",
            "Content-Type": "application/json"
        }
        
        # 3. 驗證查詢流程
        # 一般用戶只能看到自己的資料
        user_data = filter_data_by_permission(
            mock_get_response.json()["data"], 
            sample_user_general
        )
        assert len(user_data) == 1
        assert user_data[0]["created_by"] == "general_user"
    
    @patch('streamlit.session_state')
    @patch('requests.get')
    def test_role_based_data_access_workflow(self, mock_get, mock_session_state, 
                                           sample_user_admin, sample_personal_data):
        """測試基於角色的資料存取工作流程"""
        # 設定管理者會話
        mock_session_state.logged_in = True
        mock_session_state.user_info = sample_user_admin
        mock_session_state.access_token = "admin-token"
        
        # 模擬API回應
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": sample_personal_data,
            "total": 2,
            "page": 1,
            "per_page": 10
        }
        mock_get.return_value = mock_response
        
        # 模擬權限篩選函數
        def filter_data_by_permission(data, user_info):
            user_roles = user_info.get('roles', [])
            username = user_info.get('username', '')
            
            if 'admin' not in user_roles and 'global_user' not in user_roles:
                return [item for item in data if item['created_by'] == username]
            return data
        
        # 管理者可以看到所有資料
        admin_data = filter_data_by_permission(
            sample_personal_data, 
            sample_user_admin
        )
        assert len(admin_data) == 2  # 可以看到所有資料
        
        # 驗證資料遮罩
        for item in admin_data:
            assert "****" in item["id_number_masked"]
            assert len(item["id_number_masked"]) == 10
    
    @patch('streamlit.session_state')
    def test_permission_enforcement_workflow(self, mock_session_state, 
                                           sample_user_general):
        """測試權限執行工作流程"""
        # 設定一般用戶會話
        mock_session_state.logged_in = True
        mock_session_state.user_info = sample_user_general
        
        from main import has_permission, has_role
        
        # 測試頁面存取權限
        assert has_permission("CREATE_PERSONAL_DATA") == True
        assert has_permission("READ_OWN_DATA") == True
        assert has_permission("READ_ALL_DATA") == False
        assert has_permission("MANAGE_USERS") == False
        
        # 測試角色檢查
        assert has_role("general_user") == True
        assert has_role("admin") == False
        
        # 模擬頁面導航邏輯
        available_pages = []
        if has_permission("CREATE_PERSONAL_DATA"):
            available_pages.append("新增資料")
        if has_permission("READ_ALL_DATA"):
            available_pages.append("統計報表")
        if has_permission("MANAGE_USERS"):
            available_pages.append("用戶管理")
        
        # 一般用戶只能看到新增資料頁面
        assert "新增資料" in available_pages
        assert "統計報表" not in available_pages
        assert "用戶管理" not in available_pages

class TestAuthenticationFlow:
    """認證流程測試"""
    
    @patch('streamlit.session_state')
    def test_login_workflow(self, mock_session_state):
        """測試登入工作流程"""
        # 初始狀態 - 未登入
        mock_session_state.logged_in = False
        mock_session_state.user_info = None
        mock_session_state.access_token = None
        
        # 模擬登入成功
        def simulate_login(username, role):
            mock_session_state.logged_in = True
            mock_session_state.user_info = {
                "username": username,
                "roles": [role],
                "permissions": get_permissions_by_role(role)
            }
            mock_session_state.access_token = f"token-{username}"
            return True
        
        def get_permissions_by_role(role):
            role_permissions = {
                "general_user": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA"],
                "global_user": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA"],
                "admin": ["CREATE_PERSONAL_DATA", "READ_OWN_DATA", "READ_ALL_DATA", "EXPORT_DATA", "MANAGE_USERS"]
            }
            return role_permissions.get(role, [])
        
        # 測試登入流程
        login_success = simulate_login("test_user", "general_user")
        assert login_success == True
        assert mock_session_state.logged_in == True
        assert mock_session_state.user_info["username"] == "test_user"
        assert "CREATE_PERSONAL_DATA" in mock_session_state.user_info["permissions"]
    
    @patch('streamlit.session_state')
    def test_logout_workflow(self, mock_session_state):
        """測試登出工作流程"""
        # 設定登入狀態
        mock_session_state.logged_in = True
        mock_session_state.user_info = {"username": "test_user"}
        mock_session_state.access_token = "test-token"
        
        # 模擬登出
        def simulate_logout():
            mock_session_state.logged_in = False
            mock_session_state.user_info = None
            mock_session_state.access_token = None
            mock_session_state.current_page = "登入"
        
        # 執行登出
        simulate_logout()
        
        # 驗證登出狀態
        assert mock_session_state.logged_in == False
        assert mock_session_state.user_info == None
        assert mock_session_state.access_token == None

class TestDataConsistency:
    """資料一致性測試"""
    
    @patch('requests.post')
    @patch('requests.get')
    def test_data_crud_consistency(self, mock_get, mock_post):
        """測試資料CRUD一致性"""
        # 模擬新增資料
        mock_post_response = Mock()
        mock_post_response.status_code = 201
        mock_post_response.json.return_value = {
            "message": "新增成功",
            "id": 1,
            "data": {
                "id": 1,
                "name": "張三",
                "id_number_masked": "A123****89",
                "address": "台北市信義區信義路100號",
                "notes": "測試資料",
                "created_by": "test_user"
            }
        }
        mock_post.return_value = mock_post_response
        
        # 模擬查詢資料
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            "data": [{
                "id": 1,
                "name": "張三",
                "id_number_masked": "A123****89",
                "address": "台北市信義區信義路100號",
                "notes": "測試資料",
                "created_by": "test_user"
            }]
        }
        mock_get.return_value = mock_get_response
        
        # 驗證新增後查詢的一致性
        created_data = mock_post_response.json()["data"]
        queried_data = mock_get_response.json()["data"][0]
        
        assert created_data["id"] == queried_data["id"]
        assert created_data["name"] == queried_data["name"]
        assert created_data["id_number_masked"] == queried_data["id_number_masked"]
        assert created_data["created_by"] == queried_data["created_by"]
    
    def test_id_number_masking_consistency(self):
        """測試身分證字號遮罩一致性"""
        # 模擬遮罩函數
        def mask_id_number(id_number):
            if len(id_number) == 10:
                return f"{id_number[:4]}****{id_number[-2:]}"
            return id_number
        
        # 測試不同身分證字號的遮罩
        test_cases = [
            ("A123456789", "A123****89"),
            ("B234567890", "B234****90"),
            ("C345678901", "C345****01"),
        ]
        
        for original, expected in test_cases:
            masked = mask_id_number(original)
            assert masked == expected, f"身分證字號 {original} 遮罩結果不一致"
            
            # 確保遮罩後仍保持10位長度
            assert len(masked) == 10, "遮罩後長度應該保持10位"

class TestPerformanceAndScalability:
    """效能和擴展性測試"""
    
    def test_large_dataset_handling(self):
        """測試大資料集處理"""
        # 模擬大量資料
        large_dataset = []
        for i in range(1000):
            large_dataset.append({
                "id": i,
                "name": f"用戶{i}",
                "id_number_masked": f"A{i:03d}****{i%100:02d}",
                "created_by": f"user_{i%10}"
            })
        
        # 測試分頁功能
        def paginate_large_dataset(data, page, per_page):
            start = (page - 1) * per_page
            end = start + per_page
            return data[start:end]
        
        # 測試第一頁
        page1 = paginate_large_dataset(large_dataset, 1, 50)
        assert len(page1) == 50
        assert page1[0]["id"] == 0
        
        # 測試中間頁
        page10 = paginate_large_dataset(large_dataset, 10, 50)
        assert len(page10) == 50
        assert page10[0]["id"] == 450
        
        # 測試最後一頁
        last_page = paginate_large_dataset(large_dataset, 20, 50)
        assert len(last_page) == 50
        assert last_page[0]["id"] == 950
    
    def test_search_performance(self):
        """測試搜尋效能"""
        # 模擬搜尋功能
        sample_data = [
            {"name": "張三", "address": "台北市"},
            {"name": "李四", "address": "新北市"},
            {"name": "王五", "address": "台中市"},
            {"name": "張小明", "address": "台北市"}
        ]
        
        def search_data(data, keyword):
            results = []
            for item in data:
                if keyword in item["name"] or keyword in item["address"]:
                    results.append(item)
            return results
        
        # 測試名稱搜尋
        name_results = search_data(sample_data, "張")
        assert len(name_results) == 2
        
        # 測試地址搜尋
        address_results = search_data(sample_data, "台北")
        assert len(address_results) == 2
        
        # 測試空關鍵字
        empty_results = search_data(sample_data, "")
        assert len(empty_results) == 4  # 空關鍵字應該返回所有結果

class TestErrorRecovery:
    """錯誤恢復測試"""
    
    @patch('streamlit.session_state')
    @patch('requests.post')
    def test_api_failure_recovery(self, mock_post, mock_session_state):
        """測試API失敗恢復"""
        # 設定會話狀態
        mock_session_state.logged_in = True
        mock_session_state.access_token = "test-token"
        
        # 模擬API失敗
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"detail": "內部伺服器錯誤"}
        mock_post.return_value = mock_response
        
        # 模擬錯誤處理和重試邏輯
        def handle_api_error_with_retry(max_retries=3):
            for attempt in range(max_retries):
                try:
                    response = mock_post("http://api/test")
                    if response.status_code == 200:
                        return True, response.json()
                    elif response.status_code >= 500:
                        # 伺服器錯誤，可以重試
                        if attempt < max_retries - 1:
                            continue
                        else:
                            return False, "伺服器錯誤，請稍後再試"
                    else:
                        # 客戶端錯誤，不重試
                        return False, response.json().get("detail", "請求錯誤")
                except Exception as e:
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return False, f"網路錯誤: {str(e)}"
            
            return False, "達到最大重試次數"
        
        # 測試錯誤處理
        success, message = handle_api_error_with_retry()
        assert success == False
        assert "伺服器錯誤" in message
    
    def test_session_timeout_recovery(self):
        """測試會話逾時恢復"""
        import time
        
        # 模擬會話管理
        class SessionManager:
            def __init__(self):
                self.login_time = None
                self.timeout_seconds = 3600  # 1小時
            
            def login(self):
                self.login_time = time.time()
            
            def is_session_valid(self):
                if self.login_time is None:
                    return False
                return (time.time() - self.login_time) < self.timeout_seconds
            
            def refresh_session(self):
                self.login_time = time.time()
        
        # 測試會話管理
        session = SessionManager()
        
        # 未登入狀態
        assert session.is_session_valid() == False
        
        # 登入後
        session.login()
        assert session.is_session_valid() == True
        
        # 刷新會話
        session.refresh_session()
        assert session.is_session_valid() == True 
#!/usr/bin/env python3
"""
只遷移數據的腳本
"""

from app.models.database import engine
from sqlalchemy import text
import uuid
from datetime import datetime

def migrate_data():
    print('開始遷移數據...')

    with engine.connect() as conn:
        trans = conn.begin()
        
        try:
            # 1. 獲取備份數據
            result = conn.execute(text('SELECT * FROM payee_data_backup'))
            old_data = result.fetchall()
            print(f'找到 {len(old_data)} 筆備份記錄')
            
            # 2. 獲取admin用戶ID
            result = conn.execute(text("SELECT id FROM users WHERE username = 'admin'"))
            admin_user = result.fetchone()
            if not admin_user:
                raise Exception('找不到admin用戶')
            admin_id = admin_user[0]
            print(f'使用admin用戶ID: {admin_id}')
            
            # 3. 遷移數據
            for i, row in enumerate(old_data):
                new_id = str(uuid.uuid4())
                id_number = row[2] if row[2] else f'ID{i+1:06d}'
                
                conn.execute(text('''
                    INSERT INTO payee_data (
                        id, name, id_number, phone, email, address,
                        bank_name, bank_code, account_number, account_name,
                        department, notes, created_by, updated_by, is_active,
                        created_at, updated_at
                    ) VALUES (
                        :id, :name, :id_number, :phone, :email, :address,
                        :bank_name, :bank_code, :account_number, :account_name,
                        :department, :notes, :created_by, :updated_by, :is_active,
                        :created_at, :updated_at
                    )
                '''), {
                    'id': new_id,
                    'name': row[1],
                    'id_number': id_number,
                    'phone': f'09{i+1:08d}',
                    'email': f'{row[1].lower()}@example.com' if row[1] else None,
                    'address': row[3],
                    'bank_name': '台灣銀行',
                    'bank_code': '004',
                    'account_number': f'*********{i+1:04d}',
                    'account_name': row[1],
                    'department': '資訊部',
                    'notes': row[4],
                    'created_by': admin_id,
                    'updated_by': admin_id,
                    'is_active': row[7],
                    'created_at': row[8] or datetime.utcnow(),
                    'updated_at': row[9] or datetime.utcnow()
                })
            
            trans.commit()
            print(f'成功遷移 {len(old_data)} 筆記錄')
            
            # 驗證
            result = conn.execute(text('SELECT COUNT(*) FROM payee_data'))
            count = result.fetchone()[0]
            print(f'新表記錄數: {count}')
            
        except Exception as e:
            print(f'遷移失敗: {e}')
            trans.rollback()
            raise

if __name__ == "__main__":
    migrate_data()

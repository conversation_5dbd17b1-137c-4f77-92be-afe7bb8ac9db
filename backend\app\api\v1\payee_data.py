"""
受款人資料管理API路由
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, Request, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from sqlalchemy import and_
from datetime import datetime, timedelta
import pytz

from app.models.database import get_db
from app.models.user import User
from app.models.payee_data import PayeeData
from app.schemas.payee_data import (
    PayeeDataCreate, PayeeDataUpdate, PayeeDataQuery,
    PayeeDataResponse, PayeeDataListResponse
)
from app.schemas.common import MessageResponse, SuccessResponse
from app.services.payee_data import PayeeDataService
from app.core.dependencies import get_current_active_user, get_client_info
from app.core.exceptions import (
    PayeeDataNotFoundError, DuplicateIdNumberError,
    PermissionDeniedError, ValidationError, CBAException
)

# 設定台北時區
taipei_tz = pytz.timezone('Asia/Taipei')

router = APIRouter(prefix="/payee-data", tags=["受款人資料管理"])


@router.post("/", response_model=PayeeDataResponse, status_code=status.HTTP_201_CREATED)
async def create_payee_data(
    data: PayeeDataCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    建立受款人資料
    
    需要權限：CREATE_PAYEE_DATA
    """
    print(f"create_payee_data: {data}")
    try:
        client_info = get_client_info(request)
        service = PayeeDataService(db)
        
        result = service.create_payee_data(
            data=data,
            current_user=current_user,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent")
        )
        
        return result
        
    except DuplicateIdNumberError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=PayeeDataListResponse)
async def get_payee_data_list(
    search: Optional[str] = Query(None, description="搜尋關鍵字"),
    name: Optional[str] = Query(None, description="姓名篩選"),
    address: Optional[str] = Query(None, description="地址篩選"),
    department: Optional[str] = Query(None, description="部門篩選"),
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(20, ge=1, le=10000, description="每頁筆數"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取得受款人資料列表
    
    需要權限：READ_PAYEE_DATA 或 READ_ALL_DATA
    """
    try:
        query = PayeeDataQuery(
            search=search,
            name=name,
            address=address,
            department=department,
            page=page,
            size=size
        )
        
        service = PayeeDataService(db)
        return service.get_payee_data_list(query, current_user)
        
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{data_id}", response_model=PayeeDataResponse)
async def get_payee_data(
    data_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取得特定受款人資料
    
    需要權限：READ_PAYEE_DATA 或 READ_ALL_DATA
    """
    try:
        service = PayeeDataService(db)
        return service.get_payee_data(data_id, current_user)
        
    except PayeeDataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{data_id}", response_model=PayeeDataResponse)
async def update_payee_data(
    data_id: int,
    data: PayeeDataUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新受款人資料
    
    需要權限：UPDATE_PAYEE_DATA（所有用戶都可修改）
    """
    try:
        client_info = get_client_info(request)
        service = PayeeDataService(db)
        
        result = service.update_payee_data(
            data_id=data_id,
            data=data,
            current_user=current_user,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent")
        )
        
        return result
        
    except PayeeDataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{data_id}", response_model=MessageResponse)
async def delete_payee_data(
    data_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    刪除受款人資料（軟刪除）
    
    需要權限：DELETE_PAYEE_DATA（所有用戶都可刪除）
    """
    try:
        client_info = get_client_info(request)
        service = PayeeDataService(db)
        
        success = service.delete_payee_data(
            data_id=data_id,
            current_user=current_user,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent")
        )
        
        if success:
            return MessageResponse(message="受款人資料已成功刪除")
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="刪除操作失敗"
            )
        
    except PayeeDataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/validation/id-number/{id_number}", response_model=dict)
async def validate_id_number(
    id_number: str,
    exclude_id: Optional[int] = Query(None, description="排除的資料ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    驗證身分證字號是否已存在
    
    需要權限：CREATE_PAYEE_DATA
    """
    try:
        service = PayeeDataService(db)
        exists = service.check_id_number_exists(id_number, exclude_id)
        
        return {
            "id_number": id_number,
            "exists": exists,
            "message": "身分證字號已存在" if exists else "身分證字號可以使用"
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except CBAException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/statistics/overview")
async def get_statistics_overview(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取得統計概覽
    
    需要權限：READ_PAYEE_DATA 或 READ_ALL_DATA
    """
    try:
        # 基本統計查詢
        query = db.query(PayeeData).filter(PayeeData.is_active == True)
        
        # 如果不是全域用戶，只能看自己的資料
        if not current_user.has_permission("READ_ALL_DATA"):
            query = query.filter(PayeeData.created_by_user_id == current_user.id)
        
        # 總數統計
        total_count = query.count()
        
        # 取得當前台北時間
        now = datetime.now(taipei_tz)

        # 轉換為UTC時間進行資料庫查詢（SQLite儲存的是UTC時間）
        # 本月統計
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_start_utc = month_start.astimezone(pytz.UTC).replace(tzinfo=None)
        month_query = query.filter(PayeeData.created_at >= month_start_utc)
        month_count = month_query.count()

        # 本週統計（週一為起點）
        week_start = now - timedelta(days=now.weekday())
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start_utc = week_start.astimezone(pytz.UTC).replace(tzinfo=None)
        week_query = query.filter(PayeeData.created_at >= week_start_utc)
        week_count = week_query.count()

        # 今日統計
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_start_utc = today_start.astimezone(pytz.UTC).replace(tzinfo=None)
        today_query = query.filter(PayeeData.created_at >= today_start_utc)
        today_count = today_query.count()

        # 上個月統計（用於計算增減）
        if now.month == 1:
            last_month_start = now.replace(year=now.year-1, month=12, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            last_month_start = now.replace(month=now.month-1, day=1, hour=0, minute=0, second=0, microsecond=0)

        last_month_start_utc = last_month_start.astimezone(pytz.UTC).replace(tzinfo=None)
        last_month_end_utc = month_start_utc

        last_month_query = query.filter(
            and_(
                PayeeData.created_at >= last_month_start_utc,
                PayeeData.created_at < last_month_end_utc
            )
        )
        last_month_count = last_month_query.count()
        
        # 計算月增減
        monthly_delta = month_count - last_month_count if last_month_count > 0 else month_count
        
        return {
            "success": True,
            "data": {
                "total_records": total_count,
                "monthly_new": month_count,
                "monthly_delta": monthly_delta,
                "weekly_new": week_count,
                "daily_new": today_count,
                "last_month_count": last_month_count
            },
            "timestamp": now.timestamp()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "STATISTICS_ERROR",
                "message": f"取得統計資料失敗: {str(e)}"
            }
        )


@router.get("/statistics/charts")
async def get_statistics_charts(
    chart_type: str = Query(..., description="圖表類型: monthly, weekly, department"),
    period: int = Query(12, description="期間（月份數）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取得統計圖表資料
    
    需要權限：READ_PAYEE_DATA 或 READ_ALL_DATA
    """
    try:
        query = db.query(PayeeData).filter(PayeeData.is_active == True)
        
        # 權限控制
        if not current_user.has_permission("READ_ALL_DATA"):
            query = query.filter(PayeeData.created_by_user_id == current_user.id)
        
        # 取得當前台北時間
        now = datetime.now(taipei_tz)
        
        if chart_type == "monthly":
            # 月度統計
            results = []
            
            for i in range(period):
                if now.month - i <= 0:
                    year_offset = (i - now.month) // 12 + 1
                    month = 12 - ((i - now.month) % 12)
                    year = now.year - year_offset
                else:
                    month = now.month - i
                    year = now.year
                
                month_start = now.replace(year=year, month=month, day=1, hour=0, minute=0, second=0, microsecond=0)

                # 計算下個月的開始
                if month == 12:
                    next_month_start = month_start.replace(year=year+1, month=1)
                else:
                    next_month_start = month_start.replace(month=month+1)

                # 轉換為UTC時間進行資料庫查詢
                month_start_utc = month_start.astimezone(pytz.UTC).replace(tzinfo=None)
                next_month_start_utc = next_month_start.astimezone(pytz.UTC).replace(tzinfo=None)

                count = query.filter(
                    and_(
                        PayeeData.created_at >= month_start_utc,
                        PayeeData.created_at < next_month_start_utc
                    )
                ).count()
                
                results.append({
                    "period": f"{year}-{month:02d}",
                    "count": count,
                    "label": f"{year}年{month}月"
                })
            
            results.reverse()  # 按時間順序排列
            
        elif chart_type == "weekly":
            # 週度統計
            results = []
            
            for i in range(8):  # 最近8週
                week_start = now - timedelta(days=now.weekday() + 7 * i)
                week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
                week_end = week_start + timedelta(days=7)

                # 轉換為UTC時間進行資料庫查詢
                week_start_utc = week_start.astimezone(pytz.UTC).replace(tzinfo=None)
                week_end_utc = week_end.astimezone(pytz.UTC).replace(tzinfo=None)

                count = query.filter(
                    and_(
                        PayeeData.created_at >= week_start_utc,
                        PayeeData.created_at < week_end_utc
                    )
                ).count()
                
                results.append({
                    "period": f"W{week_start.isocalendar()[1]}",
                    "count": count,
                    "label": f"{week_start.month}/{week_start.day} - {(week_end-timedelta(days=1)).month}/{(week_end-timedelta(days=1)).day}"
                })
            
            results.reverse()  # 按時間順序排列
            
        elif chart_type == "department":
            # 部門統計（僅管理者可用）
            if not current_user.has_permission("READ_ALL_DATA"):
                raise PermissionDeniedError("權限不足，無法查看部門統計")
            
            # 按建立者的部門統計
            results = db.query(
                User.department.label("department"),
                func.count(PayeeData.id).label("count")
            ).join(
                PayeeData, PayeeData.created_by_user_id == User.id
            ).filter(
                PayeeData.is_active == True
            ).group_by(User.department).all()
            
            results = [
                {
                    "department": result.department or "未指定部門",
                    "count": result.count
                }
                for result in results
            ]
            
        else:
            raise ValidationError(f"不支援的圖表類型: {chart_type}")
        
        return {
            "success": True,
            "data": {
                "chart_type": chart_type,
                "results": results
            },
            "timestamp": now.timestamp()
        }
        
    except (PermissionDeniedError, ValidationError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN if isinstance(e, PermissionDeniedError) else status.HTTP_400_BAD_REQUEST,
            detail={
                "error": e.error_code,
                "message": str(e)
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "STATISTICS_ERROR",
                "message": f"取得圖表資料失敗: {str(e)}"
            }
        ) 
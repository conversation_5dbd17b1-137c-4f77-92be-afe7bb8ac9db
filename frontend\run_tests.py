#!/usr/bin/env python3
"""
測試執行腳本
執行所有測試並產生測試報告
"""

import subprocess
import sys
import os
from pathlib import Path

def run_tests():
    """執行測試並產生報告"""
    
    # 設定測試目錄
    test_dir = Path(__file__).parent / "tests"
    
    # 確保測試目錄存在
    if not test_dir.exists():
        print(f"❌ 測試目錄不存在: {test_dir}")
        return False
    
    print("🧪 開始執行第三階段前端測試...")
    print(f"📁 測試目錄: {test_dir}")
    
    # pytest命令參數
    pytest_args = [
        "python", "-m", "pytest",
        str(test_dir),
        "-v",                    # 詳細輸出
        "--tb=short",           # 簡短的錯誤追蹤
        "--cov=components",     # 程式碼覆蓋率（組件）
        "--cov=main",           # 程式碼覆蓋率（主程式）
        "--cov-report=html:htmlcov",  # HTML覆蓋率報告
        "--cov-report=term",    # 終端覆蓋率報告
        "--html=reports/test_report.html",  # HTML測試報告
        "--self-contained-html",  # 自包含HTML報告
        "-x",                   # 第一個失敗就停止
    ]
    
    # 建立報告目錄
    os.makedirs("reports", exist_ok=True)
    
    try:
        print("⚡ 執行pytest...")
        result = subprocess.run(pytest_args, capture_output=True, text=True)
        
        print("📊 測試執行結果:")
        print("=" * 60)
        print(result.stdout)
        
        if result.stderr:
            print("\n⚠️  錯誤輸出:")
            print("=" * 60)
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 所有測試通過！")
            print(f"📈 覆蓋率報告: htmlcov/index.html")
            print(f"📋 測試報告: reports/test_report.html")
        else:
            print(f"\n❌ 測試失敗，退出碼: {result.returncode}")
            
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ pytest未安裝，請先安裝測試依賴:")
        print("pip install -r test_requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 執行測試時發生錯誤: {e}")
        return False

def check_test_coverage():
    """檢查測試覆蓋率"""
    coverage_file = Path("htmlcov/index.html")
    if coverage_file.exists():
        print(f"\n📈 詳細覆蓋率報告已生成: {coverage_file.absolute()}")
    else:
        print("\n⚠️  覆蓋率報告未生成")

def run_specific_test(test_name):
    """執行特定測試"""
    pytest_args = [
        "python", "-m", "pytest",
        f"tests/{test_name}",
        "-v",
        "--tb=short"
    ]
    
    try:
        result = subprocess.run(pytest_args, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 執行測試失敗: {e}")
        return False

def main():
    """主函數"""
    if len(sys.argv) > 1:
        # 執行特定測試
        test_name = sys.argv[1]
        print(f"🧪 執行特定測試: {test_name}")
        success = run_specific_test(test_name)
    else:
        # 執行所有測試
        success = run_tests()
        check_test_coverage()
    
    if success:
        print("\n🎉 測試執行完成！")
        return 0
    else:
        print("\n💥 測試執行失敗！")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 
# CBA人員資料調查系統 - 環境變數配置範例
# 請將此檔案複製為 .env 並修改對應的配置值

# ===========================================
# 基本設定
# ===========================================
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# ===========================================
# 資料庫設定
# ===========================================
DATABASE_URL=sqlite:///./database/cba_personal_data.db

# ===========================================
# JWT 認證設定
# ===========================================
# 注意：生產環境請使用強安全密鑰
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-minimum-32-chars
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480

# ===========================================
# 資料加密設定
# ===========================================
# 注意：生產環境請使用強加密密鑰
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this-in-production-32-chars

# OAuth2 SSO設定已移除 - 只使用SOAP SSO

# ===========================================
# SOAP SSO 設定 (政府SSO)
# ===========================================
# 政府單一登入系統設定 - 已預設為屏東縣政府SSO
SSO_SOAP_WS_URL=https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL

# 如果是其他政府機關，請修改為對應的SOAP Web Service URL
# SSO_SOAP_WS_URL=https://your-gov-sso.gov.tw/path/to/service.asmx?WSDL

# ===========================================
# 前端設定
# ===========================================
FRONTEND_URL=http://localhost:8501

# ===========================================
# 審計日誌設定
# ===========================================
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=365

# ===========================================
# 密碼政策設定
# ===========================================
MIN_PASSWORD_LENGTH=8
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=false

# ===========================================
# API 限制設定
# ===========================================
API_RATE_LIMIT=100
MAX_QUERY_RESULTS=1000 
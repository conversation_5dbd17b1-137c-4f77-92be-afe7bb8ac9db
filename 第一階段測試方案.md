# CBA個人資料搜集系統 - 第一階段測試方案

## 階段概述
**階段名稱**：基礎架構與認證系統  
**開發期間**：2-3週  
**測試目標**：確保系統基礎架構穩定，認證系統安全可靠  

## 測試範圍

### 1. 專案架構建立測試
#### 1.1 目錄結構驗證
- **測試項目**：專案目錄結構完整性
- **測試方法**：檢查必要目錄和檔案是否正確建立
- **預期結果**：
  ```
  CBA人員資料調查/
  ├── backend/
  │   ├── app/
  │   │   ├── api/
  │   │   ├── core/
  │   │   ├── models/
  │   │   ├── services/
  │   │   └── utils/
  │   ├── tests/
  │   └── requirements.txt
  ├── frontend/
  │   ├── pages/
  │   ├── components/
  │   └── utils/
  ├── database/
  └── docs/
  ```

#### 1.2 依賴套件安裝測試
- **測試項目**：Python套件安裝驗證
- **測試方法**：執行 `uv pip install -r requirements.txt`
- **預期結果**：所有必要套件成功安裝，無衝突錯誤

#### 1.3 環境設定檔測試
- **測試項目**：.env檔案設定正確性
- **測試方法**：檢查環境變數載入和解析
- **預期結果**：環境變數正確載入，敏感資訊已保護

### 2. 資料庫設計與建立測試
#### 2.1 資料庫連接測試
- **測試項目**：SQLite資料庫連接
- **測試方法**：建立資料庫連接並執行簡單查詢
- **測試案例**：
  ```python
  def test_database_connection():
      conn = sqlite3.connect('test.db')
      cursor = conn.cursor()
      cursor.execute("SELECT 1")
      result = cursor.fetchone()
      assert result[0] == 1
      conn.close()
  ```

#### 2.2 資料表結構建立測試
- **測試項目**：所有資料表正確建立
- **測試方法**：執行DDL腳本並檢查表結構
- **預期結果**：
  - personal_data表建立成功
  - users表建立成功  
  - roles表建立成功
  - user_roles表建立成功
  - departments表建立成功
  - audit_log表建立成功

#### 2.3 資料表關聯測試
- **測試項目**：外鍵約束正確性
- **測試方法**：測試參照完整性約束
- **測試案例**：
  ```python
  def test_foreign_key_constraints():
      # 測試user_roles表的外鍵約束
      # 測試personal_data表的外鍵約束
      # 驗證約束違反時的錯誤處理
  ```

### 3. 基本認證系統測試
#### 3.1 JWT Token生成測試
- **測試項目**：JWT Token正確生成
- **測試方法**：驗證Token格式和內容
- **測試案例**：
  ```python
  def test_jwt_token_generation():
      user_data = {"user_id": 1, "username": "test_user"}
      token = create_access_token(user_data)
      
      # 驗證Token格式
      assert len(token.split('.')) == 3
      
      # 驗證Token內容
      payload = decode_token(token)
      assert payload["user_id"] == 1
      assert payload["username"] == "test_user"
  ```

#### 3.2 JWT Token驗證測試
- **測試項目**：Token驗證機制
- **測試方法**：測試有效和無效Token
- **測試案例**：
  - 有效Token驗證通過
  - 過期Token驗證失敗
  - 無效格式Token驗證失敗
  - 簽名錯誤Token驗證失敗

#### 3.3 權限檢查測試
- **測試項目**：基本權限檢查機制
- **測試方法**：測試不同權限等級的存取控制
- **測試案例**：
  ```python
  def test_permission_check():
      # 測試一般用戶權限
      general_user = {"role": "general", "permissions": ["CREATE_PERSONAL_DATA"]}
      assert check_permission(general_user, "CREATE_PERSONAL_DATA") == True
      assert check_permission(general_user, "READ_ALL_DATA") == False
      
      # 測試管理員權限
      admin_user = {"role": "admin", "permissions": ["MANAGE_ADMIN"]}
      assert check_permission(admin_user, "MANAGE_ADMIN") == True
  ```

### 4. SSO整合測試
#### 4.1 SSO登入流程測試
- **測試項目**：單一簽入登入流程
- **測試方法**：模擬SSO認證流程
- **測試步驟**：
  1. 導向SSO認證頁面
  2. 接收認證回應
  3. 驗證認證資訊
  4. 建立本地會話

#### 4.2 SSO認證資料解析測試
- **測試項目**：SSO回傳資料正確解析
- **測試方法**：驗證用戶資訊提取正確性
- **預期結果**：
  - 用戶ID正確提取
  - 用戶名稱正確提取
  - 部門資訊正確提取
  - 權限資訊正確映射

#### 4.3 SSO會話同步測試
- **測試項目**：SSO會話狀態同步
- **測試方法**：測試會話建立、更新、終止
- **測試案例**：
  - SSO登出時本地會話同步終止
  - SSO會話過期時本地會話處理
  - 會話資訊同步更新

### 5. 核心工具類別測試
#### 5.1 加密工具測試
- **測試項目**：AES-256-GCM加密解密
- **測試方法**：測試加密解密正確性
- **測試案例**：
  ```python
  def test_encryption_decryption():
      original_text = "A123456789"
      encrypted_data = encrypt_id_number(original_text)
      decrypted_text = decrypt_id_number(encrypted_data)
      
      assert decrypted_text == original_text
      assert encrypted_data != original_text
  ```

#### 5.2 驗證工具測試
- **測試項目**：身分證字號格式驗證
- **測試方法**：測試各種格式的身分證字號
- **測試案例**：
  ```python
  def test_id_number_validation():
      # 有效身分證字號
      assert validate_id_number("A123456789") == True
      assert validate_id_number("B234567890") == True
      
      # 無效身分證字號
      assert validate_id_number("123456789") == False
      assert validate_id_number("A12345678") == False
      assert validate_id_number("A1234567890") == False
  ```

#### 5.3 審計日誌工具測試
- **測試項目**：操作日誌記錄功能
- **測試方法**：驗證日誌記錄完整性
- **測試案例**：
  ```python
  def test_audit_logging():
      log_entry = create_audit_log(
          user_id=1,
          action="CREATE",
          table_name="personal_data",
          record_id=1,
          new_values={"name": "測試用戶"}
      )
      
      assert log_entry["user_id"] == 1
      assert log_entry["action"] == "CREATE"
      assert log_entry["timestamp"] is not None
  ```

## 測試執行計畫

### 測試環境準備
1. **開發環境**：Python 3.11 + uv套件管理
2. **測試資料庫**：SQLite測試資料庫
3. **測試框架**：pytest
4. **覆蓋率工具**：pytest-cov

### 測試執行順序
1. 專案架構建立測試
2. 資料庫設計與建立測試  
3. 核心工具類別測試
4. 基本認證系統測試
5. SSO整合測試

### 成功標準
- **功能測試**：所有測試案例通過率 100%
- **程式碼覆蓋率**：核心功能覆蓋率 > 80%
- **效能測試**：資料庫操作回應時間 < 500ms
- **安全測試**：加密解密功能正確性 100%

### 測試報告
測試完成後將產出：
1. 測試執行報告
2. 程式碼覆蓋率報告
3. 效能測試報告
4. 安全測試報告

## 風險評估
- **高風險**：SSO整合複雜度，需充分測試認證流程
- **中風險**：加密功能正確性，需驗證加密演算法實作
- **低風險**：基本CRUD操作，技術成熟風險低

## 下一階段準備
測試通過後準備進入第二階段：
- 個人資料管理API開發
- 權限管控系統實作
- 資料驗證機制建立 
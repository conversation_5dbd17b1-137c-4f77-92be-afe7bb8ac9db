#!/usr/bin/env python3
"""
CBA受款人資料搜集系統 - 整合啟動腳本
統一管理所有服務的啟動、停止和監控
"""

import os
import sys
import time
import signal
import subprocess
import threading
import requests
from pathlib import Path
from typing import List, Dict, Optional
import json
import logging

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedSystemManager:
    """整合系統管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
        # 服務配置
        self.services = {
            "sso": {
                "name": "SSO服務",
                "port": 8000,
                "cwd": self.base_dir / "odc_sso",
                "command": ["uv", "run", "uvicorn", "sso_service:app", "--host", "0.0.0.0", "--port", "8000"],
                "health_url": "http://localhost:8000/",
                "startup_delay": 2
            },
            "backend": {
                "name": "後端API",
                "port": 8080,
                "cwd": self.base_dir / "backend",
                "command": ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"],
                "health_url": "http://localhost:8080/health",
                "startup_delay": 3
            },
            "frontend": {
                "name": "前端UI",
                "port": 8501,
                "cwd": self.base_dir / "frontend",
                "command": ["streamlit", "run", "main.py", "--server.port", "8501", "--server.address", "0.0.0.0", "--server.headless", "true"],
                "health_url": "http://localhost:8501",
                "startup_delay": 5
            }
        }
    
    def check_port_available(self, port: int) -> bool:
        """檢查埠是否可用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return True
            except OSError:
                return False
    
    def wait_for_service(self, service_name: str, timeout: int = 30) -> bool:
        """等待服務啟動"""
        service = self.services[service_name]
        health_url = service["health_url"]
        
        logger.info(f"等待 {service['name']} 啟動...")
        
        for i in range(timeout):
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    logger.info(f"✅ {service['name']} 已啟動")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error(f"❌ {service['name']} 啟動超時")
        return False
    
    def start_service(self, service_name: str) -> bool:
        """啟動單個服務"""
        service = self.services[service_name]
        
        # 檢查埠是否被佔用
        if not self.check_port_available(service["port"]):
            logger.warning(f"⚠️ 埠 {service['port']} 已被佔用，{service['name']} 可能已在運行")
        
        logger.info(f"🚀 啟動 {service['name']}...")
        
        try:
            # 確保工作目錄存在
            if not service["cwd"].exists():
                logger.error(f"❌ 工作目錄不存在: {service['cwd']}")
                return False
            
            # 啟動服務
            process = subprocess.Popen(
                service["command"],
                cwd=service["cwd"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[service_name] = process
            
            # 等待服務啟動
            time.sleep(service["startup_delay"])
            
            # 檢查進程是否還在運行
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {service['name']} 啟動失敗")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
            
            # 等待服務健康檢查通過
            if self.wait_for_service(service_name):
                return True
            else:
                self.stop_service(service_name)
                return False
                
        except Exception as e:
            logger.error(f"❌ 啟動 {service['name']} 時發生錯誤: {e}")
            return False
    
    def stop_service(self, service_name: str):
        """停止單個服務"""
        if service_name in self.processes:
            process = self.processes[service_name]
            service = self.services[service_name]
            
            logger.info(f"🛑 停止 {service['name']}...")
            
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️ {service['name']} 未在10秒內停止，強制終止")
                process.kill()
                process.wait()
            
            del self.processes[service_name]
            logger.info(f"✅ {service['name']} 已停止")
    
    def start_all_services(self) -> bool:
        """按順序啟動所有服務"""
        logger.info("🚀 開始啟動CBA受款人資料搜集系統...")
        
        # 按順序啟動服務
        service_order = ["sso", "backend", "frontend"]
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ 啟動 {self.services[service_name]['name']} 失敗，停止啟動流程")
                self.stop_all_services()
                return False
        
        self.running = True
        logger.info("🎉 所有服務已成功啟動！")
        logger.info("📋 服務狀態:")
        for service_name, service in self.services.items():
            logger.info(f"  • {service['name']}: http://localhost:{service['port']}")
        
        logger.info("🌐 整合服務入口: http://localhost:80 (需要nginx)")
        return True
    
    def stop_all_services(self):
        """停止所有服務"""
        logger.info("🛑 停止所有服務...")
        
        # 反向順序停止服務
        service_order = ["frontend", "backend", "sso"]
        
        for service_name in service_order:
            if service_name in self.processes:
                self.stop_service(service_name)
        
        self.running = False
        logger.info("✅ 所有服務已停止")
    
    def monitor_services(self):
        """監控服務狀態"""
        while self.running:
            for service_name, process in list(self.processes.items()):
                if process.poll() is not None:
                    logger.error(f"❌ {self.services[service_name]['name']} 意外停止")
                    self.running = False
                    break
            
            time.sleep(5)
    
    def signal_handler(self, signum, frame):
        """信號處理器"""
        logger.info("📡 收到停止信號，正在關閉系統...")
        self.stop_all_services()
        sys.exit(0)
    
    def run(self):
        """運行整合系統"""
        # 註冊信號處理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 啟動所有服務
        if not self.start_all_services():
            sys.exit(1)
        
        # 開始監控
        monitor_thread = threading.Thread(target=self.monitor_services)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            # 保持主線程運行
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all_services()

def main():
    """主函數"""
    print("🏢 CBA受款人資料搜集系統 - 整合啟動器")
    print("=" * 50)
    
    manager = IntegratedSystemManager()
    manager.run()

if __name__ == "__main__":
    main()

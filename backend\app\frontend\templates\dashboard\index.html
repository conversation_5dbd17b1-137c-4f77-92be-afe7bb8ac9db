{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="h2 mb-2 text-primary">
                        <i class="bi bi-speedometer2" aria-hidden="true"></i>
                        <span>主控台</span>
                    </h1>
                    <p class="text-muted mb-0">歡迎使用CBA受款人資料搜集系統</p>
                    <small class="text-muted">
                        <i class="bi bi-person-circle" aria-hidden="true"></i>
                        {{ user.full_name or user.username }} |
                        <i class="bi bi-clock" aria-hidden="true"></i>
                        {{ current_time.strftime('%Y年%m月%d日 %H:%M') if current_time else '' }}
                    </small>
                </div>
                <div class="d-none d-md-block">
                    <a href="/payee/create" class="btn btn-primary btn-lg hover-lift">
                        <i class="bi bi-plus-circle" aria-hidden="true"></i>
                        <span>新增受款人</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 統計卡片 -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                總受款人數
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.total_payees or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-arrow-up text-success" aria-hidden="true"></i>
                                系統中的所有受款人
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-people" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                活躍受款人
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.active_payees or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-check-circle text-success" aria-hidden="true"></i>
                                資料完整且有效
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-person-check" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                部門數量
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.total_departments or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-building text-info" aria-hidden="true"></i>
                                系統中的部門總數
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-building" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                本月新增
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.monthly_new or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-graph-up text-warning" aria-hidden="true"></i>
                                本月新建立的記錄
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-graph-up" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速操作和最近活動 -->
    <div class="row">
        <!-- 快速操作 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning"></i> 快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="/payee/create" class="btn btn-primary btn-lg w-100">
                                <i class="bi bi-person-plus"></i><br>
                                新增受款人
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/payee" class="btn btn-success btn-lg w-100">
                                <i class="bi bi-list-ul"></i><br>
                                受款人列表
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/reports" class="btn btn-info btn-lg w-100">
                                <i class="bi bi-file-earmark-text"></i><br>
                                產生報表
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/export" class="btn btn-warning btn-lg w-100">
                                <i class="bi bi-download"></i><br>
                                匯出資料
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活動 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history"></i> 最近活動
                    </h6>
                </div>
                <div class="card-body">
                    {% if stats.recent_activities %}
                        {% for activity in stats.recent_activities %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                {% if activity.action == 'create' %}
                                    <i class="bi bi-plus-circle text-success"></i>
                                {% elif activity.action == 'update' %}
                                    <i class="bi bi-pencil-square text-primary"></i>
                                {% elif activity.action == 'delete' %}
                                    <i class="bi bi-trash text-danger"></i>
                                {% else %}
                                    <i class="bi bi-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">{{ activity.description }}</div>
                                <div class="small text-muted">{{ activity.created_at }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                            <p class="mt-2">暫無最近活動</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系統狀態 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle"></i> 系統資訊
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>當前用戶：</strong><br>
                            <span class="text-muted">{{ user.full_name or user.username }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>用戶角色：</strong><br>
                            <span class="badge badge-{% if user.role == 'admin' %}danger{% elif user.role == 'global' %}warning{% else %}info{% endif %}">
                                {% if user.role == 'admin' %}管理員{% elif user.role == 'global' %}全域用戶{% else %}一般用戶{% endif %}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>所屬部門：</strong><br>
                            <span class="text-muted">{{ user.department or '未設定' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>最後登入：</strong><br>
                            <span class="text-muted">{{ user.last_login_at or '首次登入' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.btn-lg {
    padding: 1rem;
    font-size: 0.9rem;
}

.card {
    border: none;
    border-radius: 0.35rem;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}
</style>
{% endblock %}

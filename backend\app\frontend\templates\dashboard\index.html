{% extends "base.html" %}

{% block title %}{{ title }} - CBA受款人資料搜集系統{% endblock %}

{% block extra_css %}
<style>
/* 儀表板專用樣式修復 - 強制移除所有頂部空白 */

/* 重置導航欄後的間距 */
.navbar + main,
.navbar + * {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

main.container-fluid {
    background: #f8fafc !important;
    min-height: 100vh !important;
    padding: 0 2rem 2rem 2rem !important; /* 移除頂部padding，保留左右下padding */
    margin: 0 !important; /* 強制移除所有margin */
}

/* 移除任何可能的頂部間距 */
.container-fluid > .row:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.container-fluid > .row:first-child > .col:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* 重置第一個元素的頂部間距 */
.container-fluid > *:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* 確保頁面標題區域沒有頂部間距 */
.container-fluid .row.mb-4:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.card.border-left-primary {
    border-left: 4px solid #2563eb !important;
    background: linear-gradient(135deg, #ffffff 0%, #dbeafe 100%) !important;
    border-radius: 1rem !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    transition: all 250ms ease-in-out !important;
}

.card.border-left-success {
    border-left: 4px solid #059669 !important;
    background: linear-gradient(135deg, #ffffff 0%, #d1fae5 100%) !important;
}

.card.border-left-info {
    border-left: 4px solid #0891b2 !important;
    background: linear-gradient(135deg, #ffffff 0%, #cffafe 100%) !important;
}

.card.border-left-warning {
    border-left: 4px solid #d97706 !important;
    background: linear-gradient(135deg, #ffffff 0%, #fef3c7 100%) !important;
}

.card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

.stat-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    color: white !important;
    font-size: 1.5rem !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    transition: all 150ms ease-in-out !important;
}

.card.border-left-success .stat-icon {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
}

.card.border-left-info .stat-icon {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%) !important;
}

.card.border-left-warning .stat-icon {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
}

.card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

.h3 {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
}

.text-xs {
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    border: none !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 150ms ease-in-out !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

.text-primary {
    color: #2563eb !important;
}

.card-body {
    padding: 1.5rem !important;
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-enter {
    animation: fadeInUp 0.5s ease-out !important;
}

.card:nth-child(1) { animation-delay: 0.1s !important; }
.card:nth-child(2) { animation-delay: 0.2s !important; }
.card:nth-child(3) { animation-delay: 0.3s !important; }
.card:nth-child(4) { animation-delay: 0.4s !important; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 頁面標題 -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="h2 mb-2 text-primary">
                        <i class="bi bi-speedometer2" aria-hidden="true"></i>
                        <span>主控台</span>
                    </h1>
                    <p class="text-muted mb-0">歡迎使用CBA受款人資料搜集系統</p>
                    <small class="text-muted">
                        <i class="bi bi-person-circle" aria-hidden="true"></i>
                        {{ user.full_name or user.username }} |
                        <i class="bi bi-clock" aria-hidden="true"></i>
                        {{ current_time.strftime('%Y年%m月%d日 %H:%M') if current_time else '' }}
                    </small>
                </div>
                <div class="d-none d-md-block">
                    <a href="/payee/create" class="btn btn-primary btn-lg hover-lift">
                        <i class="bi bi-plus-circle" aria-hidden="true"></i>
                        <span>新增受款人</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 統計卡片 -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                總受款人數
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.total_payees or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-arrow-up text-success" aria-hidden="true"></i>
                                系統中的所有受款人
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-people" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                活躍受款人
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.active_payees or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-check-circle text-success" aria-hidden="true"></i>
                                資料完整且有效
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-person-check" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                部門數量
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.total_departments or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-building text-info" aria-hidden="true"></i>
                                系統中的部門總數
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-building" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning hover-lift h-100 card-enter">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-2" style="letter-spacing: 0.05em;">
                                本月新增
                            </div>
                            <div class="h3 mb-1 font-weight-bold text-dark">
                                {{ stats.monthly_new or 0 }}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-graph-up text-warning" aria-hidden="true"></i>
                                本月新建立的記錄
                            </small>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-graph-up" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速操作和最近活動 -->
    <div class="row">
        <!-- 快速操作 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning"></i> 快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="/payee/create" class="btn btn-primary btn-lg w-100">
                                <i class="bi bi-person-plus"></i><br>
                                新增受款人
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/payee" class="btn btn-success btn-lg w-100">
                                <i class="bi bi-list-ul"></i><br>
                                受款人列表
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/reports" class="btn btn-info btn-lg w-100">
                                <i class="bi bi-file-earmark-text"></i><br>
                                產生報表
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/export" class="btn btn-warning btn-lg w-100">
                                <i class="bi bi-download"></i><br>
                                匯出資料
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活動 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history"></i> 最近活動
                    </h6>
                </div>
                <div class="card-body">
                    {% if stats.recent_activities %}
                        {% for activity in stats.recent_activities %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                {% if activity.action == 'create' %}
                                    <i class="bi bi-plus-circle text-success"></i>
                                {% elif activity.action == 'update' %}
                                    <i class="bi bi-pencil-square text-primary"></i>
                                {% elif activity.action == 'delete' %}
                                    <i class="bi bi-trash text-danger"></i>
                                {% else %}
                                    <i class="bi bi-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">{{ activity.description }}</div>
                                <div class="small text-muted">{{ activity.created_at }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                            <p class="mt-2">暫無最近活動</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系統狀態 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle"></i> 系統資訊
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>當前用戶：</strong><br>
                            <span class="text-muted">{{ user.full_name or user.username }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>用戶角色：</strong><br>
                            <span class="badge badge-{% if user.role == 'admin' %}danger{% elif user.role == 'global' %}warning{% else %}info{% endif %}">
                                {% if user.role == 'admin' %}管理員{% elif user.role == 'global' %}全域用戶{% else %}一般用戶{% endif %}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>所屬部門：</strong><br>
                            <span class="text-muted">{{ user.department or '未設定' }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>最後登入：</strong><br>
                            <span class="text-muted">{{ user.last_login_at or '首次登入' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

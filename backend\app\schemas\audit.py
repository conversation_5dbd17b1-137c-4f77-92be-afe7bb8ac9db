"""
審計日誌相關的Pydantic模型
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class AuditLogBase(BaseModel):
    """審計日誌基礎模型"""
    action: str
    table_name: str
    record_id: Optional[int] = None
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class AuditLogCreate(AuditLogBase):
    """建立審計日誌請求模型"""
    user_id: int


class AuditLogInDB(AuditLogBase):
    """資料庫中的審計日誌模型"""
    id: int
    user_id: int
    timestamp: datetime
    
    class Config:
        from_attributes = True


class AuditLogResponse(BaseModel):
    """審計日誌回應模型"""
    id: int
    user_id: int
    username: str
    action: str
    table_name: str
    record_id: Optional[int]
    old_values: Optional[Dict[str, Any]]
    new_values: Optional[Dict[str, Any]]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime
    
    class Config:
        from_attributes = True


class AuditLogListResponse(BaseModel):
    """審計日誌列表回應模型"""
    items: List[AuditLogResponse]
    total: int
    page: int
    size: int
    pages: int


class AuditLogQuery(BaseModel):
    """審計日誌查詢參數模型"""
    user_id: Optional[int] = None
    action: Optional[str] = None
    table_name: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    page: int = 1
    size: int = 20 
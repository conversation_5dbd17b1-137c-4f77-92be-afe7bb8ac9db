#!/usr/bin/env python3
"""
測試時區修復功能
"""

import sys
import os
import pytz
from datetime import datetime, timedelta

def test_timezone_conversion():
    """測試時區轉換功能"""
    print("🕒 測試時區轉換功能...")
    
    try:
        # 設定台北時區
        taipei_tz = pytz.timezone('Asia/Taipei')
        utc_tz = pytz.UTC
        
        # 測試當前時間
        now_taipei = datetime.now(taipei_tz)
        now_utc = now_taipei.astimezone(utc_tz).replace(tzinfo=None)
        
        print(f"✅ 台北時間: {now_taipei.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"✅ UTC時間: {now_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 測試月初時間
        month_start = now_taipei.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_start_utc = month_start.astimezone(utc_tz).replace(tzinfo=None)
        
        print(f"✅ 本月開始(台北): {month_start.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"✅ 本月開始(UTC): {month_start_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 測試週開始時間
        week_start = now_taipei - timedelta(days=now_taipei.weekday())
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start_utc = week_start.astimezone(utc_tz).replace(tzinfo=None)
        
        print(f"✅ 本週開始(台北): {week_start.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"✅ 本週開始(UTC): {week_start_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("✅ 時區轉換功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 時區轉換測試失敗: {str(e)}")
        return False

def test_time_format_parsing():
    """測試時間格式解析"""
    print("\n📅 測試時間格式解析...")
    
    try:
        taipei_tz = pytz.timezone('Asia/Taipei')
        
        # 測試不同的時間格式
        test_formats = [
            "2024-12-19T10:30:00Z",
            "2024-12-19T10:30:00+08:00",
            "2024-12-19 10:30:00",
            "2024-12-19T18:30:00.123456+08:00"
        ]
        
        for timestamp_str in test_formats:
            try:
                if 'T' in timestamp_str:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                else:
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                
                formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                print(f"✅ {timestamp_str} -> {formatted_time}")
                
            except Exception as e:
                print(f"⚠️ {timestamp_str} -> 解析失敗: {str(e)}")
        
        print("✅ 時間格式解析功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 時間格式解析測試失敗: {str(e)}")
        return False

def test_sqlite_compatibility():
    """測試SQLite相容性"""
    print("\n🗄️ 測試SQLite相容性...")
    
    try:
        # 模擬SQLite查詢邏輯
        taipei_tz = pytz.timezone('Asia/Taipei')
        now = datetime.now(taipei_tz)
        
        # 轉換為UTC時間（模擬資料庫查詢）
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_start_utc = month_start.astimezone(pytz.UTC).replace(tzinfo=None)
        
        week_start = now - timedelta(days=now.weekday())
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start_utc = week_start.astimezone(pytz.UTC).replace(tzinfo=None)
        
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_start_utc = today_start.astimezone(pytz.UTC).replace(tzinfo=None)
        
        print(f"✅ 本月開始UTC: {month_start_utc}")
        print(f"✅ 本週開始UTC: {week_start_utc}")
        print(f"✅ 今日開始UTC: {today_start_utc}")
        
        # 驗證時間邏輯
        assert month_start_utc <= week_start_utc <= today_start_utc, "時間邏輯錯誤"
        
        print("✅ SQLite相容性測試通過")
        return True
        
    except Exception as e:
        print(f"❌ SQLite相容性測試失敗: {str(e)}")
        return False

def test_monthly_chart_logic():
    """測試月度圖表邏輯"""
    print("\n📊 測試月度圖表邏輯...")
    
    try:
        taipei_tz = pytz.timezone('Asia/Taipei')
        now = datetime.now(taipei_tz)
        period = 6  # 測試6個月
        
        results = []
        
        for i in range(period):
            if now.month - i <= 0:
                year_offset = (i - now.month) // 12 + 1
                month = 12 - ((i - now.month) % 12)
                year = now.year - year_offset
            else:
                month = now.month - i
                year = now.year
            
            month_start = now.replace(year=year, month=month, day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # 計算下個月的開始
            if month == 12:
                next_month_start = month_start.replace(year=year+1, month=1)
            else:
                next_month_start = month_start.replace(month=month+1)
            
            # 轉換為UTC時間
            month_start_utc = month_start.astimezone(pytz.UTC).replace(tzinfo=None)
            next_month_start_utc = next_month_start.astimezone(pytz.UTC).replace(tzinfo=None)
            
            results.append({
                "period": f"{year}-{month:02d}",
                "label": f"{year}年{month}月",
                "start_utc": month_start_utc,
                "end_utc": next_month_start_utc
            })
        
        results.reverse()  # 按時間順序排列
        
        for result in results:
            print(f"✅ {result['label']}: {result['start_utc']} ~ {result['end_utc']}")
        
        print("✅ 月度圖表邏輯測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 月度圖表邏輯測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始測試時區修復功能...\n")
    
    results = []
    
    # 執行各項測試
    results.append(test_timezone_conversion())
    results.append(test_time_format_parsing())
    results.append(test_sqlite_compatibility())
    results.append(test_monthly_chart_logic())
    
    # 總結測試結果
    print(f"\n📊 測試結果總結:")
    print(f"✅ 通過測試: {sum(results)}")
    print(f"❌ 失敗測試: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有時區修復功能測試通過！")
        return 0
    else:
        print("\n⚠️ 部分測試失敗，請檢查修復內容")
        return 1

if __name__ == "__main__":
    sys.exit(main())

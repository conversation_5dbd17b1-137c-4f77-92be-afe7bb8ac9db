/**
 * HTTP請求工具
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import type { ApiResponse } from '@/types/common'

// 創建axios實例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 請求攔截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加認證token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 添加請求時間戳（防止快取）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('請求攔截器錯誤:', error)
    return Promise.reject(error)
  }
)

// 回應攔截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // 檢查業務狀態碼
    if (data.success === false) {
      ElMessage.error(data.message || '請求失敗')
      return Promise.reject(new Error(data.message || '請求失敗'))
    }
    
    return data
  },
  (error) => {
    console.error('回應攔截器錯誤:', error)
    
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // 未授權，清除token並跳轉到登入頁
          const authStore = useAuthStore()
          authStore.logout()
          router.push('/auth/login')
          ElMessage.error('登入已過期，請重新登入')
          break
          
        case 403:
          ElMessage.error('沒有權限執行此操作')
          break
          
        case 404:
          ElMessage.error('請求的資源不存在')
          break
          
        case 422:
          // 表單驗證錯誤
          if (data.detail && Array.isArray(data.detail)) {
            const errors = data.detail.map((item: any) => item.msg).join(', ')
            ElMessage.error(`表單驗證錯誤: ${errors}`)
          } else {
            ElMessage.error(data.message || '表單驗證錯誤')
          }
          break
          
        case 429:
          ElMessage.error('請求過於頻繁，請稍後再試')
          break
          
        case 500:
          ElMessage.error('伺服器內部錯誤')
          break
          
        default:
          ElMessage.error(data?.message || `請求失敗 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('請求超時，請檢查網路連線')
    } else {
      ElMessage.error('網路錯誤，請檢查網路連線')
    }
    
    return Promise.reject(error)
  }
)

// 封裝常用請求方法
export const http = {
  get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return request.get(url, { params })
  },
  
  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return request.post(url, data)
  },
  
  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return request.put(url, data)
  },
  
  patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return request.patch(url, data)
  },
  
  delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return request.delete(url)
  },
  
  upload<T = any>(url: string, formData: FormData): Promise<ApiResponse<T>> {
    return request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 下載檔案
export const downloadFile = async (url: string, filename?: string) => {
  try {
    const response = await request.get(url, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('檔案下載失敗:', error)
    ElMessage.error('檔案下載失敗')
  }
}

export default request

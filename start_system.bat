@echo off
chcp 65001 >nul
echo.
echo ========================================
echo CBA受款人資料搜集系統 - 整合啟動器
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Python，請先安裝Python 3.9或更高版本
    pause
    exit /b 1
)

REM 檢查uv是否安裝
uv --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到uv包管理器，請先安裝uv
    echo 安裝命令: pip install uv
    pause
    exit /b 1
)

echo ✅ 環境檢查通過
echo.

REM 設定環境變數
set API_BASE_URL=http://localhost:8080
set FRONTEND_URL=http://localhost:8501

echo 🚀 啟動整合系統...
echo.

REM 使用Python啟動腳本
python start_integrated_system.py

echo.
echo 系統已停止
pause

# CBA受款人資料搜集系統 - 整合nginx配置
# 統一80埠入口，代理到前端和後端服務

# 後端API伺服器
upstream cba_backend {
    server 127.0.0.1:8080;
}

# 前端Streamlit伺服器
upstream cba_frontend {
    server 127.0.0.1:8501;
}

server {
    listen 80;
    server_name localhost 127.0.0.1;
    
    # 設定客戶端最大請求大小
    client_max_body_size 10M;
    
    # 安全標頭
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # 前端應用 (Streamlit) - 根路徑
    location / {
        proxy_pass http://cba_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 86400;
        
        # Streamlit特殊設定
        proxy_buffering off;
        proxy_redirect off;
    }
    
    # 後端API
    location /api/ {
        proxy_pass http://cba_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API安全設定
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康檢查端點 (直接代理到後端)
    location /health {
        proxy_pass http://cba_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    

    
    # Streamlit靜態檔案
    location /_stcore/static/ {
        proxy_pass http://cba_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 靜態檔案快取
        expires 1h;
        add_header Cache-Control "public, immutable";
    }
    
    # 一般靜態檔案快取
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://cba_frontend;
        proxy_set_header Host $host;
        
        # 靜態檔案快取
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全性：隱藏nginx版本
    server_tokens off;
    
    # 日誌設定
    access_log /var/log/nginx/cba-system.access.log;
    error_log /var/log/nginx/cba-system.error.log;
    
    # 錯誤頁面
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 開發環境的簡化配置（不使用SSL）
server {
    listen 8888;
    server_name localhost 127.0.0.1;
    
    # 簡單的狀態頁面
    location / {
        return 200 "CBA受款人資料搜集系統整合服務運行中\n主要服務: http://localhost:80\n";
        add_header Content-Type text/plain;
    }
    
    # 服務狀態檢查
    location /status {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}

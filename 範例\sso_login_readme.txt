
### 1.2 功能概述

- 使用者透過 SSO 單一登入系統進行身份驗證
- 獲取 OAuth 2.0 令牌以訪問受保護的 API 資源
- 呼叫 SOAP 服務獲取待辦事項和使用者資訊
- 提供 Web 介面顯示使用者資訊和待辦事項

**功能**:獲取用戶信息
**關鍵程式碼**:
    artifact = request.args.get('SAMLart') or request.args.get('ssoToken1')
    client = "https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL"
    result = client.service.getUserProfile(artifact)
    print(f"result: {result}")
    logging.info(f"SOAP 呼叫結果: {result}")
    
    if result:  # 如果成功獲取用戶信息
        # 解析 XML 格式的 result
        from xml.etree import ElementTree as ET
        root = ET.fromstring(result)
        if root.tag == 'Error':
            user_info = {
                '帳號': '',
                '姓名': '',
                'source_org_no': ''
            }
        else:
            # 讀取帳號及姓名
            account = root.find('帳號').text
            name = root.find('姓名').text
            org_info = f"{root.find('機關名稱').text}:{root.find('機關代碼').text}:{root.find('單位代碼').text}"
            
            user_info = {
                '帳號': account,
                '姓名': name,
                'source_org_no': org_info,
                'ssoToken1': artifact   
            }


**功能**:取得結報系統待辦件數
**關鍵程式碼**:
def get_access_token():
token_url = "https://efs.pthg.gov.tw/GTHI/oauth2/token"
    client_id = "a8e50c0e-74da-4f98-959b-06827dc95b55"
    client_secret = "BMhF2OFZs0atFqRYFvpfvx0oKLymiZAf"
    
    # 使用 Basic Authentication
    credentials = f"{client_id}:{client_secret}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    
    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    # 請求參數
    payload = {
        'grant_type': 'client_credentials'
    }
    
    # 發送請求
    response = requests.post(token_url, headers=headers, data=payload, verify=False)
    token_data = response.json()
    access_token = token_data.get('access_token')

def get_todo_list(access_token, account="001401"):
    """使用 access token 讀取待辦事項"""
    api_url = f"https://efs.pthg.gov.tw/GTHI/api/to-do/find?account={account}"
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
        response = requests.get(api_url, headers=headers)


**功能**: 取得差勤待辦件數
**關鍵程式碼**:
    session = Session()
    
    # 建立 SOAP 客戶端
    client = Client(
        'https://otvmtest.pthg.gov.tw/PLM_AP/ws/syncWS.asmx?WSDL',
        transport=Transport(session=session)
    )
    account_map = "000001"  # 替換為實際的賬號
  
    print("\n=== 呼叫 GetCntWaitJson ===")
    result = client.service.GetCntWaitJson(account_map)
    print("GetCntWaitJson 結果:", result)

            client = Client(app.config['SSO_CONFIG']['soap_ws_url'])
            result = client.service.getUserProfile(artifact)
            print(f"result: {result}")
            logging.info(f"SOAP 呼叫結果: {result}")
            
            if result:  # 如果成功獲取用戶信息
                # 解析 XML 格式的 result
                from xml.etree import ElementTree as ET
                root = ET.fromstring(result)
                if root.tag == 'Error':
                    user_info = {
                        '帳號': '',
                        '姓名': '',
                        'source_org_no': ''
                    }
                else:
                    # 讀取帳號及姓名
                    account = root.find('帳號').text
                    name = root.find('姓名').text
                    org_info = f"{root.find('機關名稱').text}:{root.find('機關代碼').text}:{root.find('單位代碼').text}"
                    
                    user_info = {
                        '帳號': account,
                        '姓名': name,
                        'source_org_no': org_info,
                        'ssoToken1': artifact   
                    }


### 5.2 外部 API

 結報 OAuth 令牌 : `https://efs.pthg.gov.tw/GTHI/oauth2/token`
 結報待辦： `https://efs.pthg.gov.tw/GTHI/api/to-do/find` 
 差勤待辦（測試機）： `https://otvmtest.pthg.gov.tw/PLM_AP/ws/syncWS.asmx` 


**版本**: 1.0.0  
**最後更新**: 2023-11-15  
**作者**: 系統開發團隊

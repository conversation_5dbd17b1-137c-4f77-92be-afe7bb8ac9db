"""
CBA人員資料調查系統 - 第三階段系統檢查
檢查系統配置、依賴項和環境設定
"""

import os
import sys
import subprocess
import importlib
import sqlite3
from pathlib import Path
import json
import requests
from datetime import datetime

class SystemCheckPhase3:
    """第三階段系統檢查"""
    
    def __init__(self):
        self.check_results = []
        self.system_info = {}
        
    def check_python_version(self):
        """檢查Python版本"""
        print("=== 檢查 Python 版本 ===")
        
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        
        if version.major == 3 and version.minor >= 8:
            print(f"✓ Python版本: {version_str} (符合要求)")
            self.check_results.append(True)
        else:
            print(f"✗ Python版本: {version_str} (需要 Python 3.8+)")
            self.check_results.append(False)
        
        self.system_info["python_version"] = version_str
    
    def check_required_packages(self):
        """檢查必要的Python套件"""
        print("=== 檢查必要套件 ===")
        
        required_packages = [
            "fastapi",
            "uvicorn",
            "streamlit",
            "sqlalchemy",
            "pydantic",
            "python-jwt",
            "requests",
            "pytest",
            "cryptography"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                importlib.import_module(package.replace("-", "_"))
                print(f"✓ {package}: 已安裝")
            except ImportError:
                print(f"✗ {package}: 未安裝")
                missing_packages.append(package)
        
        if not missing_packages:
            self.check_results.append(True)
        else:
            self.check_results.append(False)
            print(f"缺少套件: {', '.join(missing_packages)}")
        
        self.system_info["missing_packages"] = missing_packages
    
    def check_file_structure(self):
        """檢查檔案結構"""
        print("=== 檢查檔案結構 ===")
        
        required_files = [
            "backend/main.py",
            "backend/app/api/v1/auth.py",
            "backend/app/api/v1/personal_data.py",
            "backend/app/core/config.py",
            "backend/app/core/sso.py",
            "backend/app/models/database.py",
            "backend/app/utils/jwt_auth.py",
            "backend/app/utils/encryption.py",
            "frontend/main.py",
            "frontend/utils/api_client.py",
            "frontend/utils/auth_manager.py",
            "frontend/components/personal_data_form.py",
            "frontend/components/data_query.py"
        ]
        
        missing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path}: 存在")
            else:
                print(f"✗ {file_path}: 不存在")
                missing_files.append(file_path)
        
        if not missing_files:
            self.check_results.append(True)
        else:
            self.check_results.append(False)
        
        self.system_info["missing_files"] = missing_files
    
    def check_database_files(self):
        """檢查資料庫檔案"""
        print("=== 檢查資料庫檔案 ===")
        
        db_files = [
            "backend/database/cba_personal_data.db",
            "database/schema.sql"
        ]
        
        db_status = {}
        
        for db_file in db_files:
            if os.path.exists(db_file):
                print(f"✓ {db_file}: 存在")
                db_status[db_file] = "exists"
                
                # 如果是SQLite資料庫，檢查是否可以連接
                if db_file.endswith(".db"):
                    try:
                        conn = sqlite3.connect(db_file)
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        conn.close()
                        
                        table_names = [table[0] for table in tables]
                        print(f"  資料表: {', '.join(table_names)}")
                        db_status[db_file] = "connected"
                    except Exception as e:
                        print(f"  ✗ 資料庫連接失敗: {e}")
                        db_status[db_file] = "connection_failed"
            else:
                print(f"✗ {db_file}: 不存在")
                db_status[db_file] = "missing"
        
        # 如果主要資料庫存在且可連接，則通過
        main_db = "backend/database/cba_personal_data.db"
        if db_status.get(main_db) == "connected":
            self.check_results.append(True)
        else:
            self.check_results.append(False)
        
        self.system_info["database_status"] = db_status
    
    def check_configuration(self):
        """檢查系統配置"""
        print("=== 檢查系統配置 ===")
        
        config_checks = []
        
        # 檢查環境變數
        env_vars = [
            "JWT_SECRET_KEY",
            "ALGORITHM",
            "ACCESS_TOKEN_EXPIRE_MINUTES"
        ]
        
        for var in env_vars:
            if os.getenv(var):
                print(f"✓ 環境變數 {var}: 已設定")
                config_checks.append(True)
            else:
                print(f"! 環境變數 {var}: 未設定 (將使用預設值)")
                config_checks.append(True)  # 環境變數未設定也OK，會使用預設值
        
        # 檢查配置檔案
        config_file = "backend/app/core/config.py"
        if os.path.exists(config_file):
            print(f"✓ 配置檔案: {config_file}")
            config_checks.append(True)
        else:
            print(f"✗ 配置檔案: {config_file} 不存在")
            config_checks.append(False)
        
        self.check_results.append(all(config_checks))
        self.system_info["configuration_status"] = "ok" if all(config_checks) else "failed"
    
    def check_port_availability(self):
        """檢查埠號可用性"""
        print("=== 檢查埠號可用性 ===")
        
        ports = [8000, 8501]  # 後端和前端預設埠號
        port_status = {}
        
        for port in ports:
            try:
                # 嘗試連接到埠號
                response = requests.get(f"http://localhost:{port}", timeout=2)
                print(f"! 埠號 {port}: 已被使用")
                port_status[port] = "in_use"
            except requests.exceptions.ConnectionError:
                print(f"✓ 埠號 {port}: 可用")
                port_status[port] = "available"
            except Exception as e:
                print(f"? 埠號 {port}: 檢查失敗 - {e}")
                port_status[port] = "unknown"
        
        self.check_results.append(True)  # 埠號被使用不算錯誤
        self.system_info["port_status"] = port_status
    
    def check_testing_environment(self):
        """檢查測試環境"""
        print("=== 檢查測試環境 ===")
        
        test_files = [
            "backend/tests/test_phase3.py",
            "test_e2e_phase3.py",
            "第三階段測試方案.md"
        ]
        
        test_status = {}
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"✓ {test_file}: 存在")
                test_status[test_file] = "exists"
            else:
                print(f"✗ {test_file}: 不存在")
                test_status[test_file] = "missing"
        
        # 檢查pytest是否可用
        try:
            result = subprocess.run(["pytest", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ pytest: 可用")
                test_status["pytest"] = "available"
            else:
                print("✗ pytest: 不可用")
                test_status["pytest"] = "unavailable"
        except Exception as e:
            print(f"✗ pytest: 檢查失敗 - {e}")
            test_status["pytest"] = "error"
        
        # 如果主要測試檔案存在，則通過
        main_tests_exist = all(os.path.exists(f) for f in test_files[:2])
        self.check_results.append(main_tests_exist)
        
        self.system_info["testing_status"] = test_status
    
    def generate_system_report(self):
        """產生系統檢查報告"""
        print("=" * 60)
        print("第三階段系統檢查報告")
        print("=" * 60)
        
        check_names = [
            "Python版本",
            "必要套件",
            "檔案結構",
            "資料庫檔案",
            "系統配置",
            "埠號可用性",
            "測試環境"
        ]
        
        passed = 0
        total = len(self.check_results)
        
        for i, result in enumerate(self.check_results):
            if i < len(check_names):
                status = "✓ 通過" if result else "✗ 失敗"
                print(f"{check_names[i]}: {status}")
                if result:
                    passed += 1
        
        print("-" * 60)
        print(f"檢查結果: {passed}/{total} 項檢查通過")
        
        if passed == total:
            print("🎉 系統檢查全部通過！可以開始測試。")
            system_ready = True
        else:
            print("⚠️  系統檢查發現問題，請修正後再進行測試。")
            system_ready = False
        
        # 儲存檢查報告
        report_data = {
            "check_date": datetime.now().isoformat(),
            "total_checks": total,
            "passed_checks": passed,
            "system_ready": system_ready,
            "check_results": dict(zip(check_names, self.check_results)),
            "system_info": self.system_info
        }
        
        with open("phase3_system_check_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"系統檢查報告已儲存至: phase3_system_check_report.json")
        return system_ready
    
    def run_system_check(self):
        """執行系統檢查"""
        print("開始執行第三階段系統檢查...")
        print("=" * 60)
        
        # 執行各項檢查
        self.check_python_version()
        self.check_required_packages()
        self.check_file_structure()
        self.check_database_files()
        self.check_configuration()
        self.check_port_availability()
        self.check_testing_environment()
        
        # 產生報告
        return self.generate_system_report()

def main():
    """主執行函數"""
    checker = SystemCheckPhase3()
    system_ready = checker.run_system_check()
    
    if system_ready:
        print("\n系統檢查完成，可以開始執行測試。")
        print("執行測試的指令：")
        print("  後端測試: cd backend && python -m pytest tests/test_phase3.py -v")
        print("  端到端測試: python test_e2e_phase3.py")
    else:
        print("\n系統檢查發現問題，請修正後再執行測試。")
    
    return system_ready

if __name__ == "__main__":
    main() 
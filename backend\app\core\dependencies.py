"""
FastAPI依賴注入函數
"""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.models.database import get_db
from app.models.user import User
from app.utils.jwt_auth import verify_token, decode_token
from app.core.exceptions import (
    AuthenticationError, PermissionDeniedError, 
    InvalidTokenError, TokenExpiredError
)


# JWT Bearer認證
security = HTTPBearer()


def get_current_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """取得當前認證用戶"""
    
    try:
        # 解碼Token獲取payload
        payload = decode_token(credentials.credentials)
        user_id = payload.get("user_id")
        
        if user_id is None:
            raise InvalidTokenError("Token中缺少用戶ID")
        
        # 查詢用戶
        user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
        if user is None:
            raise AuthenticationError("用戶不存在或已停用")
        
        return user
        
    except TokenExpiredError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token已過期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except InvalidTokenError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="身份驗證失敗",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """取得當前活躍用戶"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用戶帳號已停用"
        )
    return current_user


def require_permissions(*required_permissions: str):
    """權限檢查裝飾器"""
    
    def permission_checker(current_user: User = Depends(get_current_active_user)) -> User:
        user_permissions = set()
        
        # 收集用戶所有權限
        for role in current_user.roles:
            for permission in role.permissions:
                user_permissions.add(permission.name)
        
        # 檢查是否擁有所需權限
        for required_permission in required_permissions:
            if required_permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"權限不足，需要權限：{required_permission}"
                )
        
        return current_user
    
    return permission_checker


def require_roles(*required_roles: str):
    """角色檢查裝飾器"""
    
    def role_checker(current_user: User = Depends(get_current_active_user)) -> User:
        user_roles = {role.name for role in current_user.roles}
        
        # 檢查是否擁有所需角色
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"權限不足，需要角色：{', '.join(required_roles)}"
            )
        
        return current_user
    
    return role_checker


def get_client_info(request: Request) -> dict:
    """取得客戶端資訊"""
    return {
        "ip_address": request.client.host,
        "user_agent": request.headers.get("user-agent", ""),
        "forwarded_for": request.headers.get("x-forwarded-for"),
        "real_ip": request.headers.get("x-real-ip")
    }


# 預定義的權限檢查器
require_general_permission = require_permissions("CREATE_PAYEE_DATA", "READ_PAYEE_DATA")
require_global_permission = require_permissions("READ_ALL_DATA")
require_admin_permission = require_roles("admin")
require_global_or_admin = require_roles("global", "admin") 
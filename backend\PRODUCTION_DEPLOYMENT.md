# CBA人員資料調查系統 - 生產環境部署指南

## 📋 **目錄**
- [系統需求](#系統需求)
- [部署前準備](#部署前準備)
- [環境配置](#環境配置)
- [安全性設定](#安全性設定)
- [資料庫設定](#資料庫設定)
- [反向代理設定](#反向代理設定)
- [SSL憑證設定](#ssl憑證設定)
- [監控與日誌](#監控與日誌)
- [備份策略](#備份策略)
- [故障排除](#故障排除)

---

## 🖥️ **系統需求**

### 最低硬體需求
- **CPU**: 2核心或以上
- **記憶體**: 4GB RAM或以上
- **硬碟**: 50GB可用空間或以上
- **網路**: 穩定的網際網路連線

### 建議硬體需求
- **CPU**: 4核心或以上
- **記憶體**: 8GB RAM或以上
- **硬碟**: 100GB SSD或以上
- **網路**: 高速穩定連線

### 軟體需求
- **作業系統**: Ubuntu 20.04 LTS / CentOS 8 / RHEL 8 或以上
- **Python**: 3.9 或以上
- **資料庫**: SQLite 3.x (預設) 或 PostgreSQL 13+ (建議)
- **反向代理**: Nginx 1.18+ 或 Apache 2.4+
- **程序管理**: Supervisor 或 systemd

---

## 🚀 **部署前準備**

### 1. 建立專用用戶
```bash
# 建立系統用戶
sudo useradd -r -s /bin/bash -m -d /home/<USER>

# 建立必要目錄
sudo mkdir -p /opt/cba-system
sudo chown cba-app:cba-app /opt/cba-system
```

### 2. 安裝系統依賴
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3.9 python3.9-venv python3-pip nginx supervisor
sudo apt install -y build-essential libssl-dev libffi-dev python3-dev

# CentOS/RHEL
sudo yum update -y
sudo yum install -y python39 python39-pip nginx supervisor
sudo yum groupinstall -y "Development Tools"
```

### 3. 安裝UV包管理器
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc
```

---

## ⚙️ **環境配置**

### 1. 建立生產環境配置檔案

建立 `/opt/cba-system/.env.production`：

```bash
# ===========================================
# 生產環境基本設定
# ===========================================
ENVIRONMENT=production
DEBUG=false
HOST=127.0.0.1
PORT=8000

# ===========================================
# 資料庫設定
# ===========================================
# SQLite (適用於小型部署)
DATABASE_URL=sqlite:///./database/cba_personal_data.db

# PostgreSQL (建議用於大型部署)
# DATABASE_URL=postgresql://cba_user:secure_password@localhost:5432/cba_system

# ===========================================
# 安全性設定
# ===========================================
# 請產生強隨機密鑰，至少32字符
JWT_SECRET_KEY=REPLACE_WITH_RANDOM_32_CHAR_STRING
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=240

# 請產生強隨機加密密鑰，至少32字符  
ENCRYPTION_KEY=REPLACE_WITH_RANDOM_32_CHAR_ENCRYPTION_KEY

# ===========================================
# SOAP SSO 設定
# ===========================================
# 政府單一登入系統
SSO_SOAP_WS_URL=https://odcsso.pthg.gov.tw/SS/SS0/CommonWebService.asmx?WSDL

# ===========================================
# 前端設定
# ===========================================
FRONTEND_URL=https://your-domain.com

# ===========================================
# 審計與監控設定
# ===========================================
ENABLE_AUDIT_LOG=true
AUDIT_LOG_RETENTION_DAYS=1095

# ===========================================
# 安全性政策
# ===========================================
MIN_PASSWORD_LENGTH=12
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=true

# ===========================================
# API 限制設定
# ===========================================
API_RATE_LIMIT=60
MAX_QUERY_RESULTS=500
```

### 2. 產生安全密鑰
```bash
# 產生JWT密鑰
python3 -c "import secrets; print(f'JWT_SECRET_KEY={secrets.token_urlsafe(32)}')"

# 產生加密密鑰
python3 -c "import secrets; print(f'ENCRYPTION_KEY={secrets.token_urlsafe(32)}')"
```

---

## 🔒 **安全性設定**

### 1. 檔案權限設定
```bash
# 設定檔案擁有者
sudo chown -R cba-app:cba-app /opt/cba-system

# 設定適當權限
sudo chmod 700 /opt/cba-system
sudo chmod 600 /opt/cba-system/.env.production
sudo chmod 755 /opt/cba-system/main.py
```

### 2. 防火牆設定
```bash
# Ubuntu (UFW)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw --force enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. SELinux設定 (CentOS/RHEL)
```bash
# 如果啟用SELinux，設定適當的上下文
sudo setsebool -P httpd_can_network_connect 1
sudo semanage fcontext -a -t httpd_exec_t "/opt/cba-system(/.*)?"
sudo restorecon -Rv /opt/cba-system/
```

---

## 🗄️ **資料庫設定**

### SQLite設定 (預設)
```bash
# 建立資料庫目錄
sudo mkdir -p /opt/cba-system/database
sudo chown cba-app:cba-app /opt/cba-system/database
sudo chmod 755 /opt/cba-system/database
```

### PostgreSQL設定 (建議)
```bash
# 安裝PostgreSQL
sudo apt install -y postgresql postgresql-contrib  # Ubuntu
# sudo yum install -y postgresql-server postgresql-contrib  # CentOS

# 建立資料庫和用戶
sudo -u postgres psql << EOF
CREATE DATABASE cba_system;
CREATE USER cba_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE cba_system TO cba_user;
\q
EOF

# 設定PostgreSQL連線
echo "host    cba_system    cba_user    127.0.0.1/32    md5" | sudo tee -a /etc/postgresql/*/main/pg_hba.conf
sudo systemctl restart postgresql
```

---

## 🌐 **反向代理設定**

### Nginx配置
建立 `/etc/nginx/sites-available/cba-system`：

```nginx
# 後端API伺服器
upstream cba_backend {
    server 127.0.0.1:8000;
}

# 前端Streamlit伺服器
upstream cba_frontend {
    server 127.0.0.1:8501;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 強制使用HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL憑證設定
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL安全設定
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    
    # 安全標頭
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'";
    
    # 前端應用 (Streamlit)
    location / {
        proxy_pass http://cba_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 後端API
    location /api/ {
        proxy_pass http://cba_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API安全設定
        client_max_body_size 10M;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 靜態檔案快取
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全性：隱藏Nginx版本
    server_tokens off;
    
    # 日誌設定
    access_log /var/log/nginx/cba-system.access.log;
    error_log /var/log/nginx/cba-system.error.log;
}
```

啟用站點：
```bash
sudo ln -s /etc/nginx/sites-available/cba-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔐 **SSL憑證設定**

### 使用Let's Encrypt (免費)
```bash
# 安裝Certbot
sudo apt install -y certbot python3-certbot-nginx  # Ubuntu
# sudo yum install -y certbot python3-certbot-nginx  # CentOS

# 取得憑證
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 設定自動更新
sudo crontab -e
# 加入以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 使用自簽憑證 (測試用)
```bash
sudo mkdir -p /etc/ssl/{certs,private}
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/your-domain.key \
    -out /etc/ssl/certs/your-domain.crt \
    -subj "/C=TW/ST=Taiwan/L=Pingtung/O=CBA/OU=IT/CN=your-domain.com"
```

---

## 📊 **監控與日誌**

### 1. 系統服務設定

建立 `/etc/systemd/system/cba-backend.service`：
```ini
[Unit]
Description=CBA人員資料調查系統 - 後端API
After=network.target

[Service]
Type=simple
User=cba-app
Group=cba-app
WorkingDirectory=/opt/cba-system/backend
Environment=PATH=/opt/cba-system/.venv/bin
EnvironmentFile=/opt/cba-system/.env.production
ExecStart=/opt/cba-system/.venv/bin/uvicorn main:app --host 127.0.0.1 --port 8000 --workers 4
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

建立 `/etc/systemd/system/cba-frontend.service`：
```ini
[Unit]
Description=CBA人員資料調查系統 - 前端介面
After=network.target

[Service]
Type=simple
User=cba-app
Group=cba-app
WorkingDirectory=/opt/cba-system/frontend
Environment=PATH=/opt/cba-system/.venv/bin
EnvironmentFile=/opt/cba-system/.env.production
ExecStart=/opt/cba-system/.venv/bin/streamlit run main.py --server.port 8501 --server.address 127.0.0.1
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

啟用服務：
```bash
sudo systemctl daemon-reload
sudo systemctl enable cba-backend cba-frontend
sudo systemctl start cba-backend cba-frontend
```

### 2. 日誌輪替設定

建立 `/etc/logrotate.d/cba-system`：
```
/var/log/nginx/cba-system.*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}

/opt/cba-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cba-app cba-app
    postrotate
        systemctl restart cba-backend cba-frontend
    endscript
}
```

---

## 💾 **備份策略**

### 1. 資料庫備份腳本

建立 `/opt/cba-system/scripts/backup.sh`：
```bash
#!/bin/bash

BACKUP_DIR="/opt/cba-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASE_FILE="/opt/cba-system/database/cba_personal_data.db"

# 建立備份目錄
mkdir -p $BACKUP_DIR

# SQLite備份
if [ -f "$DATABASE_FILE" ]; then
    cp "$DATABASE_FILE" "$BACKUP_DIR/cba_database_$DATE.db"
    echo "資料庫備份完成: $BACKUP_DIR/cba_database_$DATE.db"
fi

# 清理超過30天的備份
find $BACKUP_DIR -name "cba_database_*.db" -mtime +30 -delete

# PostgreSQL備份 (如果使用)
# pg_dump -h localhost -U cba_user -d cba_system > "$BACKUP_DIR/cba_database_$DATE.sql"
```

設定定時備份：
```bash
sudo chmod +x /opt/cba-system/scripts/backup.sh
sudo crontab -e
# 加入以下行（每日3:00備份）：
# 0 3 * * * /opt/cba-system/scripts/backup.sh
```

---

## 🔧 **故障排除**

### 常見問題診斷

1. **檢查服務狀態**
```bash
sudo systemctl status cba-backend cba-frontend nginx
sudo journalctl -u cba-backend -f
sudo journalctl -u cba-frontend -f
```

2. **檢查端口佔用**
```bash
sudo netstat -tlnp | grep -E ':(8000|8501|80|443)'
```

3. **檢查日誌**
```bash
sudo tail -f /var/log/nginx/cba-system.error.log
sudo tail -f /opt/cba-system/logs/app.log
```

4. **檢查檔案權限**
```bash
ls -la /opt/cba-system/
ls -la /opt/cba-system/.env.production
```

### 效能調校

1. **後端調校**
```bash
# 調整worker數量（根據CPU核心數）
# 在服務設定中修改：--workers 4
```

2. **Nginx調校**
```nginx
# 在 /etc/nginx/nginx.conf 中調整
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
client_max_body_size 10M;
```

---

## 📞 **支援聯絡**

如遇到部署問題，請提供以下資訊：
- 作業系統版本
- Python版本
- 錯誤日誌內容
- 系統配置資訊

---

**注意事項**：
- 定期更新系統和依賴套件
- 監控系統資源使用情況
- 定期檢查備份完整性
- 遵循資安最佳實務 
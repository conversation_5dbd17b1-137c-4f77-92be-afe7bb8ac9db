"""
自訂例外類別
"""
from typing import Optional, Any, Dict


class CBAException(Exception):
    """CBA系統基礎例外類別"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)


class ValidationError(CBAException):
    """資料驗證錯誤"""
    
    def __init__(self, message: str, field: Optional[str] = None):
        super().__init__(message, "VALIDATION_ERROR", {"field": field})


class AuthenticationError(CBAException):
    """身份驗證錯誤"""
    
    def __init__(self, message: str = "身份驗證失敗"):
        super().__init__(message, "AUTHENTICATION_ERROR")


class PermissionDeniedError(CBAException):
    """權限不足錯誤"""
    
    def __init__(self, message: str = "權限不足"):
        super().__init__(message, "PERMISSION_DENIED")


class PayeeDataNotFoundError(CBAException):
    """受款人資料未找到錯誤"""
    
    def __init__(self, message: str = "找不到指定的受款人資料"):
        super().__init__(message, "PAYEE_DATA_NOT_FOUND")


class DuplicateIdNumberError(CBAException):
    """身分證字號重複錯誤"""
    
    def __init__(self, message: str = "身分證字號已存在"):
        super().__init__(message, "DUPLICATE_ID_NUMBER")


class UserNotFoundError(CBAException):
    """用戶未找到錯誤"""
    
    def __init__(self, message: str = "找不到指定的用戶"):
        super().__init__(message, "USER_NOT_FOUND")


class InvalidTokenError(CBAException):
    """無效Token錯誤"""
    
    def __init__(self, message: str = "無效的Token"):
        super().__init__(message, "INVALID_TOKEN")


class TokenExpiredError(CBAException):
    """Token過期錯誤"""
    
    def __init__(self, message: str = "Token已過期"):
        super().__init__(message, "TOKEN_EXPIRED")


class DatabaseError(CBAException):
    """資料庫錯誤"""
    
    def __init__(self, message: str = "資料庫操作失敗"):
        super().__init__(message, "DATABASE_ERROR")


class EncryptionError(CBAException):
    """加密錯誤"""
    
    def __init__(self, message: str = "加密處理失敗"):
        super().__init__(message, "ENCRYPTION_ERROR")


class SSOError(CBAException):
    """SSO錯誤"""
    
    def __init__(self, message: str = "單一簽入驗證失敗"):
        super().__init__(message, "SSO_ERROR") 
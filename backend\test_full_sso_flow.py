#!/usr/bin/env python3
"""
測試完整的 SSO 登入流程
"""

import requests
import json
import time
import subprocess
import sys
import os
import signal
from datetime import datetime

def test_backend_health():
    """檢查後端服務是否正常運行"""
    print("🏥 檢查後端服務健康狀態")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 後端服務正常運行")
            return True
        else:
            print(f"❌ 後端服務回應異常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 無法連接到後端服務: {e}")
        return False

def test_sso_login():
    """測試 SSO 登入端點"""
    print("\n🔑 測試 SSO 登入端點")
    print("=" * 50)
    
    # 測試 Token
    test_token = "cfe66b593ddd010e2d42205e9f1f67cb17b4c7b1a4b08ba5a253473665b6d956"
    
    try:
        # 呼叫 SSO 登入端點
        url = f"http://localhost:8080/api/v1/auth/sso_login?ssoToken1={test_token}"
        print(f"📡 呼叫 SSO 登入端點: {url}")
        
        response = requests.get(url, allow_redirects=False, timeout=30)
        
        print(f"📊 回應狀態碼: {response.status_code}")
        print(f"📋 回應標頭: {dict(response.headers)}")
        
        if response.status_code == 302:
            location = response.headers.get('Location')
            print(f"🔄 重導向到: {location}")
            
            # 檢查重導向是否為前端應用
            if "localhost:8501" in location and "token=" in location:
                print("✅ SSO 登入成功！正確重導向到前端應用")
                return True
            elif "sso=success" in location:
                print("⚠️  SSO 登入成功，但重導向到舊的 URL")
                return True
            else:
                print("❌ SSO 登入失敗 - 重導向到錯誤頁面")
                return False
        else:
            print(f"❌ 未期望的狀態碼: {response.status_code}")
            print(f"📄 回應內容: {response.text[:300]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 請求失敗: {e}")
        return False

def test_auth_endpoint():
    """測試認證端點"""
    print("\n🔐 測試認證端點")
    print("=" * 50)
    
    try:
        # 先測試健康檢查
        url = "http://localhost:8080/api/v1/auth/health"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print("✅ 認證端點可達")
            return True
        else:
            print(f"❌ 認證端點異常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 認證端點無法連接: {e}")
        return False

def start_backend_service():
    """啟動後端服務"""
    print("\n🚀 啟動後端服務")
    print("=" * 50)
    
    try:
        # 檢查是否已經在運行
        if test_backend_health():
            print("✅ 後端服務已在運行")
            return True
        
        print("📡 啟動後端服務...")
        
        # 啟動後端服務
        cmd = [sys.executable, "main.py"]
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=os.getcwd()
        )
        
        # 等待服務啟動
        print("⏳ 等待服務啟動...")
        for i in range(30):  # 等待最多30秒
            time.sleep(1)
            if test_backend_health():
                print(f"✅ 後端服務啟動成功 (耗時 {i+1} 秒)")
                return proc
        
        print("❌ 後端服務啟動超時")
        proc.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 啟動後端服務失敗: {e}")
        return None

def main():
    """主要測試函數"""
    print("🔬 完整 SSO 登入流程測試")
    print("=" * 80)
    print(f"🕐 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步驟 1: 檢查/啟動後端服務
    backend_proc = None
    
    if not test_backend_health():
        print("⚠️  後端服務未運行，嘗試啟動...")
        backend_proc = start_backend_service()
        if not backend_proc:
            print("❌ 無法啟動後端服務，測試中止")
            return False
    
    try:
        # 步驟 2: 測試認證端點
        auth_ok = test_auth_endpoint()
        
        # 步驟 3: 測試 SSO 登入
        sso_ok = test_sso_login()
        
        # 總結
        print("\n" + "=" * 80)
        print("📊 測試結果:")
        print(f"  - 後端服務: {'✅ 正常' if test_backend_health() else '❌ 異常'}")
        print(f"  - 認證端點: {'✅ 正常' if auth_ok else '❌ 異常'}")
        print(f"  - SSO 登入: {'✅ 成功' if sso_ok else '❌ 失敗'}")
        
        if sso_ok:
            print("🎉 SSO 登入測試通過！")
            return True
        else:
            print("❌ SSO 登入測試失敗")
            return False
            
    finally:
        # 清理：如果我們啟動了後端服務，現在關閉它
        if backend_proc:
            print("\n🧹 清理後端服務...")
            backend_proc.terminate()
            backend_proc.wait()
            print("✅ 後端服務已關閉")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
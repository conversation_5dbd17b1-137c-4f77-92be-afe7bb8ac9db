#!/usr/bin/env python3
"""
受款人資料表結構遷移腳本
將舊的payee_data表結構更新為新的結構
"""

import uuid
from datetime import datetime
from sqlalchemy import text, create_engine
from app.models.database import engine, get_db
from app.models.payee_data import PayeeData
from app.models.user import User

def migrate_payee_data():
    """遷移受款人資料表"""
    print("開始遷移受款人資料表...")
    
    with engine.connect() as conn:
        # 開始事務
        trans = conn.begin()
        
        try:
            # 1. 備份現有數據
            print("1. 備份現有數據...")
            result = conn.execute(text("""
                SELECT id, name, encrypted_id_number, address, notes, 
                       created_by_user_id, created_by_department, is_active, 
                       created_at, updated_at
                FROM payee_data
            """))
            old_data = result.fetchall()
            print(f"   找到 {len(old_data)} 筆記錄")
            
            # 2. 重命名舊表
            print("2. 重命名舊表...")
            conn.execute(text("ALTER TABLE payee_data RENAME TO payee_data_backup"))
            
            # 3. 創建新表結構
            print("3. 創建新表結構...")
            conn.execute(text("""
                CREATE TABLE payee_data (
                    id VARCHAR(36) PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    id_number VARCHAR(20) NOT NULL UNIQUE,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    bank_name VARCHAR(100),
                    bank_code VARCHAR(10),
                    account_number VARCHAR(50),
                    account_name VARCHAR(100),
                    department VARCHAR(100),
                    notes TEXT,
                    created_by VARCHAR(36) NOT NULL,
                    updated_by VARCHAR(36),
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (id),
                    FOREIGN KEY (updated_by) REFERENCES users (id)
                )
            """))
            
            # 4. 創建索引
            print("4. 創建索引...")
            try:
                conn.execute(text("CREATE INDEX IF NOT EXISTS ix_payee_data_id ON payee_data (id)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS ix_payee_data_name ON payee_data (name)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS ix_payee_data_id_number ON payee_data (id_number)"))
            except Exception as e:
                print(f"   索引創建警告: {e}")
                # 繼續執行，索引可能已存在
            
            # 5. 獲取admin用戶ID
            result = conn.execute(text("SELECT id FROM users WHERE username = 'admin'"))
            admin_user = result.fetchone()
            if not admin_user:
                raise Exception("找不到admin用戶")
            admin_id = admin_user[0]
            
            # 6. 遷移數據
            print("5. 遷移數據...")
            for i, row in enumerate(old_data):
                # 生成新的UUID
                new_id = str(uuid.uuid4())
                
                # 解密身分證號（這裡假設是明文，實際可能需要解密）
                id_number = row[2] if row[2] else f"ID{i+1:06d}"
                
                # 插入新記錄
                conn.execute(text("""
                    INSERT INTO payee_data (
                        id, name, id_number, phone, email, address,
                        bank_name, bank_code, account_number, account_name,
                        department, notes, created_by, updated_by, is_active,
                        created_at, updated_at
                    ) VALUES (
                        :id, :name, :id_number, :phone, :email, :address,
                        :bank_name, :bank_code, :account_number, :account_name,
                        :department, :notes, :created_by, :updated_by, :is_active,
                        :created_at, :updated_at
                    )
                """), {
                    'id': new_id,
                    'name': row[1],
                    'id_number': id_number,
                    'phone': f'09{i+1:08d}',  # 生成假電話
                    'email': f'{row[1].lower()}@example.com' if row[1] else None,
                    'address': row[3],
                    'bank_name': '台灣銀行',  # 預設銀行
                    'bank_code': '004',
                    'account_number': f'*********{i+1:04d}',  # 生成假帳號
                    'account_name': row[1],
                    'department': '資訊部',  # 預設部門
                    'notes': row[4],
                    'created_by': admin_id,
                    'updated_by': admin_id,
                    'is_active': row[7],
                    'created_at': row[8] or datetime.utcnow(),
                    'updated_at': row[9] or datetime.utcnow()
                })
            
            print(f"   成功遷移 {len(old_data)} 筆記錄")
            
            # 6. 提交事務
            trans.commit()
            print("6. 遷移完成！")
            
            # 7. 驗證結果
            result = conn.execute(text("SELECT COUNT(*) FROM payee_data"))
            new_count = result.fetchone()[0]
            print(f"7. 驗證：新表有 {new_count} 筆記錄")
            
        except Exception as e:
            print(f"遷移失敗: {e}")
            trans.rollback()
            raise

def verify_migration():
    """驗證遷移結果"""
    print("\n驗證遷移結果...")
    
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        payees = db.query(PayeeData).limit(3).all()
        print(f"查詢到 {len(payees)} 筆受款人記錄:")
        
        for payee in payees:
            print(f"  - {payee.name} ({payee.id_number}) - {payee.phone}")
            print(f"    銀行: {payee.bank_name} {payee.account_number}")
            print(f"    部門: {payee.department}")
            print()
            
    except Exception as e:
        print(f"驗證失敗: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    migrate_payee_data()
    verify_migration()

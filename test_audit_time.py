#!/usr/bin/env python3
"""
測試審計日誌時間轉換
"""

import sys
import pytz
from datetime import datetime

def test_audit_time_conversion():
    """測試審計日誌時間轉換"""
    print("🕒 測試審計日誌時間轉換...")
    
    # 模擬資料庫中的時間（UTC）
    db_timestamp_str = "2025-07-14 07:46:27"  # 資料庫中的UTC時間
    expected_taipei_time = "2025-07-14 15:46:27"  # 期望的台北時間
    
    try:
        taipei_tz = pytz.timezone('Asia/Taipei')
        
        # 方法1：假設資料庫時間是UTC（無時區資訊）
        timestamp = datetime.strptime(db_timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"✅ 資料庫時間（UTC）: {db_timestamp_str}")
        print(f"✅ 轉換後台北時間: {formatted_time}")
        print(f"✅ 期望台北時間: {expected_taipei_time}")
        
        if formatted_time == expected_taipei_time:
            print("✅ 時間轉換正確！")
            return True
        else:
            print("❌ 時間轉換錯誤！")
            return False
            
    except Exception as e:
        print(f"❌ 時間轉換測試失敗: {str(e)}")
        return False

def test_different_time_formats():
    """測試不同時間格式的轉換"""
    print("\n📅 測試不同時間格式...")
    
    test_cases = [
        {
            "input": "2025-07-14 07:46:27",
            "format": "naive_utc",
            "expected": "2025-07-14 15:46:27"
        },
        {
            "input": "2025-07-14T07:46:27Z",
            "format": "iso_utc",
            "expected": "2025-07-14 15:46:27"
        },
        {
            "input": "2025-07-14T07:46:27+00:00",
            "format": "iso_with_tz",
            "expected": "2025-07-14 15:46:27"
        }
    ]
    
    taipei_tz = pytz.timezone('Asia/Taipei')
    all_passed = True
    
    for case in test_cases:
        try:
            input_str = case["input"]
            
            if case["format"] == "naive_utc":
                # 假設是UTC時間，無時區資訊
                timestamp = datetime.strptime(input_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
            elif case["format"] == "iso_utc":
                # ISO格式，Z表示UTC
                timestamp = datetime.fromisoformat(input_str.replace('Z', '+00:00')).astimezone(taipei_tz)
            elif case["format"] == "iso_with_tz":
                # ISO格式，包含時區資訊
                timestamp = datetime.fromisoformat(input_str).astimezone(taipei_tz)
            
            formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            
            if formatted_time == case["expected"]:
                print(f"✅ {case['format']}: {input_str} -> {formatted_time}")
            else:
                print(f"❌ {case['format']}: {input_str} -> {formatted_time} (期望: {case['expected']})")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {case['format']}: {input_str} -> 轉換失敗: {str(e)}")
            all_passed = False
    
    return all_passed

def test_current_frontend_logic():
    """測試當前前端邏輯"""
    print("\n🔍 測試當前前端邏輯...")
    
    # 模擬API返回的資料
    mock_log = {
        "timestamp": "2025-07-14 07:46:27",  # 假設API返回這種格式
        "username": "系統管理者",
        "action": "LOGIN_SUCCESS"
    }
    
    try:
        taipei_tz = pytz.timezone('Asia/Taipei')
        
        # 模擬前端的轉換邏輯
        timestamp_str = mock_log.get('timestamp', '')
        formatted_time = ""
        
        if timestamp_str:
            try:
                if 'T' in timestamp_str:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).astimezone(taipei_tz)
                else:
                    # 這裡是關鍵：假設資料庫時間是UTC
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC).astimezone(taipei_tz)
                formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                formatted_time = timestamp_str
                print(f"⚠️ 轉換異常: {str(e)}")
        
        print(f"✅ 原始時間: {timestamp_str}")
        print(f"✅ 轉換後時間: {formatted_time}")
        
        # 檢查是否正確轉換為台北時間
        if formatted_time == "2025-07-14 15:46:27":
            print("✅ 前端邏輯正確！")
            return True
        else:
            print("❌ 前端邏輯需要修正！")
            return False
            
    except Exception as e:
        print(f"❌ 前端邏輯測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始測試審計日誌時間轉換...\n")
    
    results = []
    
    # 執行各項測試
    results.append(test_audit_time_conversion())
    results.append(test_different_time_formats())
    results.append(test_current_frontend_logic())
    
    # 總結測試結果
    print(f"\n📊 測試結果總結:")
    print(f"✅ 通過測試: {sum(results)}")
    print(f"❌ 失敗測試: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有審計日誌時間轉換測試通過！")
        print("💡 建議：確保後端API返回的時間格式一致，前端邏輯應該能正確處理。")
        return 0
    else:
        print("\n⚠️ 部分測試失敗，需要檢查時間轉換邏輯")
        return 1

if __name__ == "__main__":
    sys.exit(main())
